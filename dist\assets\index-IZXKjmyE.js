const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./web-MUvptLmm.js","./mui-oD-jvHl6.js","./vendor-B_Ch-B_d.js","./utils-Ch7HAeVX.js","./charts-UhR5A4U7.js","./web-BGthZxH_.js"])))=>i.map(i=>d[i]);
var e,t=Object.defineProperty,n=(e,n,i)=>((e,n,i)=>n in e?t(e,n,{enumerable:!0,configurable:!0,writable:!0,value:i}):e[n]=i)(e,"symbol"!=typeof n?n+"":n,i);import{j as i,B as r,A as a,T as s,a as o,b as l,P as c,c as d,d as u,D as m,e as h,L as p,f as x,g,h as y,i as j,I as D,k as C,l as v,M as S,m as b,R as f,n as w,o as E,S as P,p as F,q as U,r as T,N as k,s as A,t as R,C as M,u as I,v as N,w as L,x as O,y as V,z as q,E as B,F as _,G as z,W as $,H as W,J as X,K as Q,O as J,Q as H,U as G,V as Y,X as K,Y as Z,Z as ee,_ as te,$ as ne,a0 as ie,a1 as re,a2 as ae,a3 as se,a4 as oe,a5 as le,a6 as ce,a7 as de,a8 as ue,a9 as me,aa as he,ab as pe,ac as xe,ad as ge,ae as ye,af as je,ag as De,ah as Ce,ai as ve,aj as Se,ak as be,al as fe,am as we,an as Ee,ao as Pe,ap as Fe,aq as Ue,ar as Te,as as ke,at as Ae,au as Re,av as Me,aw as Ie,ax as Ne,ay as Le,az as Oe,aA as Ve,aB as qe,aC as Be,aD as _e,aE as ze,aF as $e,aG as We,aH as Xe,aI as Qe,aJ as Je,aK as He,aL as Ge,aM as Ye,aN as Ke,aO as Ze,aP as et,aQ as tt,aR as nt,aS as it,aT as rt,aU as at,aV as st,aW as ot,aX as lt,aY as ct,aZ as dt,a_ as ut,a$ as mt,b0 as ht,b1 as pt,b2 as xt,b3 as gt,b4 as yt,b5 as jt,b6 as Dt}from"./mui-oD-jvHl6.js";import{c as Ct,r as vt,R as St,a as bt}from"./vendor-B_Ch-B_d.js";import{b as ft,a as wt,c as Et,d as Pt,f as Ft,s as Ut,e as Tt,g as kt,i as At,h as Rt,j as Mt,k as It,l as Nt,m as Lt,n as Ot,o as Vt,p as qt,q as Bt}from"./utils-Ch7HAeVX.js";import{L as _t,D as zt,B as $t,C as Wt,a as Xt,b as Qt,P as Jt,c as Ht,d as Gt,p as Yt,e as Kt,f as Zt,A as en}from"./charts-UhR5A4U7.js";!function(){const e=document.createElement("link").relList;if(!(e&&e.supports&&e.supports("modulepreload"))){for(const e of document.querySelectorAll('link[rel="modulepreload"]'))t(e);new MutationObserver(e=>{for(const n of e)if("childList"===n.type)for(const e of n.addedNodes)"LINK"===e.tagName&&"modulepreload"===e.rel&&t(e)}).observe(document,{childList:!0,subtree:!0})}function t(e){if(e.ep)return;e.ep=!0;const t=function(e){const t={};return e.integrity&&(t.integrity=e.integrity),e.referrerPolicy&&(t.referrerPolicy=e.referrerPolicy),"use-credentials"===e.crossOrigin?t.credentials="include":"anonymous"===e.crossOrigin?t.credentials="omit":t.credentials="same-origin",t}(e);fetch(e.href,t)}}();var tn,nn,rn={},an=Ct;
/**
 * @remix-run/router v1.23.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */
function sn(){return sn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e},sn.apply(this,arguments)}rn.createRoot=an.createRoot,rn.hydrateRoot=an.hydrateRoot,(nn=tn||(tn={})).Pop="POP",nn.Push="PUSH",nn.Replace="REPLACE";const on="popstate";function ln(e){return void 0===e&&(e={}),function(e,t,n,i){void 0===i&&(i={});let{window:r=document.defaultView,v5Compat:a=!1}=i,s=r.history,o=tn.Pop,l=null,c=d();null==c&&(c=0,s.replaceState(sn({},s.state,{idx:c}),""));function d(){return(s.state||{idx:null}).idx}function u(){o=tn.Pop;let e=d(),t=null==e?null:e-c;c=e,l&&l({action:o,location:x.location,delta:t})}function m(e,t){o=tn.Push;let i=mn(x.location,e,t);n&&n(i,e),c=d()+1;let u=un(i,c),m=x.createHref(i);try{s.pushState(u,"",m)}catch(h){if(h instanceof DOMException&&"DataCloneError"===h.name)throw h;r.location.assign(m)}a&&l&&l({action:o,location:x.location,delta:1})}function h(e,t){o=tn.Replace;let i=mn(x.location,e,t);n&&n(i,e),c=d();let r=un(i,c),u=x.createHref(i);s.replaceState(r,"",u),a&&l&&l({action:o,location:x.location,delta:0})}function p(e){let t="null"!==r.location.origin?r.location.origin:r.location.href,n="string"==typeof e?e:hn(e);return n=n.replace(/ $/,"%20"),cn(t,"No window.location.(origin|href) available to create URL for href: "+n),new URL(n,t)}let x={get action(){return o},get location(){return e(r,s)},listen(e){if(l)throw new Error("A history only accepts one active listener");return r.addEventListener(on,u),l=e,()=>{r.removeEventListener(on,u),l=null}},createHref:e=>t(r,e),createURL:p,encodeLocation(e){let t=p(e);return{pathname:t.pathname,search:t.search,hash:t.hash}},push:m,replace:h,go:e=>s.go(e)};return x}(function(e,t){let{pathname:n="/",search:i="",hash:r=""}=pn(e.location.hash.substr(1));return n.startsWith("/")||n.startsWith(".")||(n="/"+n),mn("",{pathname:n,search:i,hash:r},t.state&&t.state.usr||null,t.state&&t.state.key||"default")},function(e,t){let n=e.document.querySelector("base"),i="";if(n&&n.getAttribute("href")){let t=e.location.href,n=t.indexOf("#");i=-1===n?t:t.slice(0,n)}return i+"#"+("string"==typeof t?t:hn(t))},function(e,t){dn("/"===e.pathname.charAt(0),"relative pathnames are not supported in hash history.push("+JSON.stringify(t)+")")},e)}function cn(e,t){if(!1===e||null==e)throw new Error(t)}function dn(e,t){if(!e){"undefined"!=typeof console&&console.warn(t);try{throw new Error(t)}catch(n){}}}function un(e,t){return{usr:e.state,key:e.key,idx:t}}function mn(e,t,n,i){return void 0===n&&(n=null),sn({pathname:"string"==typeof e?e:e.pathname,search:"",hash:""},"string"==typeof t?pn(t):t,{state:n,key:t&&t.key||i||Math.random().toString(36).substr(2,8)})}function hn(e){let{pathname:t="/",search:n="",hash:i=""}=e;return n&&"?"!==n&&(t+="?"===n.charAt(0)?n:"?"+n),i&&"#"!==i&&(t+="#"===i.charAt(0)?i:"#"+i),t}function pn(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substr(n),e=e.substr(0,n));let i=e.indexOf("?");i>=0&&(t.search=e.substr(i),e=e.substr(0,i)),e&&(t.pathname=e)}return t}var xn,gn;function yn(e,t,n){return void 0===n&&(n="/"),function(e,t,n){let i="string"==typeof t?pn(t):t,r=kn(i.pathname||"/",n);if(null==r)return null;let a=jn(e);!function(e){e.sort((e,t)=>e.score!==t.score?t.score-e.score:function(e,t){let n=e.length===t.length&&e.slice(0,-1).every((e,n)=>e===t[n]);return n?e[e.length-1]-t[t.length-1]:0}(e.routesMeta.map(e=>e.childrenIndex),t.routesMeta.map(e=>e.childrenIndex)))}(a);let s=null;for(let o=0;null==s&&o<a.length;++o){let e=Tn(r);s=Fn(a[o],e)}return s}(e,t,n)}function jn(e,t,n,i){void 0===t&&(t=[]),void 0===n&&(n=[]),void 0===i&&(i="");let r=(e,r,a)=>{let s={relativePath:void 0===a?e.path||"":a,caseSensitive:!0===e.caseSensitive,childrenIndex:r,route:e};s.relativePath.startsWith("/")&&(cn(s.relativePath.startsWith(i),'Absolute route path "'+s.relativePath+'" nested under path "'+i+'" is not valid. An absolute child route path must start with the combined path of all its parent routes.'),s.relativePath=s.relativePath.slice(i.length));let o=In([i,s.relativePath]),l=n.concat(s);e.children&&e.children.length>0&&(cn(!0!==e.index,'Index routes must not have child routes. Please remove all child routes from route path "'+o+'".'),jn(e.children,t,l,o)),(null!=e.path||e.index)&&t.push({path:o,score:Pn(o,e.index),routesMeta:l})};return e.forEach((e,t)=>{var n;if(""!==e.path&&null!=(n=e.path)&&n.includes("?"))for(let i of Dn(e.path))r(e,t,i);else r(e,t)}),t}function Dn(e){let t=e.split("/");if(0===t.length)return[];let[n,...i]=t,r=n.endsWith("?"),a=n.replace(/\?$/,"");if(0===i.length)return r?[a,""]:[a];let s=Dn(i.join("/")),o=[];return o.push(...s.map(e=>""===e?a:[a,e].join("/"))),r&&o.push(...s),o.map(t=>e.startsWith("/")&&""===t?"/":t)}(gn=xn||(xn={})).data="data",gn.deferred="deferred",gn.redirect="redirect",gn.error="error";const Cn=/^:[\w-]+$/,vn=3,Sn=2,bn=1,fn=10,wn=-2,En=e=>"*"===e;function Pn(e,t){let n=e.split("/"),i=n.length;return n.some(En)&&(i+=wn),t&&(i+=Sn),n.filter(e=>!En(e)).reduce((e,t)=>e+(Cn.test(t)?vn:""===t?bn:fn),i)}function Fn(e,t,n){let{routesMeta:i}=e,r={},a="/",s=[];for(let o=0;o<i.length;++o){let e=i[o],n=o===i.length-1,l="/"===a?t:t.slice(a.length)||"/",c=Un({path:e.relativePath,caseSensitive:e.caseSensitive,end:n},l),d=e.route;if(!c)return null;Object.assign(r,c.params),s.push({params:r,pathname:In([a,c.pathname]),pathnameBase:Nn(In([a,c.pathnameBase])),route:d}),"/"!==c.pathnameBase&&(a=In([a,c.pathnameBase]))}return s}function Un(e,t){"string"==typeof e&&(e={path:e,caseSensitive:!1,end:!0});let[n,i]=function(e,t,n){void 0===t&&(t=!1);void 0===n&&(n=!0);dn("*"===e||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were "'+e.replace(/\*$/,"/*")+'" because the `*` character must always follow a `/` in the pattern. To get rid of this warning, please change the route path to "'+e.replace(/\*$/,"/*")+'".');let i=[],r="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(e,t,n)=>(i.push({paramName:t,isOptional:null!=n}),n?"/?([^\\/]+)?":"/([^\\/]+)"));e.endsWith("*")?(i.push({paramName:"*"}),r+="*"===e||"/*"===e?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?r+="\\/*$":""!==e&&"/"!==e&&(r+="(?:(?=\\/|$))");let a=new RegExp(r,t?void 0:"i");return[a,i]}(e.path,e.caseSensitive,e.end),r=t.match(n);if(!r)return null;let a=r[0],s=a.replace(/(.)\/+$/,"$1"),o=r.slice(1);return{params:i.reduce((e,t,n)=>{let{paramName:i,isOptional:r}=t;if("*"===i){let e=o[n]||"";s=a.slice(0,a.length-e.length).replace(/(.)\/+$/,"$1")}const l=o[n];return e[i]=r&&!l?void 0:(l||"").replace(/%2F/g,"/"),e},{}),pathname:a,pathnameBase:s,pattern:e}}function Tn(e){try{return e.split("/").map(e=>decodeURIComponent(e).replace(/\//g,"%2F")).join("/")}catch(t){return dn(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent encoding ('+t+")."),e}}function kn(e,t){if("/"===t)return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,i=e.charAt(n);return i&&"/"!==i?null:e.slice(n)||"/"}function An(e,t,n,i){return"Cannot include a '"+e+"' character in a manually specified `to."+t+"` field ["+JSON.stringify(i)+"].  Please separate it out to the `to."+n+'` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.'}function Rn(e,t){let n=function(e){return e.filter((e,t)=>0===t||e.route.path&&e.route.path.length>0)}(e);return t?n.map((e,t)=>t===n.length-1?e.pathname:e.pathnameBase):n.map(e=>e.pathnameBase)}function Mn(e,t,n,i){let r;void 0===i&&(i=!1),"string"==typeof e?r=pn(e):(r=sn({},e),cn(!r.pathname||!r.pathname.includes("?"),An("?","pathname","search",r)),cn(!r.pathname||!r.pathname.includes("#"),An("#","pathname","hash",r)),cn(!r.search||!r.search.includes("#"),An("#","search","hash",r)));let a,s=""===e||""===r.pathname,o=s?"/":r.pathname;if(null==o)a=n;else{let e=t.length-1;if(!i&&o.startsWith("..")){let t=o.split("/");for(;".."===t[0];)t.shift(),e-=1;r.pathname=t.join("/")}a=e>=0?t[e]:"/"}let l=function(e,t){void 0===t&&(t="/");let{pathname:n,search:i="",hash:r=""}="string"==typeof e?pn(e):e,a=n?n.startsWith("/")?n:function(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(e=>{".."===e?n.length>1&&n.pop():"."!==e&&n.push(e)}),n.length>1?n.join("/"):"/"}(n,t):t;return{pathname:a,search:Ln(i),hash:On(r)}}(r,a),c=o&&"/"!==o&&o.endsWith("/"),d=(s||"."===o)&&n.endsWith("/");return l.pathname.endsWith("/")||!c&&!d||(l.pathname+="/"),l}const In=e=>e.join("/").replace(/\/\/+/g,"/"),Nn=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),Ln=e=>e&&"?"!==e?e.startsWith("?")?e:"?"+e:"",On=e=>e&&"#"!==e?e.startsWith("#")?e:"#"+e:"";const Vn=["post","put","patch","delete"];new Set(Vn);const qn=["get",...Vn];
/**
 * React Router v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */
function Bn(){return Bn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e},Bn.apply(this,arguments)}new Set(qn);const _n=vt.createContext(null),zn=vt.createContext(null),$n=vt.createContext(null),Wn=vt.createContext(null),Xn=vt.createContext({outlet:null,matches:[],isDataRoute:!1}),Qn=vt.createContext(null);function Jn(){return null!=vt.useContext(Wn)}function Hn(){return Jn()||cn(!1),vt.useContext(Wn).location}function Gn(e){vt.useContext($n).static||vt.useLayoutEffect(e)}function Yn(){let{isDataRoute:e}=vt.useContext(Xn);return e?function(){let{router:e}=function(){let e=vt.useContext(_n);return e||cn(!1),e}(ri.UseNavigateStable),t=si(ai.UseNavigateStable),n=vt.useRef(!1);return Gn(()=>{n.current=!0}),vt.useCallback(function(i,r){void 0===r&&(r={}),n.current&&("number"==typeof i?e.navigate(i):e.navigate(i,Bn({fromRouteId:t},r)))},[e,t])}():function(){Jn()||cn(!1);let e=vt.useContext(_n),{basename:t,future:n,navigator:i}=vt.useContext($n),{matches:r}=vt.useContext(Xn),{pathname:a}=Hn(),s=JSON.stringify(Rn(r,n.v7_relativeSplatPath)),o=vt.useRef(!1);return Gn(()=>{o.current=!0}),vt.useCallback(function(n,r){if(void 0===r&&(r={}),!o.current)return;if("number"==typeof n)return void i.go(n);let l=Mn(n,JSON.parse(s),a,"path"===r.relative);null==e&&"/"!==t&&(l.pathname="/"===l.pathname?t:In([t,l.pathname])),(r.replace?i.replace:i.push)(l,r.state,r)},[t,i,s,a,e])}()}const Kn=vt.createContext(null);function Zn(e,t){return function(e,t,n,i){Jn()||cn(!1);let{navigator:r}=vt.useContext($n),{matches:a}=vt.useContext(Xn),s=a[a.length-1],o=s?s.params:{};!s||s.pathname;let l=s?s.pathnameBase:"/";s&&s.route;let c,d=Hn();if(t){var u;let e="string"==typeof t?pn(t):t;"/"===l||(null==(u=e.pathname)?void 0:u.startsWith(l))||cn(!1),c=e}else c=d;let m=c.pathname||"/",h=m;if("/"!==l){let e=l.replace(/^\//,"").split("/");h="/"+m.replace(/^\//,"").split("/").slice(e.length).join("/")}let p=yn(e,{pathname:h}),x=function(e,t,n,i){var r;void 0===t&&(t=[]);void 0===n&&(n=null);void 0===i&&(i=null);if(null==e){var a;if(!n)return null;if(n.errors)e=n.matches;else{if(!(null!=(a=i)&&a.v7_partialHydration&&0===t.length&&!n.initialized&&n.matches.length>0))return null;e=n.matches}}let s=e,o=null==(r=n)?void 0:r.errors;if(null!=o){let e=s.findIndex(e=>e.route.id&&void 0!==(null==o?void 0:o[e.route.id]));e>=0||cn(!1),s=s.slice(0,Math.min(s.length,e+1))}let l=!1,c=-1;if(n&&i&&i.v7_partialHydration)for(let d=0;d<s.length;d++){let e=s[d];if((e.route.HydrateFallback||e.route.hydrateFallbackElement)&&(c=d),e.route.id){let{loaderData:t,errors:i}=n,r=e.route.loader&&void 0===t[e.route.id]&&(!i||void 0===i[e.route.id]);if(e.route.lazy||r){l=!0,s=c>=0?s.slice(0,c+1):[s[0]];break}}}return s.reduceRight((e,i,r)=>{let a,d=!1,u=null,m=null;var h;n&&(a=o&&i.route.id?o[i.route.id]:void 0,u=i.route.errorElement||ti,l&&(c<0&&0===r?(oi[h="route-fallback"]||(oi[h]=!0),d=!0,m=null):c===r&&(d=!0,m=i.route.hydrateFallbackElement||null)));let p=t.concat(s.slice(0,r+1)),x=()=>{let t;return t=a?u:d?m:i.route.Component?vt.createElement(i.route.Component,null):i.route.element?i.route.element:e,vt.createElement(ii,{match:i,routeContext:{outlet:e,matches:p,isDataRoute:null!=n},children:t})};return n&&(i.route.ErrorBoundary||i.route.errorElement||0===r)?vt.createElement(ni,{location:n.location,revalidation:n.revalidation,component:u,error:a,children:x(),routeContext:{outlet:null,matches:p,isDataRoute:!0}}):x()},null)}(p&&p.map(e=>Object.assign({},e,{params:Object.assign({},o,e.params),pathname:In([l,r.encodeLocation?r.encodeLocation(e.pathname).pathname:e.pathname]),pathnameBase:"/"===e.pathnameBase?l:In([l,r.encodeLocation?r.encodeLocation(e.pathnameBase).pathname:e.pathnameBase])})),a,n,i);if(t&&x)return vt.createElement(Wn.Provider,{value:{location:Bn({pathname:"/",search:"",hash:"",state:null,key:"default"},c),navigationType:tn.Pop}},x);return x}(e,t)}function ei(){let e=function(){var e;let t=vt.useContext(Qn),n=function(){let e=vt.useContext(zn);return e||cn(!1),e}(),i=si();if(void 0!==t)return t;return null==(e=n.errors)?void 0:e[i]}(),t=function(e){return null!=e&&"number"==typeof e.status&&"string"==typeof e.statusText&&"boolean"==typeof e.internal&&"data"in e}(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,i={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"};return vt.createElement(vt.Fragment,null,vt.createElement("h2",null,"Unexpected Application Error!"),vt.createElement("h3",{style:{fontStyle:"italic"}},t),n?vt.createElement("pre",{style:i},n):null,null)}const ti=vt.createElement(ei,null);class ni extends vt.Component{constructor(e){super(e),this.state={location:e.location,revalidation:e.revalidation,error:e.error}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,t){return t.location!==e.location||"idle"!==t.revalidation&&"idle"===e.revalidation?{error:e.error,location:e.location,revalidation:e.revalidation}:{error:void 0!==e.error?e.error:t.error,location:t.location,revalidation:e.revalidation||t.revalidation}}componentDidCatch(e,t){console.error("React Router caught the following error during render",e,t)}render(){return void 0!==this.state.error?vt.createElement(Xn.Provider,{value:this.props.routeContext},vt.createElement(Qn.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function ii(e){let{routeContext:t,match:n,children:i}=e,r=vt.useContext(_n);return r&&r.static&&r.staticContext&&(n.route.errorElement||n.route.ErrorBoundary)&&(r.staticContext._deepestRenderedBoundaryId=n.route.id),vt.createElement(Xn.Provider,{value:t},i)}var ri=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(ri||{}),ai=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(ai||{});function si(e){let t=function(){let e=vt.useContext(Xn);return e||cn(!1),e}(),n=t.matches[t.matches.length-1];return n.route.id||cn(!1),n.route.id}const oi={};function li(e){let{to:t,replace:n,state:i,relative:r}=e;Jn()||cn(!1);let{future:a,static:s}=vt.useContext($n),{matches:o}=vt.useContext(Xn),{pathname:l}=Hn(),c=Yn(),d=Mn(t,Rn(o,a.v7_relativeSplatPath),l,"path"===r),u=JSON.stringify(d);return vt.useEffect(()=>c(JSON.parse(u),{replace:n,state:i,relative:r}),[c,u,r,n,i]),null}function ci(e){return function(e){let t=vt.useContext(Xn).outlet;return t?vt.createElement(Kn.Provider,{value:e},t):t}(e.context)}function di(e){cn(!1)}function ui(e){let{basename:t="/",children:n=null,location:i,navigationType:r=tn.Pop,navigator:a,static:s=!1,future:o}=e;Jn()&&cn(!1);let l=t.replace(/^\/*/,"/"),c=vt.useMemo(()=>({basename:l,navigator:a,static:s,future:Bn({v7_relativeSplatPath:!1},o)}),[l,o,a,s]);"string"==typeof i&&(i=pn(i));let{pathname:d="/",search:u="",hash:m="",state:h=null,key:p="default"}=i,x=vt.useMemo(()=>{let e=kn(d,l);return null==e?null:{location:{pathname:e,search:u,hash:m,state:h,key:p},navigationType:r}},[l,d,u,m,h,p,r]);return null==x?null:vt.createElement($n.Provider,{value:c},vt.createElement(Wn.Provider,{children:n,value:x}))}function mi(e){let{children:t,location:n}=e;return Zn(hi(t),n)}function hi(e,t){void 0===t&&(t=[]);let n=[];return vt.Children.forEach(e,(e,i)=>{if(!vt.isValidElement(e))return;let r=[...t,i];if(e.type===vt.Fragment)return void n.push.apply(n,hi(e.props.children,r));e.type!==di&&cn(!1),e.props.index&&e.props.children&&cn(!1);let a={id:e.props.id||r.join("-"),caseSensitive:e.props.caseSensitive,element:e.props.element,Component:e.props.Component,index:e.props.index,path:e.props.path,loader:e.props.loader,action:e.props.action,errorElement:e.props.errorElement,ErrorBoundary:e.props.ErrorBoundary,hasErrorBoundary:null!=e.props.ErrorBoundary||null!=e.props.errorElement,shouldRevalidate:e.props.shouldRevalidate,handle:e.props.handle,lazy:e.props.lazy};e.props.children&&(a.children=hi(e.props.children,r)),n.push(a)}),n}
/**
 * React Router DOM v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */new Promise(()=>{});try{window.__reactRouterVersion="6"}catch(Sa){}const pi=St.startTransition;function xi(e){let{basename:t,children:n,future:i,window:r}=e,a=vt.useRef();null==a.current&&(a.current=ln({window:r,v5Compat:!0}));let s=a.current,[o,l]=vt.useState({action:s.action,location:s.location}),{v7_startTransition:c}=i||{},d=vt.useCallback(e=>{c&&pi?pi(()=>l(e)):l(e)},[l,c]);return vt.useLayoutEffect(()=>s.listen(d),[s,d]),vt.useEffect(()=>{return null==(e=i)||e.v7_startTransition,void(null==e||e.v7_relativeSplatPath);var e},[i]),vt.createElement(ui,{basename:t,children:n,location:o.location,navigationType:o.action,navigator:s,future:i})}var gi,yi,ji,Di;(yi=gi||(gi={})).UseScrollRestoration="useScrollRestoration",yi.UseSubmit="useSubmit",yi.UseSubmitFetcher="useSubmitFetcher",yi.UseFetcher="useFetcher",yi.useViewTransitionState="useViewTransitionState",(Di=ji||(ji={})).UseFetcher="useFetcher",Di.UseFetchers="useFetchers",Di.UseScrollRestoration="useScrollRestoration";const Ci={components:{MuiBreadcrumbs:{defaultProps:{expandText:"Montrer le chemin"}},MuiTablePagination:{defaultProps:{getItemAriaLabel:e=>"first"===e?"Aller à la première page":"last"===e?"Aller à la dernière page":"next"===e?"Aller à la page suivante":"Aller à la page précédente",labelRowsPerPage:"Lignes par page :",labelDisplayedRows:({from:e,to:t,count:n})=>`${e}–${t} sur ${-1!==n?n:`plus que ${t}`}`}},MuiRating:{defaultProps:{getLabelText:e=>`${e} Etoile${1!==e?"s":""}`,emptyLabelText:"Vide"}},MuiAutocomplete:{defaultProps:{clearText:"Vider",closeText:"Fermer",loadingText:"Chargement…",noOptionsText:"Pas de résultats",openText:"Ouvrir"}},MuiAlert:{defaultProps:{closeText:"Fermer"}},MuiPagination:{defaultProps:{"aria-label":"navigation de pagination",getItemAriaLabel:(e,t,n)=>"page"===e?`${n?"":"Aller à la "}page ${t}`:"first"===e?"Aller à la première page":"last"===e?"Aller à la dernière page":"next"===e?"Aller à la page suivante":"Aller à la page précédente"}}}},vi={},Si=function(e,t,n){let i=Promise.resolve();if(t&&t.length>0){const e=document.getElementsByTagName("link"),r=document.querySelector("meta[property=csp-nonce]"),a=(null==r?void 0:r.nonce)||(null==r?void 0:r.getAttribute("nonce"));i=Promise.allSettled(t.map(t=>{if(t=function(e,t){return new URL(e,t).href}(t,n),t in vi)return;vi[t]=!0;const i=t.endsWith(".css"),r=i?'[rel="stylesheet"]':"";if(!!n)for(let n=e.length-1;n>=0;n--){const r=e[n];if(r.href===t&&(!i||"stylesheet"===r.rel))return}else if(document.querySelector(`link[href="${t}"]${r}`))return;const s=document.createElement("link");return s.rel=i?"stylesheet":"modulepreload",i||(s.as="script"),s.crossOrigin="",s.href=t,a&&s.setAttribute("nonce",a),document.head.appendChild(s),i?new Promise((e,n)=>{s.addEventListener("load",e),s.addEventListener("error",()=>n(new Error(`Unable to preload CSS for ${t}`)))}):void 0}))}function r(e){const t=new Event("vite:preloadError",{cancelable:!0});if(t.payload=e,window.dispatchEvent(t),!t.defaultPrevented)throw e}return i.then(t=>{for(const e of t||[])"rejected"===e.status&&r(e.reason);return e().catch(r)})};
/*! Capacitor: https://capacitorjs.com/ - MIT License */
var bi,fi;(fi=bi||(bi={})).Unimplemented="UNIMPLEMENTED",fi.Unavailable="UNAVAILABLE";class wi extends Error{constructor(e,t,n){super(e),this.message=e,this.code=t,this.data=n}}const Ei=e=>{const t=e.CapacitorCustomPlatform||null,n=e.Capacitor||{},i=n.Plugins=n.Plugins||{},r=()=>null!==t?t.name:(e=>{var t,n;return(null==e?void 0:e.androidBridge)?"android":(null===(n=null===(t=null==e?void 0:e.webkit)||void 0===t?void 0:t.messageHandlers)||void 0===n?void 0:n.bridge)?"ios":"web"})(e),a=e=>{var t;return null===(t=n.PluginHeaders)||void 0===t?void 0:t.find(t=>t.name===e)},s=new Map;return n.convertFileSrc||(n.convertFileSrc=e=>e),n.getPlatform=r,n.handleError=t=>e.console.error(t),n.isNativePlatform=()=>"web"!==r(),n.isPluginAvailable=e=>{const t=s.get(e);return!!(null==t?void 0:t.platforms.has(r()))||!!a(e)},n.registerPlugin=(e,o={})=>{const l=s.get(e);if(l)return console.warn(`Capacitor plugin "${e}" already registered. Cannot register plugins twice.`),l.proxy;const c=r(),d=a(e);let u;const m=i=>{let r;const a=(...a)=>{const s=(async()=>(!u&&c in o?u=u="function"==typeof o[c]?await o[c]():o[c]:null!==t&&!u&&"web"in o&&(u=u="function"==typeof o.web?await o.web():o.web),u))().then(t=>{const s=((t,i)=>{var r,a;if(!d){if(t)return null===(a=t[i])||void 0===a?void 0:a.bind(t);throw new wi(`"${e}" plugin is not implemented on ${c}`,bi.Unimplemented)}{const a=null==d?void 0:d.methods.find(e=>i===e.name);if(a)return"promise"===a.rtype?t=>n.nativePromise(e,i.toString(),t):(t,r)=>n.nativeCallback(e,i.toString(),t,r);if(t)return null===(r=t[i])||void 0===r?void 0:r.bind(t)}})(t,i);if(s){const e=s(...a);return r=null==e?void 0:e.remove,e}throw new wi(`"${e}.${i}()" is not implemented on ${c}`,bi.Unimplemented)});return"addListener"===i&&(s.remove=async()=>r()),s};return a.toString=()=>`${i.toString()}() { [capacitor code] }`,Object.defineProperty(a,"name",{value:i,writable:!1,configurable:!1}),a},h=m("addListener"),p=m("removeListener"),x=(e,t)=>{const n=h({eventName:e},t),i=async()=>{const i=await n;p({eventName:e,callbackId:i},t)},r=new Promise(e=>n.then(()=>e({remove:i})));return r.remove=async()=>{console.warn("Using addListener() without 'await' is deprecated."),await i()},r},g=new Proxy({},{get(e,t){switch(t){case"$$typeof":return;case"toJSON":return()=>({});case"addListener":return d?x:h;case"removeListener":return p;default:return m(t)}}});return i[e]=g,s.set(e,{name:e,proxy:g,platforms:new Set([...Object.keys(o),...d?[c]:[]])}),g},n.Exception=wi,n.DEBUG=!!n.DEBUG,n.isLoggingEnabled=!!n.isLoggingEnabled,n},Pi=(e=>e.Capacitor=Ei(e))("undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:"undefined"!=typeof global?global:{}),Fi=Pi.registerPlugin;class Ui{constructor(){this.listeners={},this.retainedEventArguments={},this.windowListeners={}}addListener(e,t){let n=!1;this.listeners[e]||(this.listeners[e]=[],n=!0),this.listeners[e].push(t);const i=this.windowListeners[e];i&&!i.registered&&this.addWindowListener(i),n&&this.sendRetainedArgumentsForEvent(e);return Promise.resolve({remove:async()=>this.removeListener(e,t)})}async removeAllListeners(){this.listeners={};for(const e in this.windowListeners)this.removeWindowListener(this.windowListeners[e]);this.windowListeners={}}notifyListeners(e,t,n){const i=this.listeners[e];if(i)i.forEach(e=>e(t));else if(n){let n=this.retainedEventArguments[e];n||(n=[]),n.push(t),this.retainedEventArguments[e]=n}}hasListeners(e){var t;return!!(null===(t=this.listeners[e])||void 0===t?void 0:t.length)}registerWindowListener(e,t){this.windowListeners[t]={registered:!1,windowEventName:e,pluginEventName:t,handler:e=>{this.notifyListeners(t,e)}}}unimplemented(e="not implemented"){return new Pi.Exception(e,bi.Unimplemented)}unavailable(e="not available"){return new Pi.Exception(e,bi.Unavailable)}async removeListener(e,t){const n=this.listeners[e];if(!n)return;const i=n.indexOf(t);this.listeners[e].splice(i,1),this.listeners[e].length||this.removeWindowListener(this.windowListeners[e])}addWindowListener(e){window.addEventListener(e.windowEventName,e.handler),e.registered=!0}removeWindowListener(e){e&&(window.removeEventListener(e.windowEventName,e.handler),e.registered=!1)}sendRetainedArgumentsForEvent(e){const t=this.retainedEventArguments[e];t&&(delete this.retainedEventArguments[e],t.forEach(t=>{this.notifyListeners(e,t)}))}}const Ti=e=>encodeURIComponent(e).replace(/%(2[346B]|5E|60|7C)/g,decodeURIComponent).replace(/[()]/g,escape),ki=e=>e.replace(/(%[\dA-F]{2})+/gi,decodeURIComponent);class Ai extends Ui{async getCookies(){const e=document.cookie,t={};return e.split(";").forEach(e=>{if(e.length<=0)return;let[n,i]=e.replace(/=/,"CAP_COOKIE").split("CAP_COOKIE");n=ki(n).trim(),i=ki(i).trim(),t[n]=i}),t}async setCookie(e){try{const t=Ti(e.key),n=Ti(e.value),i=`; expires=${(e.expires||"").replace("expires=","")}`,r=(e.path||"/").replace("path=",""),a=null!=e.url&&e.url.length>0?`domain=${e.url}`:"";document.cookie=`${t}=${n||""}${i}; path=${r}; ${a};`}catch(t){return Promise.reject(t)}}async deleteCookie(e){try{document.cookie=`${e.key}=; Max-Age=0`}catch(t){return Promise.reject(t)}}async clearCookies(){try{const e=document.cookie.split(";")||[];for(const t of e)document.cookie=t.replace(/^ +/,"").replace(/=.*/,`=;expires=${(new Date).toUTCString()};path=/`)}catch(e){return Promise.reject(e)}}async clearAllCookies(){try{await this.clearCookies()}catch(e){return Promise.reject(e)}}}Fi("CapacitorCookies",{web:()=>new Ai});const Ri=(e,t={})=>{const n=Object.assign({method:e.method||"GET",headers:e.headers},t),i=((e={})=>{const t=Object.keys(e);return Object.keys(e).map(e=>e.toLocaleLowerCase()).reduce((n,i,r)=>(n[i]=e[t[r]],n),{})})(e.headers)["content-type"]||"";if("string"==typeof e.data)n.body=e.data;else if(i.includes("application/x-www-form-urlencoded")){const t=new URLSearchParams;for(const[n,i]of Object.entries(e.data||{}))t.set(n,i);n.body=t.toString()}else if(i.includes("multipart/form-data")||e.data instanceof FormData){const t=new FormData;if(e.data instanceof FormData)e.data.forEach((e,n)=>{t.append(n,e)});else for(const n of Object.keys(e.data))t.append(n,e.data[n]);n.body=t;const i=new Headers(n.headers);i.delete("content-type"),n.headers=i}else(i.includes("application/json")||"object"==typeof e.data)&&(n.body=JSON.stringify(e.data));return n};class Mi extends Ui{async request(e){const t=Ri(e,e.webFetchExtra),n=((e,t=!0)=>e?Object.entries(e).reduce((e,n)=>{const[i,r]=n;let a,s;return Array.isArray(r)?(s="",r.forEach(e=>{a=t?encodeURIComponent(e):e,s+=`${i}=${a}&`}),s.slice(0,-1)):(a=t?encodeURIComponent(r):r,s=`${i}=${a}`),`${e}&${s}`},"").substr(1):null)(e.params,e.shouldEncodeUrlParams),i=n?`${e.url}?${n}`:e.url,r=await fetch(i,t),a=r.headers.get("content-type")||"";let s,o,{responseType:l="text"}=r.ok?e:{};switch(a.includes("application/json")&&(l="json"),l){case"arraybuffer":case"blob":o=await r.blob(),s=await(async e=>new Promise((t,n)=>{const i=new FileReader;i.onload=()=>{const e=i.result;t(e.indexOf(",")>=0?e.split(",")[1]:e)},i.onerror=e=>n(e),i.readAsDataURL(e)}))(o);break;case"json":s=await r.json();break;default:s=await r.text()}const c={};return r.headers.forEach((e,t)=>{c[t]=e}),{data:s,headers:c,status:r.status,url:r.url}}async get(e){return this.request(Object.assign(Object.assign({},e),{method:"GET"}))}async post(e){return this.request(Object.assign(Object.assign({},e),{method:"POST"}))}async put(e){return this.request(Object.assign(Object.assign({},e),{method:"PUT"}))}async patch(e){return this.request(Object.assign(Object.assign({},e),{method:"PATCH"}))}async delete(e){return this.request(Object.assign(Object.assign({},e),{method:"DELETE"}))}}Fi("CapacitorHttp",{web:()=>new Mi});const Ii=()=>{const e=Pi.getPlatform();return{isMobile:Pi.isNativePlatform(),isDesktop:!Pi.isNativePlatform(),isAndroid:"android"===e,isIOS:"ios"===e,isWeb:"web"===e,platform:e}},Ni=()=>Pi.isNativePlatform();class Li{static arrayToCSV(e,t,n=!0){if(!e||0===e.length){const e=t.map(e=>e.header).join(",")+"\n";return n?"\ufeff"+e:e}const i=[t.map(e=>e.header).join(","),...e.map(e=>t.map(t=>{let n=e[t.key];return null==n?n="":"date"===t.type&&n?n=new Date(n).toISOString().split("T")[0]:"boolean"===t.type?n=n?"Oui":"Non":"object"==typeof n&&(n=Li.formatComplexValue(n,t.key)),n=String(n),(n.includes(",")||n.includes('"')||n.includes("\n"))&&(n='"'+n.replace(/"/g,'""')+'"'),n}).join(","))].join("\n");return n?"\ufeff"+i:i}static formatComplexValue(e,t){if(!e)return"";if("paiements"===t&&Array.isArray(e))return 0===e.length?"Aucun paiement":e.map((e,t)=>{const n=[];if(n.push(`Paiement ${t+1}:`),e.montantCDF&&n.push(`${e.montantCDF.toLocaleString("fr-FR")} CDF`),e.montantUSD&&n.push(`${e.montantUSD.toLocaleString("fr-FR",{minimumFractionDigits:2})} USD`),e.methodePaiement){const t={cash:"Comptant",mobile_money:"Mobile Money",bank:"Banque",card:"Carte"};n.push(`via ${t[e.methodePaiement]||e.methodePaiement}`)}if(e.datePaiement){const t=new Date(e.datePaiement);n.push(`le ${t.toLocaleDateString("fr-FR")}`)}return e.notes&&n.push(`(${e.notes})`),n.join(" ")}).join(" | ");if("produits"===t&&Array.isArray(e))return 0===e.length?"Aucun produit":e.map((e,t)=>{const n=[];return e.nom&&n.push(`${e.nom}`),e.quantite&&n.push(`(Qté: ${e.quantite})`),e.prixUnitaireCDF&&n.push(`${e.prixUnitaireCDF.toLocaleString("fr-FR")} CDF/unité`),n.join(" ")}).join(" | ");if(Array.isArray(e))return e.length>0?`${e.length} élément(s)`:"Aucun élément";if("object"==typeof e){const t=Object.keys(e);if(0===t.length)return"Objet vide";return t.slice(0,3).map(t=>{const n=e[t];return null!=n&&""!==n?`${t}: ${String(n).substring(0,20)}`:null}).filter(Boolean).join(", ")||"Données complexes"}return JSON.stringify(e)}static createExcelCompatibleBlob(e){return new Blob([e],{type:"text/csv;charset=utf-8"})}static downloadCSV(e,t,n){const i=Li.arrayToCSV(e,t,!0),r=Li.createExcelCompatibleBlob(i),a=URL.createObjectURL(r),s=document.createElement("a");s.href=a,s.download=n,document.body.appendChild(s),s.click(),document.body.removeChild(s),URL.revokeObjectURL(a)}static csvToArray(e,t){if(!e||""===e.trim())return[];const n=this.parseCSVLines(e);if(n.length<=1)return[];return n.slice(1).map((e,n)=>{const i=e,r={};return t.forEach((e,t)=>{let n=i[t]||"";"number"===e.type?n=""===n?0:parseFloat(n)||0:"boolean"===e.type?n="oui"===n.toLowerCase()||"true"===n.toLowerCase()||"1"===n:"date"===e.type&&n?n=new Date(n).toISOString():e.key.includes("prix")&&"string"==typeof n&&(n=parseFloat(n)||0),r[e.key]=n}),r.id||(r.id=String(n+1)),r})}static parseCSVLines(e){const t=[],n=e.split("\n");for(const i of n){if(""===i.trim())continue;const e=[];let n="",r=!1,a=0;for(;a<i.length;){const t=i[a];'"'===t?r&&'"'===i[a+1]?(n+='"',a+=2):(r=!r,a++):","!==t||r?(n+=t,a++):(e.push(n),n="",a++)}e.push(n),t.push(e)}return t}static validateCSVData(e,t){const n=[];return e.forEach((e,i)=>{t.forEach(t=>{!t.required||void 0!==e[t.key]&&null!==e[t.key]&&""!==e[t.key]||n.push(`Ligne ${i+2}: Le champ "${t.header}" est requis`),"number"===t.type&&void 0!==e[t.key]&&isNaN(Number(e[t.key]))&&n.push(`Ligne ${i+2}: Le champ "${t.header}" doit être un nombre`)})}),{isValid:0===n.length,errors:n}}static generateTemplate(e){const t=[{}];return e.forEach(e=>{switch(e.type){case"string":t[0][e.key]="Exemple";break;case"number":t[0][e.key]=100;break;case"boolean":t[0][e.key]=!0;break;case"date":t[0][e.key]=(new Date).toISOString()}}),this.arrayToCSV(t,e)}}const Oi=[{key:"id",header:"ID",type:"string",required:!0},{key:"nom",header:"Nom du Produit",type:"string",required:!0},{key:"description",header:"Description",type:"string"},{key:"prixAchatCDF",header:"Prix d'achat CDF",type:"number",required:!0},{key:"prixAchatUSD",header:"Prix d'achat USD",type:"number"},{key:"prixCDF",header:"Prix de vente CDF",type:"number",required:!0},{key:"prixUSD",header:"Prix de vente USD",type:"number"},{key:"beneficeUnitaireCDF",header:"Bénéfice unitaire CDF",type:"number"},{key:"beneficeUnitaireUSD",header:"Bénéfice unitaire USD",type:"number"},{key:"codeQR",header:"Code QR",type:"string"},{key:"categorie",header:"Catégorie",type:"string",required:!0},{key:"stock",header:"Stock",type:"number",required:!0},{key:"stockMin",header:"Stock Minimum",type:"number"},{key:"codeBarres",header:"Code Barres",type:"string"},{key:"dateCreation",header:"Date de Création",type:"date"},{key:"dateModification",header:"Date de Modification",type:"date"}],Vi=[{key:"id",header:"ID",type:"string",required:!0},{key:"nom",header:"Nom",type:"string",required:!0},{key:"email",header:"Email",type:"string",required:!0},{key:"role",header:"Rôle",type:"string",required:!0},{key:"motDePasse",header:"Mot de Passe",type:"string",required:!0},{key:"dateCreation",header:"Date de Création",type:"date"},{key:"actif",header:"Actif",type:"boolean"}],qi=[{key:"id",header:"ID",type:"string",required:!0},{key:"date",header:"Date",type:"date",required:!0},{key:"client",header:"Client",type:"string"},{key:"produits",header:"Produits (JSON)",type:"string",required:!0},{key:"totalCDF",header:"Total CDF",type:"number",required:!0},{key:"totalUSD",header:"Total USD",type:"number",required:!0},{key:"typePaiement",header:"Type de Paiement",type:"string",required:!0},{key:"typeVente",header:"Type de Vente",type:"string",required:!0},{key:"vendeur",header:"Vendeur",type:"string",required:!0},{key:"numeroRecu",header:"Numéro de Reçu",type:"string"}],Bi=[{key:"id",header:"ID",type:"string",required:!0},{key:"nomClient",header:"Client",type:"string",required:!0},{key:"telephoneClient",header:"Téléphone",type:"string"},{key:"adresseClient",header:"Adresse",type:"string"},{key:"montantTotalCDF",header:"Montant Total CDF",type:"number",required:!0},{key:"montantTotalUSD",header:"Montant Total USD",type:"number"},{key:"montantPayeCDF",header:"Montant Payé CDF",type:"number",required:!0},{key:"montantPayeUSD",header:"Montant Payé USD",type:"number"},{key:"montantRestantCDF",header:"Montant Restant CDF",type:"number",required:!0},{key:"montantRestantUSD",header:"Montant Restant USD",type:"number"},{key:"dateCreation",header:"Date de Création",type:"date",required:!0},{key:"dateEcheance",header:"Date d'Échéance",type:"date"},{key:"statut",header:"Statut",type:"string",required:!0},{key:"statutPaiement",header:"Statut de Paiement",type:"string",required:!0},{key:"venteId",header:"ID de Vente",type:"string"},{key:"paiements",header:"Paiements",type:"string"},{key:"notes",header:"Notes",type:"string"}],_i=[{key:"id",header:"ID",type:"string",required:!0},{key:"description",header:"Description",type:"string",required:!0},{key:"montantCDF",header:"Montant CDF",type:"number",required:!0},{key:"montantUSD",header:"Montant USD",type:"number",required:!0},{key:"date",header:"Date",type:"date",required:!0},{key:"categorie",header:"Catégorie",type:"string",required:!0},{key:"utilisateur",header:"Utilisateur",type:"string",required:!0},{key:"numeroRecu",header:"Numéro de Reçu",type:"string"}],zi=[{key:"id",header:"ID",type:"string",required:!0},{key:"nomComplet",header:"Nom Complet",type:"string",required:!0},{key:"poste",header:"Poste",type:"string",required:!0},{key:"salaireCDF",header:"Salaire CDF",type:"number",required:!0},{key:"salaireUSD",header:"Salaire USD",type:"number"},{key:"dateEmbauche",header:"Date d'Embauche",type:"date",required:!0},{key:"telephone",header:"Téléphone",type:"string"},{key:"adresse",header:"Adresse",type:"string"},{key:"statut",header:"Statut",type:"string",required:!0},{key:"notes",header:"Notes",type:"string"},{key:"creePar",header:"Créé par",type:"string",required:!0},{key:"dateCreation",header:"Date de Création",type:"date",required:!0},{key:"dateModification",header:"Date de Modification",type:"date"}],$i=[{key:"id",header:"ID",type:"string",required:!0},{key:"nomEmploye",header:"Nom Employé",type:"string",required:!0},{key:"montantCDF",header:"Montant CDF",type:"number",required:!0},{key:"montantUSD",header:"Montant USD",type:"number"},{key:"datePaiement",header:"Date de Paiement",type:"date",required:!0},{key:"methodePaiement",header:"Méthode de Paiement",type:"string",required:!0},{key:"notes",header:"Notes",type:"string"},{key:"creePar",header:"Créé par",type:"string",required:!0},{key:"dateCreation",header:"Date de Création",type:"date",required:!0},{key:"dateModification",header:"Date de Modification",type:"date"}],Wi=[{key:"cle",header:"Clé",type:"string",required:!0},{key:"valeur",header:"Valeur",type:"string",required:!0},{key:"type",header:"Type",type:"string",required:!0},{key:"description",header:"Description",type:"string"}];function Xi(e){var t,n;const i=[];return i.push({cle:"tauxChangeUSDCDF",valeur:(null==(t=e.tauxChangeUSDCDF)?void 0:t.toString())||"2800",type:"number",description:"Taux de change USD vers CDF"}),i.push({cle:"seuilStockBas",valeur:(null==(n=e.seuilStockBas)?void 0:n.toString())||"10",type:"number",description:"Seuil de stock bas"}),e.categories&&Array.isArray(e.categories)&&i.push({cle:"categories",valeur:JSON.stringify(e.categories),type:"json",description:"Catégories de produits"}),e.entreprise&&i.push({cle:"entreprise",valeur:JSON.stringify(e.entreprise),type:"json",description:"Informations de l'entreprise"}),e.impression&&i.push({cle:"impression",valeur:JSON.stringify(e.impression),type:"json",description:"Paramètres d'impression des reçus"}),i}const Qi=new class{constructor(){n(this,"prefix","smartboutique_csv_")}getKey(e){return`${this.prefix}${e}`}setCSV(e,t,n){try{const i=Li.arrayToCSV(t,n);localStorage.setItem(this.getKey(e),i)}catch(i){console.error(`Erreur lors de la sauvegarde CSV ${e}:`,i)}}getCSV(e,t,n=[]){try{const i=localStorage.getItem(this.getKey(e));return i?Li.csvToArray(i,t):n}catch(i){return console.error(`Erreur lors de la lecture CSV ${e}:`,i),n}}set(e,t){try{const n=JSON.stringify(t);localStorage.setItem(this.getKey(e),n)}catch(n){console.error("Erreur lors de la sauvegarde:",n)}}get(e,t){try{const n=localStorage.getItem(this.getKey(e));return null===n?t:JSON.parse(n)}catch(n){return console.error("Erreur lors de la lecture:",n),t}}remove(e){localStorage.removeItem(this.getKey(e))}clear(){Object.keys(localStorage).forEach(e=>{e.startsWith(this.prefix)&&localStorage.removeItem(e)})}getUsers(){return this.getCSV("users",Vi,[])}setUsers(e){this.setCSV("users",e,Vi)}getProducts(){return this.getCSV("products",Oi,[]).map(e=>{if(!e.prixAchatCDF&&e.prixCDF&&(e.prixAchatCDF=.7*e.prixCDF),e.prixAchatCDF=Number(e.prixAchatCDF)||0,e.prixAchatUSD=e.prixAchatUSD?Number(e.prixAchatUSD):void 0,e.prixCDF=Number(e.prixCDF)||0,e.prixUSD=e.prixUSD?Number(e.prixUSD):void 0,!e.beneficeUnitaireCDF||isNaN(Number(e.beneficeUnitaireCDF))?e.beneficeUnitaireCDF=e.prixCDF-e.prixAchatCDF:e.beneficeUnitaireCDF=Number(e.beneficeUnitaireCDF),!e.beneficeUnitaireUSD||isNaN(Number(e.beneficeUnitaireUSD))){const t=2800;e.beneficeUnitaireUSD=Math.round(e.beneficeUnitaireCDF/t*100)/100}else e.beneficeUnitaireUSD=Number(e.beneficeUnitaireUSD);e.stock=Number(e.stock)||0,e.stockMin=Number(e.stockMin)||0;const t=(new Date).toISOString();return e.dateCreation&&!isNaN(new Date(e.dateCreation).getTime())||(e.dateCreation=t),e.dateModification&&!isNaN(new Date(e.dateModification).getTime())||(e.dateModification=t),e})}setProducts(e){this.setCSV("products",e,Oi)}getSales(){try{const e=this.getCSV("sales",qi,[]);return e.filter(e=>e&&"object"==typeof e).map(e=>{const t={...e,datevente:e.date||e.datevente||(new Date).toISOString(),methodePaiement:e.typePaiement||e.methodePaiement||"cash",nomClient:e.client||e.nomClient||"Client"};t.totalCDF=Number(t.totalCDF)||0,t.totalUSD=t.totalUSD?Number(t.totalUSD):void 0;let n=t.produits||[];if("string"==typeof n)try{n=JSON.parse(n)}catch(i){console.warn("Error parsing produits JSON:",i),n=[]}return Array.isArray(n)?t.produits=n.map(e=>({...e,quantite:Number(e.quantite)||0,prixUnitaireCDF:Number(e.prixUnitaireCDF)||0,prixUnitaireUSD:e.prixUnitaireUSD?Number(e.prixUnitaireUSD):void 0,totalCDF:Number(e.totalCDF)||0,totalUSD:e.totalUSD?Number(e.totalUSD):void 0})):t.produits=[],t})}catch(e){return console.error("Error getting sales data:",e),[]}}setSales(e){const t=e.map(e=>({...e,date:e.datevente||e.date,typePaiement:e.methodePaiement||e.typePaiement,client:e.nomClient||e.client,produits:"string"==typeof e.produits?e.produits:JSON.stringify(e.produits||[])}));this.setCSV("sales",t,qi)}getDebts(){return this.getCSV("debts",Bi,[]).map(e=>{e.statutPaiement||(e.statutPaiement="paid"===e.statut?"paye":"impaye"),e.montantTotalCDF=void 0!==e.montantTotalCDF&&null!==e.montantTotalCDF&&""!==e.montantTotalCDF?Number(e.montantTotalCDF):0,e.montantTotalUSD=e.montantTotalUSD?Number(e.montantTotalUSD):void 0,e.montantPayeCDF=void 0!==e.montantPayeCDF&&null!==e.montantPayeCDF&&""!==e.montantPayeCDF?Number(e.montantPayeCDF):0,e.montantPayeUSD=e.montantPayeUSD?Number(e.montantPayeUSD):void 0,e.montantRestantCDF=void 0!==e.montantRestantCDF&&null!==e.montantRestantCDF&&""!==e.montantRestantCDF?Number(e.montantRestantCDF):e.montantTotalCDF-e.montantPayeCDF,e.montantRestantUSD=e.montantRestantUSD?Number(e.montantRestantUSD):void 0;let t=e.paiements||[];if("string"==typeof t)try{t=JSON.parse(t)}catch(n){console.warn("Error parsing paiements JSON:",n),t=[]}return Array.isArray(t)?e.paiements=t.map(e=>({...e,montantCDF:Number(e.montantCDF)||0,montantUSD:e.montantUSD?Number(e.montantUSD):void 0})):e.paiements=[],e})}setDebts(e){this.setCSV("debts",e,Bi)}getDettes(){return this.getDebts()}setDettes(e){this.setDebts(e)}getCreances(){return this.getDebts()}setCreances(e){this.setDebts(e)}getExpenses(){return this.getCSV("expenses",_i,[])}setExpenses(e){this.setCSV("expenses",e,_i)}getEmployeePayments(){return this.getCSV("employee_payments",$i,[])}setEmployeePayments(e){this.setCSV("employee_payments",e,$i)}addEmployeePayment(e){const t=this.getEmployeePayments();t.push(e),this.setEmployeePayments(t)}updateEmployeePayment(e){const t=this.getEmployeePayments(),n=t.findIndex(t=>t.id===e.id);-1!==n&&(t[n]=e,this.setEmployeePayments(t))}deleteEmployeePayment(e){const t=this.getEmployeePayments().filter(t=>t.id!==e);this.setEmployeePayments(t)}getEmployees(){return this.getCSV("employees",zi,[])}setEmployees(e){this.setCSV("employees",e,zi)}addEmployee(e){const t=this.getEmployees();t.push(e),this.setEmployees(t)}updateEmployee(e){const t=this.getEmployees(),n=t.findIndex(t=>t.id===e.id);-1!==n&&(t[n]=e,this.setEmployees(t))}deleteEmployee(e){const t=this.getEmployees().filter(t=>t.id!==e);this.setEmployees(t)}getSettings(){return this.get("settings",{tauxChangeUSDCDF:2800,seuilStockBas:10,categories:[{id:"1",nom:"Électronique",description:"Appareils électroniques",couleur:"#2196F3"},{id:"2",nom:"Vêtements",description:"Vêtements et accessoires",couleur:"#4CAF50"},{id:"3",nom:"Alimentation",description:"Produits alimentaires",couleur:"#FF9800"},{id:"4",nom:"Maison",description:"Articles pour la maison",couleur:"#9C27B0"},{id:"5",nom:"Beauté",description:"Produits de beauté",couleur:"#E91E63"},{id:"6",nom:"Boissons",description:"Boissons et breuvages",couleur:"#00BCD4"},{id:"7",nom:"Épicerie",description:"Produits d'épicerie",couleur:"#795548"},{id:"8",nom:"Livres",description:"Livres et éducation",couleur:"#607D8B"},{id:"9",nom:"Sport",description:"Articles de sport",couleur:"#FF5722"},{id:"10",nom:"Santé",description:"Produits de santé",couleur:"#8BC34A"}],entreprise:{nom:"SmartBoutique",adresse:"Kinshasa, RDC",telephone:"+*********** 000",email:"<EMAIL>",rccm:"",idNat:"",logo:""}})}setSettings(e){this.set("settings",e)}getCurrentUser(){return this.get("currentUser",null)}setCurrentUser(e){this.set("currentUser",e)}initializeDefaultData(){if(0===this.getUsers().length){const e=[{id:"1",nom:"Super Admin",email:"<EMAIL>",role:"super_admin",motDePasse:"admin123",dateCreation:(new Date).toISOString(),actif:!0},{id:"2",nom:"Gestionnaire",email:"<EMAIL>",role:"admin",motDePasse:"manager123",dateCreation:(new Date).toISOString(),actif:!0},{id:"3",nom:"Employé",email:"<EMAIL>",role:"employee",motDePasse:"employee123",dateCreation:(new Date).toISOString(),actif:!0}];this.setUsers(e)}0===this.getProducts().length&&this.initializeProductCatalog();0===this.getSales().length&&this.initializeSampleSales();0===this.getDebts().length&&this.initializeSampleDebts()}clearAllData(){localStorage.clear(),console.log("All localStorage data cleared")}initializeProductCatalog(){const e=[{id:"1",nom:"iPhone 15",description:"Smartphone Apple iPhone 15 128GB",prixAchatCDF:168e4,prixAchatUSD:600,prixCDF:224e4,prixUSD:800,beneficeUnitaireCDF:56e4,beneficeUnitaireUSD:200,codeQR:"SB12345678ABCD",categorie:"Électronique",stock:25,stockMin:5,codeBarres:"1234567890123",dateCreation:(new Date).toISOString(),dateModification:(new Date).toISOString()},{id:"2",nom:"T-shirt Nike",description:"T-shirt Nike en coton, taille M",prixAchatCDF:7e4,prixAchatUSD:25,prixCDF:98e3,prixUSD:35,beneficeUnitaireCDF:28e3,beneficeUnitaireUSD:10,codeQR:"SB12345679EFGH",categorie:"Vêtements",stock:50,stockMin:10,codeBarres:"1234567890124",dateCreation:(new Date).toISOString(),dateModification:(new Date).toISOString()},{id:"3",nom:"Café Arabica",description:"Café Arabica premium 500g",prixAchatCDF:25200,prixAchatUSD:9,prixCDF:33600,prixUSD:12,beneficeUnitaireCDF:8400,beneficeUnitaireUSD:3,codeQR:"SB12345680IJKL",categorie:"Alimentation",stock:8,stockMin:15,codeBarres:"1234567890125",dateCreation:(new Date).toISOString(),dateModification:(new Date).toISOString()},{id:"4",nom:"Sucre",description:"Sucre blanc cristallisé 1kg",prixAchatCDF:6300,prixAchatUSD:2.25,prixCDF:8400,prixUSD:3,beneficeUnitaireCDF:2100,beneficeUnitaireUSD:.75,codeQR:"SB12345681MNOP",categorie:"Épicerie",stock:120,stockMin:20,codeBarres:"1234567890126",dateCreation:(new Date).toISOString(),dateModification:(new Date).toISOString()},{id:"5",nom:"Riz",description:"Riz blanc parfumé 5kg",prixAchatCDF:33600,prixAchatUSD:12,prixCDF:42e3,prixUSD:15,beneficeUnitaireCDF:8400,beneficeUnitaireUSD:3,codeQR:"SB12345682QRST",categorie:"Alimentation",stock:80,stockMin:15,codeBarres:"1234567890127",dateCreation:(new Date).toISOString(),dateModification:(new Date).toISOString()},{id:"6",nom:"Sel",description:"Sel de cuisine iodé 500g",prixCDF:2800,prixUSD:1,codeQR:"SB12345683UVWX",categorie:"Épicerie",stock:200,stockMin:30,codeBarres:"1234567890128",dateCreation:(new Date).toISOString(),dateModification:(new Date).toISOString()},{id:"7",nom:"Lait",description:"Lait entier UHT 1 litre",prixCDF:5600,prixUSD:2,codeQR:"SB12345684YZAB",categorie:"Boissons",stock:60,stockMin:12,codeBarres:"1234567890129",dateCreation:(new Date).toISOString(),dateModification:(new Date).toISOString()},{id:"8",nom:"Thé",description:"Thé noir en sachets, boîte de 25",prixCDF:11200,prixUSD:4,codeQR:"SB12345685CDEF",categorie:"Boissons",stock:45,stockMin:10,codeBarres:"1234567890130",dateCreation:(new Date).toISOString(),dateModification:(new Date).toISOString()},{id:"9",nom:"Vestes",description:"Veste en jean unisexe, taille L",prixCDF:14e4,prixUSD:50,codeQR:"SB12345686GHIJ",categorie:"Vêtements",stock:30,stockMin:5,codeBarres:"1234567890131",dateCreation:(new Date).toISOString(),dateModification:(new Date).toISOString()},{id:"10",nom:"Livres",description:'Roman français "Le Petit Prince"',prixCDF:22400,prixUSD:8,codeQR:"SB12345687KLMN",categorie:"Livres",stock:25,stockMin:5,codeBarres:"1234567890132",dateCreation:(new Date).toISOString(),dateModification:(new Date).toISOString()}];this.setProducts(e)}initializeSampleSales(){const e=new Date,t=new Date(e.getTime()-864e5),n=new Date(e.getTime()-1728e5),i=[{id:"1",datevente:t.toISOString(),nomClient:"Jean Mukendi",telephoneClient:"+243 900 000 001",produits:[{produitId:"1",nomProduit:"iPhone 15",quantite:1,prixUnitaireCDF:224e4,prixUnitaireUSD:800,totalCDF:224e4,totalUSD:800}],totalCDF:224e4,totalUSD:800,methodePaiement:"cash",typeVente:"cash",vendeur:"Super Admin",notes:"Vente comptant"},{id:"2",datevente:t.toISOString(),nomClient:"Marie Kabila",telephoneClient:"+243 900 000 002",produits:[{produitId:"2",nomProduit:"T-shirt Nike",quantite:2,prixUnitaireCDF:98e3,prixUnitaireUSD:35,totalCDF:196e3,totalUSD:70},{produitId:"4",nomProduit:"Sucre",quantite:3,prixUnitaireCDF:8400,prixUnitaireUSD:3,totalCDF:25200,totalUSD:9}],totalCDF:221200,totalUSD:79,methodePaiement:"mobile_money",typeVente:"cash",vendeur:"Gestionnaire",notes:"Paiement mobile money"},{id:"3",datevente:n.toISOString(),nomClient:"Paul Tshisekedi",telephoneClient:"+243 900 000 003",produits:[{produitId:"5",nomProduit:"Riz",quantite:2,prixUnitaireCDF:42e3,prixUnitaireUSD:15,totalCDF:84e3,totalUSD:30}],totalCDF:84e3,totalUSD:30,methodePaiement:"card",typeVente:"cash",vendeur:"Employé",notes:"Paiement par carte"}];this.setSales(i)}initializeSampleDebts(){const e=new Date,t=new Date(e.getTime()-6048e5),n=new Date(e.getTime()-12096e5),i=new Date(e.getTime()+2592e6),r=new Date(e.getTime()+12096e5),a=[{id:"DET-001",venteId:"VTE-CREDIT-001",nomClient:"Jean Baptiste Mukendi",telephoneClient:"+243 812 345 678",adresseClient:"Avenue Lumumba, Kinshasa",montantTotalCDF:15e4,montantTotalUSD:53.57,montantPayeCDF:0,montantPayeUSD:0,montantRestantCDF:15e4,montantRestantUSD:53.57,dateCreation:t.toISOString(),dateEcheance:i.toISOString(),statut:"active",statutPaiement:"impaye",paiements:[],notes:"Vente à crédit - Produits électroniques"},{id:"DET-002",venteId:"VTE-CREDIT-002",nomClient:"Marie Kabila Tshisekedi",telephoneClient:"+243 998 765 432",adresseClient:"Boulevard du 30 Juin, Kinshasa",montantTotalCDF:25e4,montantTotalUSD:89.29,montantPayeCDF:1e5,montantPayeUSD:35.71,montantRestantCDF:15e4,montantRestantUSD:53.57,dateCreation:n.toISOString(),dateEcheance:r.toISOString(),statut:"active",statutPaiement:"impaye",paiements:[{id:"PAY-001",montantCDF:1e5,montantUSD:35.71,methodePaiement:"cash",datePaiement:new Date(e.getTime()-2592e5).toISOString(),notes:"Paiement partiel en espèces"}],notes:"Paiement partiel effectué - Vêtements et accessoires"},{id:"DET-003",venteId:"VTE-CREDIT-003",nomClient:"Joseph Kabila Kabange",telephoneClient:"+243 811 222 333",adresseClient:"Avenue de la Paix, Lubumbashi",montantTotalCDF:18e4,montantTotalUSD:64.29,montantPayeCDF:18e4,montantPayeUSD:64.29,montantRestantCDF:0,montantRestantUSD:0,dateCreation:n.toISOString(),dateEcheance:i.toISOString(),statut:"paid",statutPaiement:"paye",paiements:[{id:"PAY-002",montantCDF:18e4,montantUSD:64.29,methodePaiement:"mobile_money",datePaiement:new Date(e.getTime()-1728e5).toISOString(),notes:"Paiement complet via Mobile Money"}],notes:"Dette entièrement payée - Alimentation et boissons"},{id:"DET-004",venteId:"VTE-CREDIT-004",nomClient:"Fatou Diallo Sankara",telephoneClient:"+243 977 888 999",adresseClient:"Quartier Matonge, Kinshasa",montantTotalCDF:32e4,montantTotalUSD:114.29,montantPayeCDF:8e4,montantPayeUSD:28.57,montantRestantCDF:24e4,montantRestantUSD:85.71,dateCreation:new Date(e.getTime()-3888e6).toISOString(),dateEcheance:new Date(e.getTime()-1296e6).toISOString(),statut:"overdue",statutPaiement:"impaye",paiements:[{id:"PAY-003",montantCDF:8e4,montantUSD:28.57,methodePaiement:"cash",datePaiement:new Date(e.getTime()-2592e6).toISOString(),notes:"Paiement partiel initial"}],notes:"Dette en retard - Nécessite suivi"}];this.setDebts(a)}exportData(){const e=this.getUsers(),t=this.getProducts(),n=this.getSales(),i=this.getDebts(),r=this.getExpenses(),a=this.getEmployeePayments(),s=this.getSettings(),o={exportDate:(new Date).toISOString(),products:Li.arrayToCSV(t,Oi),users:Li.arrayToCSV(e,Vi),sales:Li.arrayToCSV(n,qi),debts:Li.arrayToCSV(i,Bi),expenses:Li.arrayToCSV(r,_i),employeePayments:Li.arrayToCSV(a,$i),settings:Li.arrayToCSV(Xi(s),Wi)};return{csvData:`SmartBoutique - Sauvegarde Complète (Desktop)\nDate d'exportation: ${o.exportDate}\n\n=== PRODUITS ===\n${o.products}\n\n=== UTILISATEURS ===\n${o.users}\n\n=== VENTES ===\n${o.sales}\n\n=== DETTES ===\n${o.debts}\n\n=== DÉPENSES ===\n${o.expenses}\n\n=== PAIEMENTS EMPLOYÉS ===\n${o.employeePayments}\n\n=== PARAMÈTRES ===\n${o.settings}\n`,exportDate:o.exportDate}}importData(e){try{return e.csvData&&"string"==typeof e.csvData?this.importFromCSVBackup(e.csvData):(e.users&&this.setUsers(e.users),e.products&&this.setProducts(e.products),e.sales&&this.setSales(e.sales),e.debts&&this.setDebts(e.debts),e.expenses&&this.setExpenses(e.expenses),e.employeePayments&&this.setEmployeePayments(e.employeePayments),e.settings&&this.setSettings(e.settings),!0)}catch(t){return console.error("Erreur lors de l'importation:",t),!1}}importFromCSVBackup(e){try{const t=this.parseCSVBackup(e);if(t.products){const e=Li.csvToArray(t.products,Oi);this.setProducts(e)}if(t.users){const e=Li.csvToArray(t.users,Vi);this.setUsers(e)}if(t.sales){const e=Li.csvToArray(t.sales,qi);this.setSales(e)}if(t.debts){const e=Li.csvToArray(t.debts,Bi);this.setDebts(e)}if(t.expenses){const e=Li.csvToArray(t.expenses,_i);this.setExpenses(e)}if(t.employeePayments){const e=Li.csvToArray(t.employeePayments,$i);this.setEmployeePayments(e)}if(t.settings){const e=function(e){const t={};return e.forEach(e=>{const n=e.cle;let i=e.valeur;switch(e.type){case"number":i=parseFloat(i)||0;break;case"boolean":i="true"===i||"1"===i||"Oui"===i;break;case"json":try{i=JSON.parse(i)}catch(r){console.error(`Erreur lors du parsing JSON pour ${n}:`,r),i=null}}t[n]=i}),t}(Li.csvToArray(t.settings,Wi));this.setSettings(e)}return!0}catch(t){return console.error("Erreur lors de l'importation CSV:",t),!1}}parseCSVBackup(e){const t={},n=e.split("\n");let i="",r=[];for(const a of n)if(a.startsWith("=== ")&&a.endsWith(" ===")){i&&r.length>0&&(t[i]=r.join("\n"));switch(a.replace(/=== | ===/g,"").toLowerCase()){case"produits":i="products";break;case"utilisateurs":i="users";break;case"ventes":i="sales";break;case"dettes":i="debts";break;case"dépenses":i="expenses";break;case"paiements employés":i="employeePayments";break;case"paramètres":i="settings";break;default:i=""}r=[]}else i&&""!==a.trim()&&r.push(a);return i&&r.length>0&&(t[i]=r.join("\n")),t}exportCSV(e){let t=[],n=[];switch(e){case"products":t=this.getProducts(),n=Oi;break;case"users":t=this.getUsers(),n=Vi;break;case"sales":t=this.getSales(),n=qi;break;case"debts":t=this.getDebts(),n=Bi;break;case"expenses":t=this.getExpenses(),n=_i;break;case"employee_payments":t=this.getEmployeePayments(),n=$i}return Li.arrayToCSV(t,n)}importCSV(e,t,n=!1){try{let i=[],r=[];switch(e){case"products":i=Oi,r=n?[]:this.getProducts();break;case"users":i=Vi,r=n?[]:this.getUsers();break;default:return{success:!1,message:"Type de données non supporté",errors:[]}}const a=Li.csvToArray(t,i),s=Li.validateCSVData(a,i);if(!s.isValid)return{success:!1,message:"Données invalides",errors:s.errors};let o=a;if(!n&&r.length>0){const e=new Set(r.map(e=>e.id)),t=a.filter(t=>!e.has(t.id));o=[...r,...t]}switch(e){case"products":this.setProducts(o);break;case"users":this.setUsers(o)}return{success:!0,message:`${a.length} éléments importés avec succès`,errors:[]}}catch(i){return{success:!1,message:"Erreur lors de l'importation: "+i.message,errors:[i.message]}}}},Ji=Fi("Preferences",{web:()=>Si(()=>import("./web-MUvptLmm.js"),__vite__mapDeps([0,1,2,3,4]),import.meta.url).then(e=>new e.PreferencesWeb)}),Hi=Object.freeze(Object.defineProperty({__proto__:null,Preferences:Ji},Symbol.toStringTag,{value:"Module"}));const Gi=new class{constructor(){n(this,"CSV_PREFIX","smartboutique_csv_")}async setCSV(e,t,n){try{const i=Li.arrayToCSV(t,n);await Ji.set({key:this.CSV_PREFIX+e,value:i})}catch(i){throw console.error(`Erreur lors de la sauvegarde CSV ${e}:`,i),i}}async getCSV(e,t,n=[]){try{const i=await Ji.get({key:this.CSV_PREFIX+e});return i.value?Li.csvToArray(i.value,t):n}catch(i){return console.error(`Erreur lors de la lecture CSV ${e}:`,i),n}}async getProducts(){return(await this.getCSV("products",Oi,[])).map(e=>{if(!e.prixAchatCDF&&e.prixCDF&&(e.prixAchatCDF=.7*e.prixCDF),e.prixAchatCDF=Number(e.prixAchatCDF)||0,e.prixAchatUSD=e.prixAchatUSD?Number(e.prixAchatUSD):void 0,e.prixCDF=Number(e.prixCDF)||0,e.prixUSD=e.prixUSD?Number(e.prixUSD):void 0,!e.beneficeUnitaireCDF||isNaN(Number(e.beneficeUnitaireCDF))?e.beneficeUnitaireCDF=e.prixCDF-e.prixAchatCDF:e.beneficeUnitaireCDF=Number(e.beneficeUnitaireCDF),!e.beneficeUnitaireUSD||isNaN(Number(e.beneficeUnitaireUSD))){const t=2800;e.beneficeUnitaireUSD=Math.round(e.beneficeUnitaireCDF/t*100)/100}else e.beneficeUnitaireUSD=Number(e.beneficeUnitaireUSD);e.stock=Number(e.stock)||0,e.stockMin=Number(e.stockMin)||0;const t=(new Date).toISOString();return e.dateCreation&&!isNaN(new Date(e.dateCreation).getTime())||(e.dateCreation=t),e.dateModification&&!isNaN(new Date(e.dateModification).getTime())||(e.dateModification=t),e})}async setProducts(e){await this.setCSV("products",e,Oi)}async getUsers(){return this.getCSV("users",Vi,[])}async setUsers(e){await this.setCSV("users",e,Vi)}async getSales(){return(await this.getCSV("sales",qi,[])).map(e=>(e.totalCDF=Number(e.totalCDF)||0,e.totalUSD=e.totalUSD?Number(e.totalUSD):void 0,e.produits=(e.produits||[]).map(e=>({...e,quantite:Number(e.quantite)||0,prixUnitaireCDF:Number(e.prixUnitaireCDF)||0,prixUnitaireUSD:e.prixUnitaireUSD?Number(e.prixUnitaireUSD):void 0,totalCDF:Number(e.totalCDF)||0,totalUSD:e.totalUSD?Number(e.totalUSD):void 0})),e))}async setSales(e){await this.setCSV("sales",e,qi)}async getDebts(){return(await this.getCSV("debts",Bi,[])).map(e=>(e.statutPaiement||(e.statutPaiement="paid"===e.statut?"paye":"impaye"),e.montantTotalCDF=void 0!==e.montantTotalCDF&&null!==e.montantTotalCDF&&""!==e.montantTotalCDF?Number(e.montantTotalCDF):0,e.montantTotalUSD=e.montantTotalUSD?Number(e.montantTotalUSD):void 0,e.montantPayeCDF=void 0!==e.montantPayeCDF&&null!==e.montantPayeCDF&&""!==e.montantPayeCDF?Number(e.montantPayeCDF):0,e.montantPayeUSD=e.montantPayeUSD?Number(e.montantPayeUSD):void 0,e.montantRestantCDF=void 0!==e.montantRestantCDF&&null!==e.montantRestantCDF&&""!==e.montantRestantCDF?Number(e.montantRestantCDF):e.montantTotalCDF-e.montantPayeCDF,e.montantRestantUSD=e.montantRestantUSD?Number(e.montantRestantUSD):void 0,e.paiements=(e.paiements||[]).map(e=>({...e,montantCDF:Number(e.montantCDF)||0,montantUSD:e.montantUSD?Number(e.montantUSD):void 0})),e))}async setDebts(e){await this.setCSV("debts",e,Bi)}async getExpenses(){return this.getCSV("expenses",_i,[])}async setExpenses(e){await this.setCSV("expenses",e,_i)}async getEmployees(){return this.getCSV("employees",zi,[])}async setEmployees(e){await this.setCSV("employees",e,zi)}async addEmployee(e){const t=await this.getEmployees();t.push(e),await this.setEmployees(t)}async updateEmployee(e){const t=await this.getEmployees(),n=t.findIndex(t=>t.id===e.id);-1!==n&&(t[n]=e,await this.setEmployees(t))}async deleteEmployee(e){const t=(await this.getEmployees()).filter(t=>t.id!==e);await this.setEmployees(t)}async getSettings(){const e=await Ji.get({key:this.CSV_PREFIX+"settings"});if(e.value)try{return JSON.parse(e.value)}catch(t){console.error("Erreur lors du parsing des paramètres:",t)}return{tauxChangeUSDCDF:2800,seuilStockBas:10,categories:[{id:"1",nom:"Électronique",description:"Appareils électroniques",couleur:"#2196F3"},{id:"2",nom:"Vêtements",description:"Vêtements et accessoires",couleur:"#4CAF50"},{id:"3",nom:"Alimentation",description:"Produits alimentaires",couleur:"#FF9800"},{id:"4",nom:"Maison",description:"Articles pour la maison",couleur:"#9C27B0"},{id:"5",nom:"Beauté",description:"Produits de beauté",couleur:"#E91E63"},{id:"6",nom:"Boissons",description:"Boissons et breuvages",couleur:"#00BCD4"},{id:"7",nom:"Épicerie",description:"Produits d'épicerie",couleur:"#795548"},{id:"8",nom:"Livres",description:"Livres et éducation",couleur:"#607D8B"},{id:"9",nom:"Sport",description:"Articles de sport",couleur:"#FF5722"},{id:"10",nom:"Santé",description:"Produits de santé",couleur:"#8BC34A"}],entreprise:{nom:"SmartBoutique",adresse:"Kinshasa, RDC",telephone:"+*********** 000",email:"<EMAIL>",rccm:"",idNat:"",logo:""}}}async setSettings(e){await Ji.set({key:this.CSV_PREFIX+"settings",value:JSON.stringify(e)})}async getCurrentUser(){const e=await Ji.get({key:this.CSV_PREFIX+"currentUser"});return e.value?JSON.parse(e.value):null}async setCurrentUser(e){await Ji.set({key:this.CSV_PREFIX+"currentUser",value:JSON.stringify(e)})}async initializeDefaultData(){if(0===(await this.getUsers()).length){const e=[{id:"1",nom:"Super Admin",email:"<EMAIL>",role:"super_admin",motDePasse:"admin123",dateCreation:(new Date).toISOString(),actif:!0},{id:"2",nom:"Gestionnaire",email:"<EMAIL>",role:"admin",motDePasse:"manager123",dateCreation:(new Date).toISOString(),actif:!0},{id:"3",nom:"Employé",email:"<EMAIL>",role:"employee",motDePasse:"employee123",dateCreation:(new Date).toISOString(),actif:!0}];await this.setUsers(e)}0===(await this.getProducts()).length&&await this.initializeProductCatalog();0===(await this.getDebts()).length&&await this.initializeSampleDebts()}async initializeProductCatalog(){const e=[{id:"1",nom:"iPhone 15",description:"Smartphone Apple iPhone 15 128GB",prixCDF:224e4,prixUSD:800,codeQR:"SB12345678ABCD",categorie:"Électronique",stock:25,stockMin:5,codeBarres:"1234567890123",dateCreation:(new Date).toISOString(),dateModification:(new Date).toISOString()},{id:"2",nom:"T-shirt Nike",description:"T-shirt Nike en coton, taille M",prixCDF:98e3,prixUSD:35,codeQR:"SB12345679EFGH",categorie:"Vêtements",stock:50,stockMin:10,codeBarres:"1234567890124",dateCreation:(new Date).toISOString(),dateModification:(new Date).toISOString()},{id:"3",nom:"Café Arabica",description:"Café Arabica premium 500g",prixCDF:33600,prixUSD:12,codeQR:"SB12345680IJKL",categorie:"Alimentation",stock:8,stockMin:15,codeBarres:"1234567890125",dateCreation:(new Date).toISOString(),dateModification:(new Date).toISOString()},{id:"4",nom:"Sucre",description:"Sucre blanc cristallisé 1kg",prixCDF:8400,prixUSD:3,codeQR:"SB12345681MNOP",categorie:"Épicerie",stock:120,stockMin:20,codeBarres:"1234567890126",dateCreation:(new Date).toISOString(),dateModification:(new Date).toISOString()},{id:"5",nom:"Riz",description:"Riz blanc parfumé 5kg",prixCDF:42e3,prixUSD:15,codeQR:"SB12345682QRST",categorie:"Alimentation",stock:80,stockMin:15,codeBarres:"1234567890127",dateCreation:(new Date).toISOString(),dateModification:(new Date).toISOString()},{id:"6",nom:"Sel",description:"Sel de cuisine iodé 500g",prixCDF:2800,prixUSD:1,codeQR:"SB12345683UVWX",categorie:"Épicerie",stock:200,stockMin:30,codeBarres:"1234567890128",dateCreation:(new Date).toISOString(),dateModification:(new Date).toISOString()},{id:"7",nom:"Lait",description:"Lait entier UHT 1 litre",prixCDF:5600,prixUSD:2,codeQR:"SB12345684YZAB",categorie:"Boissons",stock:60,stockMin:12,codeBarres:"1234567890129",dateCreation:(new Date).toISOString(),dateModification:(new Date).toISOString()},{id:"8",nom:"Thé",description:"Thé noir en sachets, boîte de 25",prixCDF:11200,prixUSD:4,codeQR:"SB12345685CDEF",categorie:"Boissons",stock:45,stockMin:10,codeBarres:"1234567890130",dateCreation:(new Date).toISOString(),dateModification:(new Date).toISOString()},{id:"9",nom:"Vestes",description:"Veste en jean unisexe, taille L",prixCDF:14e4,prixUSD:50,codeQR:"SB12345686GHIJ",categorie:"Vêtements",stock:30,stockMin:5,codeBarres:"1234567890131",dateCreation:(new Date).toISOString(),dateModification:(new Date).toISOString()},{id:"10",nom:"Livres",description:'Roman français "Le Petit Prince"',prixCDF:22400,prixUSD:8,codeQR:"SB12345687KLMN",categorie:"Livres",stock:25,stockMin:5,codeBarres:"1234567890132",dateCreation:(new Date).toISOString(),dateModification:(new Date).toISOString()},{id:"11",nom:"Huile de Palme",description:"Huile de palme rouge 1 litre",prixCDF:16800,prixUSD:6,codeQR:"SB12345688OPQR",categorie:"Alimentation",stock:40,stockMin:8,codeBarres:"1234567890133",dateCreation:(new Date).toISOString(),dateModification:(new Date).toISOString()},{id:"12",nom:"Farine de Maïs",description:"Farine de maïs blanche 2kg",prixCDF:11200,prixUSD:4,codeQR:"SB12345689STUV",categorie:"Alimentation",stock:75,stockMin:15,codeBarres:"1234567890134",dateCreation:(new Date).toISOString(),dateModification:(new Date).toISOString()}];await this.setProducts(e)}async clearAllData(){try{const e=["products","users","sales","debts","expenses","settings","currentUser"];for(const t of e)await Ji.remove({key:this.CSV_PREFIX+t});console.log("✅ All CSV data cleared from mobile storage")}catch(e){throw console.error("❌ Error clearing CSV data:",e),e}}async initializeSampleDebts(){const e=new Date,t=new Date(e.getTime()-6048e5),n=new Date(e.getTime()-12096e5),i=new Date(e.getTime()+2592e6),r=new Date(e.getTime()+12096e5),a=[{id:"DET-001",venteId:"VTE-CREDIT-001",nomClient:"Jean Baptiste Mukendi",telephoneClient:"+243 812 345 678",adresseClient:"Avenue Lumumba, Kinshasa",montantTotalCDF:15e4,montantTotalUSD:53.57,montantPayeCDF:0,montantPayeUSD:0,montantRestantCDF:15e4,montantRestantUSD:53.57,dateCreation:t.toISOString(),dateEcheance:i.toISOString(),statut:"active",statutPaiement:"impaye",paiements:[],notes:"Vente à crédit - Produits électroniques"},{id:"DET-002",venteId:"VTE-CREDIT-002",nomClient:"Marie Kabila Tshisekedi",telephoneClient:"+243 998 765 432",adresseClient:"Boulevard du 30 Juin, Kinshasa",montantTotalCDF:25e4,montantTotalUSD:89.29,montantPayeCDF:1e5,montantPayeUSD:35.71,montantRestantCDF:15e4,montantRestantUSD:53.57,dateCreation:n.toISOString(),dateEcheance:r.toISOString(),statut:"active",statutPaiement:"impaye",paiements:[{id:"PAY-001",montantCDF:1e5,montantUSD:35.71,methodePaiement:"cash",datePaiement:new Date(e.getTime()-2592e5).toISOString(),notes:"Paiement partiel en espèces"}],notes:"Paiement partiel effectué - Vêtements et accessoires"},{id:"DET-003",venteId:"VTE-CREDIT-003",nomClient:"Joseph Kabila Kabange",telephoneClient:"+243 811 222 333",adresseClient:"Avenue de la Paix, Lubumbashi",montantTotalCDF:18e4,montantTotalUSD:64.29,montantPayeCDF:18e4,montantPayeUSD:64.29,montantRestantCDF:0,montantRestantUSD:0,dateCreation:n.toISOString(),dateEcheance:i.toISOString(),statut:"paid",statutPaiement:"paye",paiements:[{id:"PAY-002",montantCDF:18e4,montantUSD:64.29,methodePaiement:"mobile_money",datePaiement:new Date(e.getTime()-1728e5).toISOString(),notes:"Paiement complet via Mobile Money"}],notes:"Dette entièrement payée - Alimentation et boissons"},{id:"DET-004",venteId:"VTE-CREDIT-004",nomClient:"Fatou Diallo Sankara",telephoneClient:"+243 977 888 999",adresseClient:"Quartier Matonge, Kinshasa",montantTotalCDF:32e4,montantTotalUSD:114.29,montantPayeCDF:8e4,montantPayeUSD:28.57,montantRestantCDF:24e4,montantRestantUSD:85.71,dateCreation:new Date(e.getTime()-3888e6).toISOString(),dateEcheance:new Date(e.getTime()-1296e6).toISOString(),statut:"overdue",statutPaiement:"impaye",paiements:[{id:"PAY-003",montantCDF:8e4,montantUSD:28.57,methodePaiement:"cash",datePaiement:new Date(e.getTime()-2592e6).toISOString(),notes:"Paiement partiel initial"}],notes:"Dette en retard - Nécessite suivi"}];await this.setDebts(a)}};let Yi=null,Ki=null,Zi=null;const er="undefined"==typeof window&&"undefined"!=typeof process&&(null==(e=process.versions)?void 0:e.electron);if(er)try{Yi=require("better-sqlite3"),Ki=require("electron").app,Zi=require("path").join}catch(ba){console.warn("better-sqlite3 not available:",ba)}class tr{constructor(){if(n(this,"db"),n(this,"dbPath"),n(this,"isAvailable",!1),er&&Yi)try{const e=(null==Ki?void 0:Ki.getPath("userData"))||"./data";this.dbPath=Zi(e,"smartboutique.db"),this.initializeDatabase(),this.isAvailable=!0}catch(ba){console.error("Failed to initialize SQLite:",ba)}else console.warn("SQLite not available in this context")}initializeDatabase(){if(!Yi)throw new Error("better-sqlite3 not available");this.db=new Yi(this.dbPath),this.db.pragma("journal_mode = WAL"),this.db.pragma("synchronous = NORMAL"),this.db.pragma("cache_size = 1000"),this.db.pragma("temp_store = memory"),this.createTables(),this.createIndexes()}checkAvailability(){if(!this.isAvailable)throw new Error("SQLite storage not available in this context")}createTables(){this.db.exec("\n      CREATE TABLE IF NOT EXISTS products (\n        id TEXT PRIMARY KEY,\n        nom TEXT NOT NULL,\n        description TEXT,\n        prixAchatCDF REAL NOT NULL,\n        prixAchatUSD REAL,\n        prixCDF REAL NOT NULL,\n        prixUSD REAL,\n        beneficeUnitaireCDF REAL,\n        beneficeUnitaireUSD REAL,\n        codeQR TEXT,\n        categorie TEXT,\n        stock INTEGER,\n        stockMin INTEGER,\n        codeBarres TEXT,\n        dateCreation TEXT,\n        dateModification TEXT,\n        quantiteEnStock INTEGER,\n        coutAchatStockCDF REAL,\n        coutAchatStockUSD REAL,\n        prixParPieceCDF REAL,\n        prixParPieceUSD REAL\n      )\n    "),this.db.exec("\n      CREATE TABLE IF NOT EXISTS users (\n        id TEXT PRIMARY KEY,\n        nom TEXT NOT NULL,\n        email TEXT NOT NULL UNIQUE,\n        role TEXT NOT NULL,\n        motDePasse TEXT NOT NULL,\n        dateCreation TEXT,\n        actif INTEGER DEFAULT 1\n      )\n    "),this.db.exec("\n      CREATE TABLE IF NOT EXISTS sales (\n        id TEXT PRIMARY KEY,\n        date TEXT NOT NULL,\n        client TEXT,\n        produits TEXT NOT NULL,\n        totalCDF REAL NOT NULL,\n        totalUSD REAL,\n        typePaiement TEXT NOT NULL,\n        typeVente TEXT NOT NULL,\n        vendeur TEXT NOT NULL,\n        numeroRecu TEXT\n      )\n    "),this.db.exec("\n      CREATE TABLE IF NOT EXISTS debts (\n        id TEXT PRIMARY KEY,\n        client TEXT NOT NULL,\n        montantCDF REAL NOT NULL,\n        montantUSD REAL,\n        dateCreation TEXT NOT NULL,\n        dateEcheance TEXT,\n        statut TEXT NOT NULL,\n        description TEXT,\n        vendeur TEXT NOT NULL\n      )\n    "),this.db.exec("\n      CREATE TABLE IF NOT EXISTS expenses (\n        id TEXT PRIMARY KEY,\n        description TEXT NOT NULL,\n        montantCDF REAL NOT NULL,\n        montantUSD REAL,\n        date TEXT NOT NULL,\n        categorie TEXT NOT NULL,\n        utilisateur TEXT NOT NULL,\n        numeroRecu TEXT\n      )\n    "),this.db.exec("\n      CREATE TABLE IF NOT EXISTS settings (\n        key TEXT PRIMARY KEY,\n        value TEXT NOT NULL\n      )\n    "),this.db.exec("\n      CREATE TABLE IF NOT EXISTS employees (\n        id TEXT PRIMARY KEY,\n        nomComplet TEXT NOT NULL,\n        poste TEXT NOT NULL,\n        salaireCDF REAL NOT NULL,\n        salaireUSD REAL,\n        dateEmbauche TEXT NOT NULL,\n        telephone TEXT,\n        adresse TEXT,\n        statut TEXT NOT NULL,\n        notes TEXT,\n        creePar TEXT NOT NULL,\n        dateCreation TEXT NOT NULL,\n        dateModification TEXT\n      )\n    "),this.db.exec("\n      CREATE TABLE IF NOT EXISTS employee_payments (\n        id TEXT PRIMARY KEY,\n        nomEmploye TEXT NOT NULL,\n        montantCDF REAL NOT NULL,\n        montantUSD REAL,\n        datePaiement TEXT NOT NULL,\n        methodePaiement TEXT NOT NULL,\n        notes TEXT,\n        creePar TEXT NOT NULL,\n        dateCreation TEXT NOT NULL,\n        dateModification TEXT\n      )\n    ")}createIndexes(){this.db.exec("\n      CREATE INDEX IF NOT EXISTS idx_products_categorie ON products(categorie);\n      CREATE INDEX IF NOT EXISTS idx_products_stock ON products(stock);\n      CREATE INDEX IF NOT EXISTS idx_sales_date ON sales(date);\n      CREATE INDEX IF NOT EXISTS idx_sales_vendeur ON sales(vendeur);\n      CREATE INDEX IF NOT EXISTS idx_debts_statut ON debts(statut);\n      CREATE INDEX IF NOT EXISTS idx_debts_client ON debts(client);\n      CREATE INDEX IF NOT EXISTS idx_expenses_date ON expenses(date);\n      CREATE INDEX IF NOT EXISTS idx_expenses_categorie ON expenses(categorie);\n      CREATE INDEX IF NOT EXISTS idx_employee_payments_date ON employee_payments(datePaiement);\n      CREATE INDEX IF NOT EXISTS idx_employee_payments_employe ON employee_payments(nomEmploye);\n      CREATE INDEX IF NOT EXISTS idx_employee_payments_methode ON employee_payments(methodePaiement);\n      CREATE INDEX IF NOT EXISTS idx_employee_payments_cree_par ON employee_payments(creePar);\n    ")}getProducts(){this.checkAvailability();return this.db.prepare("SELECT * FROM products ORDER BY nom").all()}getProduct(e){this.checkAvailability();return this.db.prepare("SELECT * FROM products WHERE id = ?").get(e)}setProducts(e){this.db.transaction(e=>{this.db.prepare("DELETE FROM products").run();const t=this.db.prepare("\n        INSERT INTO products (\n          id, nom, description, prixAchatCDF, prixAchatUSD, prixCDF, prixUSD,\n          beneficeUnitaireCDF, beneficeUnitaireUSD, codeQR, categorie, stock,\n          stockMin, codeBarres, dateCreation, dateModification\n        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)\n      ");for(const n of e)t.run(n.id,n.nom,n.description,n.prixAchatCDF,n.prixAchatUSD,n.prixCDF,n.prixUSD,n.beneficeUnitaireCDF,n.beneficeUnitaireUSD,n.codeQR,n.categorie,n.stock,n.stockMin,n.codeBarres,n.dateCreation,n.dateModification)})(e)}addProduct(e){this.db.prepare("\n      INSERT INTO products (\n        id, nom, description, prixAchatCDF, prixAchatUSD, prixCDF, prixUSD,\n        beneficeUnitaireCDF, beneficeUnitaireUSD, codeQR, categorie, stock,\n        stockMin, codeBarres, dateCreation, dateModification, quantiteEnStock,\n        coutAchatStockCDF, coutAchatStockUSD, prixParPieceCDF, prixParPieceUSD\n      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)\n    ").run(e.id,e.nom,e.description,e.prixAchatCDF,e.prixAchatUSD,e.prixCDF,e.prixUSD,e.beneficeUnitaireCDF,e.beneficeUnitaireUSD,e.codeQR,e.categorie,e.stock,e.stockMin,e.codeBarres,e.dateCreation,e.dateModification,e.quantiteEnStock,e.coutAchatStockCDF,e.coutAchatStockUSD,e.prixParPieceCDF,e.prixParPieceUSD)}updateProduct(e){this.db.prepare("\n      UPDATE products SET\n        nom = ?, description = ?, prixAchatCDF = ?, prixAchatUSD = ?,\n        prixCDF = ?, prixUSD = ?, beneficeUnitaireCDF = ?, beneficeUnitaireUSD = ?,\n        codeQR = ?, categorie = ?, stock = ?, stockMin = ?, codeBarres = ?,\n        dateModification = ?\n      WHERE id = ?\n    ").run(e.nom,e.description,e.prixAchatCDF,e.prixAchatUSD,e.prixCDF,e.prixUSD,e.beneficeUnitaireCDF,e.beneficeUnitaireUSD,e.codeQR,e.categorie,e.stock,e.stockMin,e.codeBarres,e.dateModification,e.id)}deleteProduct(e){this.db.prepare("DELETE FROM products WHERE id = ?").run(e)}getUsers(){return this.db.prepare("SELECT * FROM users ORDER BY nom").all().map(e=>({...e,actif:Boolean(e.actif)}))}setUsers(e){this.db.transaction(e=>{this.db.prepare("DELETE FROM users").run();const t=this.db.prepare("\n        INSERT INTO users (id, nom, email, role, motDePasse, dateCreation, actif)\n        VALUES (?, ?, ?, ?, ?, ?, ?)\n      ");for(const n of e)t.run(n.id,n.nom,n.email,n.role,n.motDePasse,n.dateCreation,n.actif?1:0)})(e)}getSales(){return this.db.prepare("SELECT * FROM sales ORDER BY date DESC").all().map(e=>({...e,produits:JSON.parse(e.produits)}))}setSales(e){this.db.transaction(e=>{this.db.prepare("DELETE FROM sales").run();const t=this.db.prepare("\n        INSERT INTO sales (id, date, client, produits, totalCDF, totalUSD, typePaiement, typeVente, vendeur, numeroRecu)\n        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)\n      ");for(const n of e)t.run(n.id,n.datevente,n.nomClient,JSON.stringify(n.produits),n.totalCDF,n.totalUSD,n.methodePaiement,n.typeVente,n.vendeur,n.numeroRecu)})(e)}getEmployeePayments(){this.checkAvailability();return this.db.prepare("SELECT * FROM employee_payments ORDER BY datePaiement DESC").all()}getEmployeePayment(e){this.checkAvailability();return this.db.prepare("SELECT * FROM employee_payments WHERE id = ?").get(e)}addEmployeePayment(e){this.checkAvailability();this.db.prepare("\n      INSERT INTO employee_payments (\n        id, nomEmploye, montantCDF, montantUSD, datePaiement, methodePaiement,\n        notes, creePar, dateCreation, dateModification\n      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)\n    ").run(e.id,e.nomEmploye,e.montantCDF,e.montantUSD,e.datePaiement,e.methodePaiement,e.notes,e.creePar,e.dateCreation,e.dateModification)}clearAllData(){this.checkAvailability();try{this.db.transaction(()=>{this.db.prepare("DELETE FROM products").run(),this.db.prepare("DELETE FROM users").run(),this.db.prepare("DELETE FROM sales").run(),this.db.prepare("DELETE FROM debts").run(),this.db.prepare("DELETE FROM expenses").run(),this.db.prepare("DELETE FROM employee_payments").run(),this.db.prepare("DELETE FROM settings").run(),this.db.prepare("DELETE FROM sqlite_sequence").run()})(),console.log("✅ All SQLite data cleared successfully")}catch(ba){throw console.error("❌ Error clearing SQLite data:",ba),ba}}updateEmployeePayment(e){this.checkAvailability();this.db.prepare("\n      UPDATE employee_payments SET\n        nomEmploye = ?, montantCDF = ?, montantUSD = ?, datePaiement = ?,\n        methodePaiement = ?, notes = ?, dateModification = ?\n      WHERE id = ?\n    ").run(e.nomEmploye,e.montantCDF,e.montantUSD,e.datePaiement,e.methodePaiement,e.notes,e.dateModification,e.id)}deleteEmployeePayment(e){this.checkAvailability();this.db.prepare("DELETE FROM employee_payments WHERE id = ?").run(e)}setEmployeePayments(e){this.checkAvailability();this.db.transaction(e=>{this.db.prepare("DELETE FROM employee_payments").run();const t=this.db.prepare("\n        INSERT INTO employee_payments (\n          id, nomEmploye, montantCDF, montantUSD, datePaiement, methodePaiement,\n          notes, creePar, dateCreation, dateModification\n        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)\n      ");for(const n of e)t.run(n.id,n.nomEmploye,n.montantCDF,n.montantUSD,n.datePaiement,n.methodePaiement,n.notes,n.creePar,n.dateCreation,n.dateModification)})(e)}migrateFromCSV(e){console.log("Starting migration from CSV to SQLite...");this.db.transaction(()=>{var t,n,i;(null==(t=e.products)?void 0:t.length)&&this.setProducts(e.products),(null==(n=e.users)?void 0:n.length)&&this.setUsers(e.users),(null==(i=e.sales)?void 0:i.length)&&this.setSales(e.sales)})(),console.log("Migration completed successfully")}exportToCSV(){return{products:"",users:"",sales:""}}close(){this.db.close()}getStats(){const e=this.db.prepare("SELECT COUNT(*) as count FROM products").get(),t=this.db.prepare("SELECT COUNT(*) as count FROM users").get(),n=this.db.prepare("SELECT COUNT(*) as count FROM sales").get(),i=this.db.prepare("SELECT COUNT(*) as count FROM employee_payments").get();return{products:e.count,users:t.count,sales:n.count,employeePayments:i.count,dbSize:0}}getEmployees(){this.checkAvailability();return this.db.prepare("SELECT * FROM employees ORDER BY nom ASC").all()}addEmployee(e){this.checkAvailability();this.db.prepare("\n      INSERT INTO employees (\n        id, nomComplet, poste, salaireCDF, salaireUSD, dateEmbauche,\n        telephone, adresse, statut, notes, creePar, dateCreation, dateModification\n      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)\n    ").run(e.id,e.nomComplet,e.poste,e.salaireCDF,e.salaireUSD,e.dateEmbauche,e.telephone,e.adresse,e.statut,e.notes,e.creePar,e.dateCreation,e.dateModification)}updateEmployee(e){this.checkAvailability();this.db.prepare("\n      UPDATE employees SET\n        nomComplet = ?, poste = ?, salaireCDF = ?, salaireUSD = ?,\n        dateEmbauche = ?, telephone = ?, adresse = ?, statut = ?, notes = ?,\n        dateModification = ?\n      WHERE id = ?\n    ").run(e.nomComplet,e.poste,e.salaireCDF,e.salaireUSD,e.dateEmbauche,e.telephone,e.adresse,e.statut,e.notes,e.dateModification,e.id)}deleteEmployee(e){this.checkAvailability();this.db.prepare("DELETE FROM employees WHERE id = ?").run(e)}}const nr=new tr,ir=Object.freeze(Object.defineProperty({__proto__:null,SQLiteStorageService:tr,sqliteStorageService:nr},Symbol.toStringTag,{value:"Module"}));class rr{constructor(e){this.sqlite=e,this._connectionDict=new Map}async initWebStore(){try{return await this.sqlite.initWebStore(),Promise.resolve()}catch(e){return Promise.reject(e)}}async saveToStore(e){try{return await this.sqlite.saveToStore({database:e}),Promise.resolve()}catch(t){return Promise.reject(t)}}async saveToLocalDisk(e){try{return await this.sqlite.saveToLocalDisk({database:e}),Promise.resolve()}catch(t){return Promise.reject(t)}}async getFromLocalDiskToStore(e){const t=null==e||e;try{return await this.sqlite.getFromLocalDiskToStore({overwrite:t}),Promise.resolve()}catch(n){return Promise.reject(n)}}async echo(e){try{const t=await this.sqlite.echo({value:e});return Promise.resolve(t)}catch(t){return Promise.reject(t)}}async isSecretStored(){try{const e=await this.sqlite.isSecretStored();return Promise.resolve(e)}catch(e){return Promise.reject(e)}}async setEncryptionSecret(e){try{return await this.sqlite.setEncryptionSecret({passphrase:e}),Promise.resolve()}catch(t){return Promise.reject(t)}}async changeEncryptionSecret(e,t){try{return await this.sqlite.changeEncryptionSecret({passphrase:e,oldpassphrase:t}),Promise.resolve()}catch(n){return Promise.reject(n)}}async clearEncryptionSecret(){try{return await this.sqlite.clearEncryptionSecret(),Promise.resolve()}catch(e){return Promise.reject(e)}}async checkEncryptionSecret(e){try{const t=await this.sqlite.checkEncryptionSecret({passphrase:e});return Promise.resolve(t)}catch(t){return Promise.reject(t)}}async addUpgradeStatement(e,t){try{return e.endsWith(".db")&&(e=e.slice(0,-3)),await this.sqlite.addUpgradeStatement({database:e,upgrade:t}),Promise.resolve()}catch(n){return Promise.reject(n)}}async createConnection(e,t,n,i,r){try{e.endsWith(".db")&&(e=e.slice(0,-3)),await this.sqlite.createConnection({database:e,encrypted:t,mode:n,version:i,readonly:r});const a=new ar(e,r,this.sqlite),s=r?`RO_${e}`:`RW_${e}`;return this._connectionDict.set(s,a),Promise.resolve(a)}catch(a){return Promise.reject(a)}}async closeConnection(e,t){try{e.endsWith(".db")&&(e=e.slice(0,-3)),await this.sqlite.closeConnection({database:e,readonly:t});const n=t?`RO_${e}`:`RW_${e}`;return this._connectionDict.delete(n),Promise.resolve()}catch(n){return Promise.reject(n)}}async isConnection(e,t){const n={};e.endsWith(".db")&&(e=e.slice(0,-3));const i=t?`RO_${e}`:`RW_${e}`;return n.result=this._connectionDict.has(i),Promise.resolve(n)}async retrieveConnection(e,t){e.endsWith(".db")&&(e=e.slice(0,-3));const n=t?`RO_${e}`:`RW_${e}`;if(this._connectionDict.has(n)){const t=this._connectionDict.get(n);return void 0!==t?Promise.resolve(t):Promise.reject(`Connection ${e} is undefined`)}return Promise.reject(`Connection ${e} does not exist`)}async getNCDatabasePath(e,t){try{const n=await this.sqlite.getNCDatabasePath({path:e,database:t});return Promise.resolve(n)}catch(n){return Promise.reject(n)}}async createNCConnection(e,t){try{await this.sqlite.createNCConnection({databasePath:e,version:t});const n=new ar(e,!0,this.sqlite),i=`RO_${e})`;return this._connectionDict.set(i,n),Promise.resolve(n)}catch(n){return Promise.reject(n)}}async closeNCConnection(e){try{await this.sqlite.closeNCConnection({databasePath:e});const t=`RO_${e})`;return this._connectionDict.delete(t),Promise.resolve()}catch(t){return Promise.reject(t)}}async isNCConnection(e){const t={},n=`RO_${e})`;return t.result=this._connectionDict.has(n),Promise.resolve(t)}async retrieveNCConnection(e){if(this._connectionDict.has(e)){const t=`RO_${e})`,n=this._connectionDict.get(t);return void 0!==n?Promise.resolve(n):Promise.reject(`Connection ${e} is undefined`)}return Promise.reject(`Connection ${e} does not exist`)}async isNCDatabase(e){try{const t=await this.sqlite.isNCDatabase({databasePath:e});return Promise.resolve(t)}catch(t){return Promise.reject(t)}}async retrieveAllConnections(){return this._connectionDict}async closeAllConnections(){const e=new Map;try{for(const t of this._connectionDict.keys()){const n=t.substring(3),i="RO_"===t.substring(0,3);await this.sqlite.closeConnection({database:n,readonly:i}),e.set(t,null)}for(const t of e.keys())this._connectionDict.delete(t);return Promise.resolve()}catch(t){return Promise.reject(t)}}async checkConnectionsConsistency(){try{const e=[...this._connectionDict.keys()],t=[],n=[];for(const r of e)t.push(r.substring(0,2)),n.push(r.substring(3));const i=await this.sqlite.checkConnectionsConsistency({dbNames:n,openModes:t});return i.result||(this._connectionDict=new Map),Promise.resolve(i)}catch(e){return this._connectionDict=new Map,Promise.reject(e)}}async importFromJson(e){try{const t=await this.sqlite.importFromJson({jsonstring:e});return Promise.resolve(t)}catch(t){return Promise.reject(t)}}async isJsonValid(e){try{const t=await this.sqlite.isJsonValid({jsonstring:e});return Promise.resolve(t)}catch(t){return Promise.reject(t)}}async copyFromAssets(e){const t=null==e||e;try{return await this.sqlite.copyFromAssets({overwrite:t}),Promise.resolve()}catch(n){return Promise.reject(n)}}async getFromHTTPRequest(e,t){const n=null==t||t;try{return await this.sqlite.getFromHTTPRequest({url:e,overwrite:n}),Promise.resolve()}catch(i){return Promise.reject(i)}}async isDatabaseEncrypted(e){e.endsWith(".db")&&(e=e.slice(0,-3));try{const t=await this.sqlite.isDatabaseEncrypted({database:e});return Promise.resolve(t)}catch(t){return Promise.reject(t)}}async isInConfigEncryption(){try{const e=await this.sqlite.isInConfigEncryption();return Promise.resolve(e)}catch(e){return Promise.reject(e)}}async isInConfigBiometricAuth(){try{const e=await this.sqlite.isInConfigBiometricAuth();return Promise.resolve(e)}catch(e){return Promise.reject(e)}}async isDatabase(e){e.endsWith(".db")&&(e=e.slice(0,-3));try{const t=await this.sqlite.isDatabase({database:e});return Promise.resolve(t)}catch(t){return Promise.reject(t)}}async getDatabaseList(){try{const e=(await this.sqlite.getDatabaseList()).values;e.sort();const t={values:e};return Promise.resolve(t)}catch(e){return Promise.reject(e)}}async getMigratableDbList(e){const t=e||"default";try{const e=await this.sqlite.getMigratableDbList({folderPath:t});return Promise.resolve(e)}catch(n){return Promise.reject(n)}}async addSQLiteSuffix(e,t){const n=e||"default",i=t||[];try{const e=await this.sqlite.addSQLiteSuffix({folderPath:n,dbNameList:i});return Promise.resolve(e)}catch(r){return Promise.reject(r)}}async deleteOldDatabases(e,t){const n=e||"default",i=t||[];try{const e=await this.sqlite.deleteOldDatabases({folderPath:n,dbNameList:i});return Promise.resolve(e)}catch(r){return Promise.reject(r)}}async moveDatabasesAndAddSuffix(e,t){const n=e||"default",i=t||[];return this.sqlite.moveDatabasesAndAddSuffix({folderPath:n,dbNameList:i})}}class ar{constructor(e,t,n){this.dbName=e,this.readonly=t,this.sqlite=n}getConnectionDBName(){return this.dbName}getConnectionReadOnly(){return this.readonly}async open(){try{return await this.sqlite.open({database:this.dbName,readonly:this.readonly}),Promise.resolve()}catch(e){return Promise.reject(e)}}async close(){try{return await this.sqlite.close({database:this.dbName,readonly:this.readonly}),Promise.resolve()}catch(e){return Promise.reject(e)}}async beginTransaction(){try{const e=await this.sqlite.beginTransaction({database:this.dbName});return Promise.resolve(e)}catch(e){return Promise.reject(e)}}async commitTransaction(){try{const e=await this.sqlite.commitTransaction({database:this.dbName});return Promise.resolve(e)}catch(e){return Promise.reject(e)}}async rollbackTransaction(){try{const e=await this.sqlite.rollbackTransaction({database:this.dbName});return Promise.resolve(e)}catch(e){return Promise.reject(e)}}async isTransactionActive(){try{const e=await this.sqlite.isTransactionActive({database:this.dbName});return Promise.resolve(e)}catch(e){return Promise.reject(e)}}async loadExtension(e){try{return await this.sqlite.loadExtension({database:this.dbName,path:e,readonly:this.readonly}),Promise.resolve()}catch(t){return Promise.reject(t)}}async enableLoadExtension(e){try{return await this.sqlite.enableLoadExtension({database:this.dbName,toggle:e,readonly:this.readonly}),Promise.resolve()}catch(t){return Promise.reject(t)}}async getUrl(){try{const e=await this.sqlite.getUrl({database:this.dbName,readonly:this.readonly});return Promise.resolve(e)}catch(e){return Promise.reject(e)}}async getVersion(){try{const e=await this.sqlite.getVersion({database:this.dbName,readonly:this.readonly});return Promise.resolve(e)}catch(e){return Promise.reject(e)}}async getTableList(){try{const e=await this.sqlite.getTableList({database:this.dbName,readonly:this.readonly});return Promise.resolve(e)}catch(e){return Promise.reject(e)}}async execute(e,t=!0,n=!0){try{if(this.readonly)return Promise.reject("not allowed in read-only mode");{const i=await this.sqlite.execute({database:this.dbName,statements:e,transaction:t,readonly:!1,isSQL92:n});return Promise.resolve(i)}}catch(i){return Promise.reject(i)}}async query(e,t,n=!0){let i;try{return i=t&&t.length>0?await this.sqlite.query({database:this.dbName,statement:e,values:t,readonly:this.readonly,isSQL92:!0}):await this.sqlite.query({database:this.dbName,statement:e,values:[],readonly:this.readonly,isSQL92:n}),i=await this.reorderRows(i),Promise.resolve(i)}catch(r){return Promise.reject(r)}}async run(e,t,n=!0,i="no",r=!0){let a;try{return this.readonly?Promise.reject("not allowed in read-only mode"):(a=t&&t.length>0?await this.sqlite.run({database:this.dbName,statement:e,values:t,transaction:n,readonly:!1,returnMode:i,isSQL92:!0}):await this.sqlite.run({database:this.dbName,statement:e,values:[],transaction:n,readonly:!1,returnMode:i,isSQL92:r}),a.changes=await this.reorderRows(a.changes),Promise.resolve(a))}catch(s){return Promise.reject(s)}}async executeSet(e,t=!0,n="no",i=!0){let r;try{return this.readonly?Promise.reject("not allowed in read-only mode"):(r=await this.sqlite.executeSet({database:this.dbName,set:e,transaction:t,readonly:!1,returnMode:n,isSQL92:i}),r.changes=await this.reorderRows(r.changes),Promise.resolve(r))}catch(a){return Promise.reject(a)}}async isExists(){try{const e=await this.sqlite.isDBExists({database:this.dbName,readonly:this.readonly});return Promise.resolve(e)}catch(e){return Promise.reject(e)}}async isTable(e){try{const t=await this.sqlite.isTableExists({database:this.dbName,table:e,readonly:this.readonly});return Promise.resolve(t)}catch(t){return Promise.reject(t)}}async isDBOpen(){try{const e=await this.sqlite.isDBOpen({database:this.dbName,readonly:this.readonly});return Promise.resolve(e)}catch(e){return Promise.reject(e)}}async delete(){try{return this.readonly?Promise.reject("not allowed in read-only mode"):(await this.sqlite.deleteDatabase({database:this.dbName,readonly:!1}),Promise.resolve())}catch(e){return Promise.reject(e)}}async createSyncTable(){try{if(this.readonly)return Promise.reject("not allowed in read-only mode");{const e=await this.sqlite.createSyncTable({database:this.dbName,readonly:!1});return Promise.resolve(e)}}catch(e){return Promise.reject(e)}}async setSyncDate(e){try{return this.readonly?Promise.reject("not allowed in read-only mode"):(await this.sqlite.setSyncDate({database:this.dbName,syncdate:e,readonly:!1}),Promise.resolve())}catch(t){return Promise.reject(t)}}async getSyncDate(){try{const e=await this.sqlite.getSyncDate({database:this.dbName,readonly:this.readonly});let t="";return e.syncDate>0&&(t=new Date(1e3*e.syncDate).toISOString()),Promise.resolve(t)}catch(e){return Promise.reject(e)}}async exportToJson(e,t=!1){try{const n=await this.sqlite.exportToJson({database:this.dbName,jsonexportmode:e,readonly:this.readonly,encrypted:t});return Promise.resolve(n)}catch(n){return Promise.reject(n)}}async deleteExportedRows(){try{return this.readonly?Promise.reject("not allowed in read-only mode"):(await this.sqlite.deleteExportedRows({database:this.dbName,readonly:!1}),Promise.resolve())}catch(e){return Promise.reject(e)}}async executeTransaction(e,t=!0){let n=0,i=!1;if(this.readonly)return Promise.reject("not allowed in read-only mode");if(await this.sqlite.beginTransaction({database:this.dbName}),i=await this.sqlite.isTransactionActive({database:this.dbName}),!i)return Promise.reject("After Begin Transaction, no transaction active");try{for(const r of e){if("object"!=typeof r||!("statement"in r))throw new Error("Error a task.statement must be provided");if("values"in r&&r.values&&r.values.length>0){const e=r.statement.toUpperCase().includes("RETURNING")?"all":"no",i=await this.sqlite.run({database:this.dbName,statement:r.statement,values:r.values,transaction:!1,readonly:!1,returnMode:e,isSQL92:t});if(i.changes.changes<0)throw new Error("Error in transaction method run ");n+=i.changes.changes}else{const e=await this.sqlite.execute({database:this.dbName,statements:r.statement,transaction:!1,readonly:!1});if(e.changes.changes<0)throw new Error("Error in transaction method execute ");n+=e.changes.changes}}n+=(await this.sqlite.commitTransaction({database:this.dbName})).changes.changes;const i={changes:{changes:n}};return Promise.resolve(i)}catch(r){const e=r.message?r.message:r;return await this.sqlite.rollbackTransaction({database:this.dbName}),Promise.reject(e)}}async reorderRows(e){const t=e;if((null==e?void 0:e.values)&&"object"==typeof e.values[0]&&Object.keys(e.values[0]).includes("ios_columns")){const n=e.values[0].ios_columns,i=[];for(let t=1;t<e.values.length;t++){const r=e.values[t],a={};for(const e of n)a[e]=r[e];i.push(a)}t.values=i}return Promise.resolve(t)}}const sr=Fi("CapacitorSQLite",{web:()=>Si(()=>import("./web-BGthZxH_.js"),__vite__mapDeps([5,1,2,3,4]),import.meta.url).then(e=>new e.CapacitorSQLiteWeb),electron:()=>window.CapacitorCustomPlatform.plugins.CapacitorSQLite});class or{constructor(){n(this,"sqlite"),n(this,"db",null),n(this,"DB_NAME","smartboutique.db"),n(this,"DB_VERSION",1),n(this,"isInitialized",!1),this.sqlite=new rr(sr)}async initialize(){if(!this.isInitialized)try{if(console.log("Initializing mobile SQLite database..."),!Pi.isNativePlatform())throw new Error("SQLite is only supported on native platforms (Android/iOS)");const e=await this.sqlite.checkConnectionsConsistency(),t=(await this.sqlite.isConnection(this.DB_NAME,!1)).result;e.result&&t?this.db=await this.sqlite.retrieveConnection(this.DB_NAME,!1):this.db=await this.sqlite.createConnection(this.DB_NAME,!1,"no-encryption",this.DB_VERSION,!1),await this.db.open(),await this.createTables(),await this.createIndexes(),this.isInitialized=!0,console.log("Mobile SQLite database initialized successfully")}catch(ba){throw console.error("Failed to initialize mobile SQLite database:",ba),ba}}async createTables(){if(!this.db)throw new Error("Database not initialized");const e=["CREATE TABLE IF NOT EXISTS products (\n        id TEXT PRIMARY KEY,\n        nom TEXT NOT NULL,\n        description TEXT,\n        prixAchatCDF REAL NOT NULL,\n        prixAchatUSD REAL,\n        prixCDF REAL NOT NULL,\n        prixUSD REAL,\n        beneficeUnitaireCDF REAL,\n        beneficeUnitaireUSD REAL,\n        codeQR TEXT,\n        categorie TEXT,\n        stock INTEGER,\n        stockMin INTEGER,\n        codeBarres TEXT,\n        dateCreation TEXT,\n        dateModification TEXT,\n        quantiteEnStock INTEGER,\n        coutAchatStockCDF REAL,\n        coutAchatStockUSD REAL,\n        prixParPieceCDF REAL,\n        prixParPieceUSD REAL\n      );","CREATE TABLE IF NOT EXISTS users (\n        id TEXT PRIMARY KEY,\n        nom TEXT NOT NULL,\n        email TEXT NOT NULL UNIQUE,\n        role TEXT NOT NULL,\n        motDePasse TEXT NOT NULL,\n        dateCreation TEXT,\n        actif INTEGER DEFAULT 1\n      );","CREATE TABLE IF NOT EXISTS sales (\n        id TEXT PRIMARY KEY,\n        date TEXT NOT NULL,\n        client TEXT,\n        produits TEXT NOT NULL,\n        totalCDF REAL NOT NULL,\n        totalUSD REAL,\n        typePaiement TEXT NOT NULL,\n        typeVente TEXT NOT NULL,\n        vendeur TEXT NOT NULL,\n        numeroRecu TEXT\n      );","CREATE TABLE IF NOT EXISTS debts (\n        id TEXT PRIMARY KEY,\n        client TEXT NOT NULL,\n        montantCDF REAL NOT NULL,\n        montantUSD REAL,\n        dateCreation TEXT NOT NULL,\n        dateEcheance TEXT,\n        statut TEXT NOT NULL,\n        description TEXT,\n        vendeur TEXT NOT NULL\n      );","CREATE TABLE IF NOT EXISTS expenses (\n        id TEXT PRIMARY KEY,\n        description TEXT NOT NULL,\n        montantCDF REAL NOT NULL,\n        montantUSD REAL,\n        date TEXT NOT NULL,\n        categorie TEXT NOT NULL,\n        utilisateur TEXT NOT NULL,\n        numeroRecu TEXT\n      );","CREATE TABLE IF NOT EXISTS settings (\n        key TEXT PRIMARY KEY,\n        value TEXT NOT NULL\n      );"];for(const t of e)await this.db.execute(t)}async createIndexes(){if(!this.db)throw new Error("Database not initialized");const e=["CREATE INDEX IF NOT EXISTS idx_products_categorie ON products(categorie);","CREATE INDEX IF NOT EXISTS idx_products_stock ON products(stock);","CREATE INDEX IF NOT EXISTS idx_sales_date ON sales(date);","CREATE INDEX IF NOT EXISTS idx_sales_vendeur ON sales(vendeur);","CREATE INDEX IF NOT EXISTS idx_debts_statut ON debts(statut);","CREATE INDEX IF NOT EXISTS idx_debts_client ON debts(client);","CREATE INDEX IF NOT EXISTS idx_expenses_date ON expenses(date);","CREATE INDEX IF NOT EXISTS idx_expenses_categorie ON expenses(categorie);"];for(const t of e)await this.db.execute(t)}async ensureInitialized(){this.isInitialized||await this.initialize()}async getProducts(){if(await this.ensureInitialized(),!this.db)throw new Error("Database not initialized");return(await this.db.query("SELECT * FROM products ORDER BY nom")).values}async getProduct(e){var t;if(await this.ensureInitialized(),!this.db)throw new Error("Database not initialized");return null==(t=(await this.db.query("SELECT * FROM products WHERE id = ?",[e])).values)?void 0:t[0]}async setProducts(e){if(await this.ensureInitialized(),!this.db)throw new Error("Database not initialized");await this.db.beginTransaction();try{await this.db.run("DELETE FROM products");for(const t of e)await this.db.run("INSERT INTO products (\n            id, nom, description, prixAchatCDF, prixAchatUSD, prixCDF, prixUSD,\n            beneficeUnitaireCDF, beneficeUnitaireUSD, codeQR, categorie, stock,\n            stockMin, codeBarres, dateCreation, dateModification\n          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",[t.id,t.nom,t.description,t.prixAchatCDF,t.prixAchatUSD,t.prixCDF,t.prixUSD,t.beneficeUnitaireCDF,t.beneficeUnitaireUSD,t.codeQR,t.categorie,t.stock,t.stockMin,t.codeBarres,t.dateCreation,t.dateModification]);await this.db.commitTransaction()}catch(ba){throw await this.db.rollbackTransaction(),ba}}async addProduct(e){if(await this.ensureInitialized(),!this.db)throw new Error("Database not initialized");await this.db.run("INSERT INTO products (\n        id, nom, description, prixAchatCDF, prixAchatUSD, prixCDF, prixUSD,\n        beneficeUnitaireCDF, beneficeUnitaireUSD, codeQR, categorie, stock,\n        stockMin, codeBarres, dateCreation, dateModification\n      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",[e.id,e.nom,e.description,e.prixAchatCDF,e.prixAchatUSD,e.prixCDF,e.prixUSD,e.beneficeUnitaireCDF,e.beneficeUnitaireUSD,e.codeQR,e.categorie,e.stock,e.stockMin,e.codeBarres,e.dateCreation,e.dateModification])}async updateProduct(e){if(await this.ensureInitialized(),!this.db)throw new Error("Database not initialized");await this.db.run("UPDATE products SET\n        nom = ?, description = ?, prixAchatCDF = ?, prixAchatUSD = ?,\n        prixCDF = ?, prixUSD = ?, beneficeUnitaireCDF = ?, beneficeUnitaireUSD = ?,\n        codeQR = ?, categorie = ?, stock = ?, stockMin = ?, codeBarres = ?,\n        dateModification = ?\n      WHERE id = ?",[e.nom,e.description,e.prixAchatCDF,e.prixAchatUSD,e.prixCDF,e.prixUSD,e.beneficeUnitaireCDF,e.beneficeUnitaireUSD,e.codeQR,e.categorie,e.stock,e.stockMin,e.codeBarres,e.dateModification,e.id])}async deleteProduct(e){if(await this.ensureInitialized(),!this.db)throw new Error("Database not initialized");await this.db.run("DELETE FROM products WHERE id = ?",[e])}async getUsers(){if(await this.ensureInitialized(),!this.db)throw new Error("Database not initialized");return(await this.db.query("SELECT * FROM users ORDER BY nom")).values.map(e=>({...e,actif:Boolean(e.actif)}))}async setUsers(e){if(await this.ensureInitialized(),!this.db)throw new Error("Database not initialized");await this.db.beginTransaction();try{await this.db.run("DELETE FROM users");for(const t of e)await this.db.run("INSERT INTO users (id, nom, email, role, motDePasse, dateCreation, actif) VALUES (?, ?, ?, ?, ?, ?, ?)",[t.id,t.nom,t.email,t.role,t.motDePasse,t.dateCreation,t.actif?1:0]);await this.db.commitTransaction()}catch(ba){throw await this.db.rollbackTransaction(),ba}}async getSales(){if(await this.ensureInitialized(),!this.db)throw new Error("Database not initialized");return(await this.db.query("SELECT * FROM sales ORDER BY date DESC")).values.map(e=>({...e,produits:JSON.parse(e.produits),datevente:e.date,nomClient:e.client,methodePaiement:e.typePaiement}))}async setSales(e){if(await this.ensureInitialized(),!this.db)throw new Error("Database not initialized");await this.db.beginTransaction();try{await this.db.run("DELETE FROM sales");for(const t of e)await this.db.run("INSERT INTO sales (id, date, client, produits, totalCDF, totalUSD, typePaiement, typeVente, vendeur, numeroRecu) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",[t.id,t.datevente,t.nomClient,JSON.stringify(t.produits),t.totalCDF,t.totalUSD,t.methodePaiement,t.typeVente,t.vendeur,t.numeroRecu]);await this.db.commitTransaction()}catch(ba){throw await this.db.rollbackTransaction(),ba}}async close(){this.db&&(await this.db.close(),await this.sqlite.closeConnection(this.DB_NAME,!1),this.db=null,this.isInitialized=!1)}async getStats(){var e,t,n,i,r,a;if(await this.ensureInitialized(),!this.db)throw new Error("Database not initialized");const[s,o,l]=await Promise.all([this.db.query("SELECT COUNT(*) as count FROM products"),this.db.query("SELECT COUNT(*) as count FROM users"),this.db.query("SELECT COUNT(*) as count FROM sales")]);return{products:(null==(t=null==(e=s.values)?void 0:e[0])?void 0:t.count)||0,users:(null==(i=null==(n=o.values)?void 0:n[0])?void 0:i.count)||0,sales:(null==(a=null==(r=l.values)?void 0:r[0])?void 0:a.count)||0,dbSize:0}}async clearAllData(){if(await this.ensureInitialized(),!this.db)throw new Error("Database not initialized");try{await this.db.execute("DELETE FROM products"),await this.db.execute("DELETE FROM users"),await this.db.execute("DELETE FROM sales"),await this.db.execute("DELETE FROM debts"),await this.db.execute("DELETE FROM expenses"),await this.db.execute("DELETE FROM settings"),await this.db.execute("DELETE FROM sqlite_sequence"),console.log("✅ All mobile SQLite data cleared successfully")}catch(ba){throw console.error("❌ Error clearing mobile SQLite data:",ba),ba}}}const lr=new or,cr=Object.freeze(Object.defineProperty({__proto__:null,MobileSQLiteStorageService:or,mobileSQLiteStorageService:lr},Symbol.toStringTag,{value:"Module"}));const dr=new class{constructor(){n(this,"MIGRATION_FLAG_KEY","smartboutique_mobile_migrated_to_sqlite")}async isMigrationCompleted(){return"true"===(await Ji.get({key:this.MIGRATION_FLAG_KEY})).value}async markMigrationCompleted(){await Ji.set({key:this.MIGRATION_FLAG_KEY,value:"true"})}async migrateToSQLite(){const e={success:!1,message:"",migratedCounts:{products:0,users:0,sales:0,debts:0,expenses:0},errors:[]};try{if(console.log("Starting mobile migration from CSV to SQLite..."),await this.isMigrationCompleted())return e.success=!0,e.message="Migration mobile déjà effectuée",e;await lr.initialize();const t=await this.extractCSVData();if(!this.validateCSVData(t))return e.errors.push("Données CSV mobiles invalides ou corrompues"),e.message="Échec de la validation des données CSV mobiles",e;await this.createBackup(t),await this.performMigration(t,e),await this.verifyMigration(t,e)?(await this.markMigrationCompleted(),e.success=!0,e.message=`Migration mobile réussie: ${e.migratedCounts.products} produits, ${e.migratedCounts.users} utilisateurs, ${e.migratedCounts.sales} ventes migrées`):e.message="Échec de la vérification de la migration mobile"}catch(ba){console.error("Mobile migration error:",ba),e.errors.push(`Erreur de migration mobile: ${ba.message}`),e.message="Échec de la migration mobile"}return e}async extractCSVData(){return console.log("Extracting data from CSV Capacitor Preferences..."),{products:await Gi.getProducts()||[],users:await Gi.getUsers()||[],sales:await Gi.getSales()||[],debts:await Gi.getDebts()||[],expenses:await Gi.getExpenses()||[]}}validateCSVData(e){try{if(!e||"object"!=typeof e)return!1;const{products:t,users:n,sales:i,debts:r,expenses:a}=e;if(t&&Array.isArray(t))for(const e of t)if(!e.id||!e.nom||"number"!=typeof e.prixCDF)return console.warn("Invalid mobile product found:",e),!1;if(n&&Array.isArray(n))for(const e of n)if(!(e.id&&e.nom&&e.email&&e.role))return console.warn("Invalid mobile user found:",e),!1;if(i&&Array.isArray(i))for(const e of i)if(!e.id||!e.datevente||!Array.isArray(e.produits))return console.warn("Invalid mobile sale found:",e),!1;return!0}catch(ba){return console.error("Mobile data validation error:",ba),!1}}async createBackup(e){try{const t={timestamp:(new Date).toISOString(),platform:"mobile",data:e};await Ji.set({key:"smartboutique_mobile_csv_backup",value:JSON.stringify(t)}),console.log("Mobile backup created successfully")}catch(ba){throw console.error("Mobile backup creation failed:",ba),new Error("Impossible de créer une sauvegarde mobile")}}async performMigration(e,t){try{const{products:n,users:i,sales:r,debts:a,expenses:s}=e;n&&n.length>0&&(await lr.setProducts(n),t.migratedCounts.products=n.length,console.log(`Migrated ${n.length} mobile products`)),i&&i.length>0&&(await lr.setUsers(i),t.migratedCounts.users=i.length,console.log(`Migrated ${i.length} mobile users`)),r&&r.length>0&&(await lr.setSales(r),t.migratedCounts.sales=r.length,console.log(`Migrated ${r.length} mobile sales`)),console.log("Mobile migration to SQLite completed")}catch(ba){throw console.error("Mobile migration execution failed:",ba),new Error(`Échec de la migration mobile: ${ba.message}`)}}async verifyMigration(e,t){try{const n=await lr.getStats();return e.products&&e.products.length!==n.products?(t.errors.push(`Nombre de produits mobile incorrect: attendu ${e.products.length}, trouvé ${n.products}`),!1):e.users&&e.users.length!==n.users?(t.errors.push(`Nombre d'utilisateurs mobile incorrect: attendu ${e.users.length}, trouvé ${n.users}`),!1):e.sales&&e.sales.length!==n.sales?(t.errors.push(`Nombre de ventes mobile incorrect: attendu ${e.sales.length}, trouvé ${n.sales}`),!1):(console.log("Mobile migration verification successful"),!0)}catch(ba){return console.error("Mobile migration verification failed:",ba),t.errors.push(`Échec de la vérification mobile: ${ba.message}`),!1}}async rollbackMigration(){try{console.log("Rolling back mobile migration...");const e=await Ji.get({key:"smartboutique_mobile_csv_backup"});if(!e.value)return console.error("No mobile backup found for rollback"),!1;const t=JSON.parse(e.value),{data:n}=t;return n.products&&await Gi.setProducts(n.products),n.users&&await Gi.setUsers(n.users),n.sales&&await Gi.setSales(n.sales),n.debts&&await Gi.setDebts(n.debts),n.expenses&&await Gi.setExpenses(n.expenses),await Ji.remove({key:this.MIGRATION_FLAG_KEY}),await lr.close(),console.log("Mobile migration rollback completed"),!0}catch(ba){return console.error("Mobile rollback failed:",ba),!1}}async getMigrationStatus(){const e=await this.isMigrationCompleted(),t=await Ji.get({key:"smartboutique_mobile_csv_backup"}),n=await Ji.get({key:"smartboutique_csv_products"});return{isCompleted:e,sqliteStats:e?await lr.getStats():void 0,csvDataExists:Boolean(n.value),backupExists:Boolean(t.value)}}async cleanupOldData(){if(!(await this.isMigrationCompleted()))return void console.warn("Cannot cleanup mobile data: migration not completed");const e=["smartboutique_csv_products","smartboutique_csv_users","smartboutique_csv_sales","smartboutique_csv_debts","smartboutique_csv_expenses","smartboutique_csv_settings"];for(const t of e)await Ji.remove({key:t});console.log("Old mobile CSV data cleaned up")}async forceMigration(){return await Ji.remove({key:this.MIGRATION_FLAG_KEY}),this.migrateToSQLite()}};const ur=new class{constructor(){n(this,"migrationChecked",!1),n(this,"mobileMigrationChecked",!1)}get storage(){return Ni()?Gi:Qi}get sqliteStorage(){return Ni()?lr:nr}async checkDesktopMigration(){if(!this.migrationChecked&&!Ni()){this.migrationChecked=!0;try{return void console.log("SQLite migration temporarily disabled - using CSV storage")}catch(ba){console.error("Desktop migration check failed:",ba)}}}async checkMobileMigration(){if(!this.mobileMigrationChecked&&Ni()){this.mobileMigrationChecked=!0;try{if(!(await dr.isMigrationCompleted())){console.log("Starting automatic mobile migration to SQLite...");const e=await dr.migrateToSQLite();e.success?console.log("Mobile migration completed successfully:",e.message):(console.error("Mobile migration failed:",e.message,e.errors),console.log("Falling back to CSV storage on mobile"))}}catch(ba){console.error("Mobile migration check failed:",ba),console.log("Falling back to CSV storage on mobile due to error")}}}async checkMigration(){Ni()?await this.checkMobileMigration():await this.checkDesktopMigration()}async set(e,t){Ni()?console.warn("Generic set method not available in CSV storage"):Qi.set(e,t)}async get(e,t){return Ni()?(console.warn("Generic get method not available in CSV storage"),t):Qi.get(e,t)}async remove(e){Ni()?console.warn("Generic remove method not available in CSV storage"):Qi.remove(e)}async clear(){Ni()?await this.clearMobileData():await this.clearDesktopData()}async clearMobileData(){try{await Gi.clearAllData();const{Preferences:e}=await Si(async()=>{const{Preferences:e}=await Promise.resolve().then(()=>Hi);return{Preferences:e}},void 0,import.meta.url),{keys:t}=await e.keys(),n=t.filter(e=>e.startsWith("smartboutique_")||e.startsWith("smartboutique_csv_"));for(const i of n)await e.remove({key:i});try{await dr.isMigrationCompleted()&&await this.sqliteStorage.clearAllData()}catch(ba){console.warn("Mobile SQLite clear failed or not available:",ba)}console.log("✅ Mobile data cleared successfully")}catch(ba){throw console.error("❌ Error clearing mobile data:",ba),ba}}async clearDesktopData(){try{Qi.clear();try{nr.clearAllData()}catch(ba){console.warn("Desktop SQLite clear failed or not available:",ba)}console.log("✅ Desktop data cleared successfully")}catch(ba){throw console.error("❌ Error clearing desktop data:",ba),ba}}async getUsers(){if(await this.checkMigration(),Ni()){try{if(await dr.isMigrationCompleted())return await this.sqliteStorage.getUsers()}catch(ba){console.warn("Mobile SQLite failed, falling back to CSV:",ba)}return await Gi.getUsers()}return Qi.getUsers()}async setUsers(e){if(await this.checkMigration(),Ni()){try{if(await dr.isMigrationCompleted())return void(await this.sqliteStorage.setUsers(e))}catch(ba){console.warn("Mobile SQLite failed, falling back to CSV:",ba)}await Gi.setUsers(e)}else Qi.setUsers(e)}async getProducts(){if(await this.checkMigration(),Ni()){try{if(await dr.isMigrationCompleted())return await this.sqliteStorage.getProducts()}catch(ba){console.warn("Mobile SQLite failed, falling back to CSV:",ba)}return await Gi.getProducts()}return Qi.getProducts()}async setProducts(e){if(await this.checkMigration(),Ni()){try{if(await dr.isMigrationCompleted())return void(await this.sqliteStorage.setProducts(e))}catch(ba){console.warn("Mobile SQLite failed, falling back to CSV:",ba)}await Gi.setProducts(e)}else Qi.setProducts(e)}async getSales(){if(await this.checkMigration(),Ni()){try{if(await dr.isMigrationCompleted())return await this.sqliteStorage.getSales()}catch(ba){console.warn("Mobile SQLite failed, falling back to CSV:",ba)}return await Gi.getSales()}return Qi.getSales()}async setSales(e){if(await this.checkMigration(),Ni()){try{if(await dr.isMigrationCompleted())return void(await this.sqliteStorage.setSales(e))}catch(ba){console.warn("Mobile SQLite failed, falling back to CSV:",ba)}await Gi.setSales(e)}else Qi.setSales(e)}async getDebts(){return Ni()?await Gi.getDebts():Qi.getDebts()}async setDebts(e){Ni()?await Gi.setDebts(e):Qi.setDebts(e)}async getDettes(){return this.getDebts()}async setDettes(e){await this.setDebts(e)}async getCreances(){return this.getDebts()}async setCreances(e){await this.setDebts(e)}async getExpenses(){return Ni()?await Gi.getExpenses():Qi.getExpenses()}async setExpenses(e){Ni()?await Gi.setExpenses(e):Qi.setExpenses(e)}async getEmployeePayments(){var e,t;if(Ni())return await(null==(e=Gi.getEmployeePayments)?void 0:e.call(Gi))||[];try{return nr.getEmployeePayments()}catch(ba){return console.warn("SQLite not available for employee payments, using localStorage:",ba),(null==(t=Qi.getEmployeePayments)?void 0:t.call(Qi))||[]}}async addEmployeePayment(e){if(Ni())Gi.addEmployeePayment&&await Gi.addEmployeePayment(e);else try{nr.addEmployeePayment(e)}catch(ba){console.warn("SQLite not available for employee payments, using localStorage:",ba),Qi.addEmployeePayment&&Qi.addEmployeePayment(e)}}async updateEmployeePayment(e){if(Ni())Gi.updateEmployeePayment&&await Gi.updateEmployeePayment(e);else try{nr.updateEmployeePayment(e)}catch(ba){console.warn("SQLite not available for employee payments, using localStorage:",ba),Qi.updateEmployeePayment&&Qi.updateEmployeePayment(e)}}async deleteEmployeePayment(e){if(Ni())Gi.deleteEmployeePayment&&await Gi.deleteEmployeePayment(e);else try{nr.deleteEmployeePayment(e)}catch(ba){console.warn("SQLite not available for employee payments, using localStorage:",ba),Qi.deleteEmployeePayment&&Qi.deleteEmployeePayment(e)}}async setEmployeePayments(e){if(Ni())Gi.setEmployeePayments&&await Gi.setEmployeePayments(e);else try{nr.setEmployeePayments(e)}catch(ba){console.warn("SQLite not available for employee payments, using localStorage:",ba),Qi.setEmployeePayments&&Qi.setEmployeePayments(e)}}async getSettings(){return Ni()?await Gi.getSettings():Qi.getSettings()}async setSettings(e){Ni()?await Gi.setSettings(e):Qi.setSettings(e)}async getCurrentUser(){return Ni()?await Gi.getCurrentUser():Qi.getCurrentUser()}async setCurrentUser(e){Ni()?await Gi.setCurrentUser(e):Qi.setCurrentUser(e)}async initializeDefaultData(){console.log("🔄 Initializing fresh demo data..."),Ni()?await Gi.initializeDefaultData():Qi.initializeDefaultData();0===(await this.getDebts()).length&&await this.forceInitializeDebts();0===(await this.getSales()).length&&await this.initializeSampleSales();0===(await this.getExpenses()).length&&await this.initializeSampleExpenses(),console.log("✅ Fresh demo data initialized successfully")}async initializeSampleSales(){const e=new Date,t=new Date(e.getTime()-864e5),n=new Date(e.getTime()-1728e5),i=[{id:"1",numeroRecu:"RV-"+e.toISOString().slice(0,10).replace(/-/g,"")+"-0001",datevente:t.toISOString(),nomClient:"Marie Kabila",telephoneClient:"+243 900 000 001",produits:[{produitId:"1",nomProduit:"iPhone 15",quantite:1,prixUnitaireCDF:224e4,prixUnitaireUSD:800,totalCDF:224e4,totalUSD:800}],totalCDF:224e4,totalUSD:800,methodePaiement:"cash",typeVente:"cash",vendeur:"Super Admin",notes:"Vente comptant - Client satisfait"},{id:"2",numeroRecu:"RV-"+e.toISOString().slice(0,10).replace(/-/g,"")+"-0002",datevente:n.toISOString(),nomClient:"Jean Mukendi",telephoneClient:"+243 900 000 002",produits:[{produitId:"2",nomProduit:"T-shirt Nike",quantite:2,prixUnitaireCDF:98e3,prixUnitaireUSD:35,totalCDF:196e3,totalUSD:70}],totalCDF:196e3,totalUSD:70,methodePaiement:"mobile_money",typeVente:"cash",vendeur:"Gestionnaire",notes:"Paiement Mobile Money - Airtel"}];await this.setSales(i),console.log("✅ Sample sales data initialized")}async initializeSampleExpenses(){const e=new Date,t=new Date(e.getTime()-6048e5),n=[{id:"1",numeroRecu:"EX-"+e.toISOString().slice(0,10).replace(/-/g,"")+"-0001",dateDepense:t.toISOString(),description:"Achat de fournitures de bureau",montantCDF:28e4,montantUSD:100,categorie:"Fournitures",methodePaiement:"cash",creePar:"Super Admin",notes:"Papier, stylos, et autres fournitures",dateCreation:t.toISOString(),dateModification:t.toISOString()},{id:"2",numeroRecu:"EX-"+e.toISOString().slice(0,10).replace(/-/g,"")+"-0002",dateDepense:e.toISOString(),description:"Frais de transport - Livraison",montantCDF:14e4,montantUSD:50,categorie:"Transport",methodePaiement:"mobile_money",creePar:"Gestionnaire",notes:"Transport pour livraison clients",dateCreation:e.toISOString(),dateModification:e.toISOString()}];await this.setExpenses(n),console.log("✅ Sample expenses data initialized")}async forceInitializeDebts(){const e=new Date,t=new Date(e.getTime()-6048e5),n=new Date(e.getTime()+2592e6),i=[{id:"DET-001",venteId:"VTE-CREDIT-001",nomClient:"Jean Baptiste Mukendi",telephoneClient:"+243 812 345 678",adresseClient:"Avenue Lumumba, Kinshasa",montantTotalCDF:15e4,montantTotalUSD:53.57,montantPayeCDF:0,montantPayeUSD:0,montantRestantCDF:15e4,montantRestantUSD:53.57,dateCreation:t.toISOString(),dateEcheance:n.toISOString(),statut:"active",statutPaiement:"impaye",paiements:[],notes:"Vente à crédit - Produits électroniques"},{id:"DET-002",venteId:"VTE-CREDIT-002",nomClient:"Marie Kabila Tshisekedi",telephoneClient:"+243 823 456 789",adresseClient:"Boulevard du 30 Juin, Kinshasa",montantTotalCDF:75e3,montantTotalUSD:26.79,montantPayeCDF:25e3,montantPayeUSD:8.93,montantRestantCDF:5e4,montantRestantUSD:17.86,dateCreation:t.toISOString(),dateEcheance:n.toISOString(),statut:"active",statutPaiement:"impaye",paiements:[{id:"PAY-001",montantCDF:25e3,montantUSD:8.93,methodePaiement:"cash",datePaiement:e.toISOString(),notes:"Paiement partiel"}],notes:"Dette partiellement payée"}];await this.setDebts(i)}async exportData(){if(Ni()){const{csvImportExportService:e}=await Si(async()=>{const{csvImportExportService:e}=await Promise.resolve().then(()=>ma);return{csvImportExportService:e}},void 0,import.meta.url),t=await e.exportAllData();return t.data?{csvData:t.data,exportDate:(new Date).toISOString()}:{}}return Qi.exportData()}async importData(e){if(Ni()){if(e.csvData&&"string"==typeof e.csvData){const{csvImportExportService:e}=await Si(async()=>{const{csvImportExportService:e}=await Promise.resolve().then(()=>ma);return{csvImportExportService:e}},void 0,import.meta.url);return console.log("CSV import for mobile needs full implementation"),!1}return Qi.importData(e)}return Qi.importData(e)}async migrateFromDesktop(){if(!Ni())return console.warn("Migration should only be called on mobile platform"),!1;try{if((await Gi.getUsers()).length>0)return console.log("CSV storage already has data, skipping migration"),!0;const e={users:localStorage.getItem("smartboutique_users"),products:localStorage.getItem("smartboutique_products"),sales:localStorage.getItem("smartboutique_sales"),debts:localStorage.getItem("smartboutique_debts"),expenses:localStorage.getItem("smartboutique_expenses"),settings:localStorage.getItem("smartboutique_settings"),currentUser:localStorage.getItem("smartboutique_currentUser")};let t=!1;return e.users&&(await Gi.setUsers(JSON.parse(e.users)),t=!0),e.products&&(await Gi.setProducts(JSON.parse(e.products)),t=!0),e.sales&&(await Gi.setSales(JSON.parse(e.sales)),t=!0),e.debts&&(await Gi.setDebts(JSON.parse(e.debts)),t=!0),e.expenses&&(await Gi.setExpenses(JSON.parse(e.expenses)),t=!0),e.settings&&(await Gi.setSettings(JSON.parse(e.settings)),t=!0),e.currentUser&&(await Gi.setCurrentUser(JSON.parse(e.currentUser)),t=!0),t?console.log("Successfully migrated data from desktop to mobile"):console.log("No desktop data found to migrate"),!0}catch(ba){return console.error("Error during migration:",ba),!1}}async getEmployees(){await this.checkMigration();let e=[];if(Ni()){try{await dr.isMigrationCompleted()&&(e=await this.sqliteStorage.getEmployees())}catch(ba){console.warn("Mobile SQLite failed for employees, falling back to CSV:",ba)}0===e.length&&(e=Gi.getEmployees?await Gi.getEmployees():[])}else try{e=nr.getEmployees()}catch(ba){console.warn("SQLite not available for employees, falling back to localStorage storage:",ba),e=Qi.getEmployees?Qi.getEmployees():[]}return this.migrateEmployeeStructure(e)}migrateEmployeeStructure(e){return e.map(e=>{if(e.nom&&!e.nomComplet){const t=e.prenom?`${e.prenom} ${e.nom}`:e.nom;return{...e,nomComplet:t,nom:void 0,prenom:void 0}}return e})}async addEmployee(e){if(await this.checkMigration(),Ni()){try{if(await dr.isMigrationCompleted())return void(await this.sqliteStorage.addEmployee(e))}catch(ba){console.warn("Mobile SQLite failed for employees, falling back to CSV:",ba)}if(!Gi.addEmployee)throw new Error("Employee management not yet implemented for mobile CSV storage");await Gi.addEmployee(e)}else try{nr.addEmployee(e)}catch(ba){if(console.warn("SQLite not available for employees, falling back to localStorage storage:",ba),!Qi.addEmployee)throw new Error("Neither SQLite nor localStorage storage available for employees");Qi.addEmployee(e)}}async updateEmployee(e){if(await this.checkMigration(),Ni()){try{if(await dr.isMigrationCompleted())return void(await this.sqliteStorage.updateEmployee(e))}catch(ba){console.warn("Mobile SQLite failed for employees, falling back to CSV:",ba)}if(!Gi.updateEmployee)throw new Error("Employee management not yet implemented for mobile CSV storage");await Gi.updateEmployee(e)}else try{nr.updateEmployee(e)}catch(ba){if(console.warn("SQLite not available for employees, falling back to localStorage storage:",ba),!Qi.updateEmployee)throw new Error("Neither SQLite nor localStorage storage available for employees");Qi.updateEmployee(e)}}async deleteEmployee(e){if(await this.checkMigration(),Ni()){try{if(await dr.isMigrationCompleted())return void(await this.sqliteStorage.deleteEmployee(e))}catch(ba){console.warn("Mobile SQLite failed for employees, falling back to CSV:",ba)}if(!Gi.deleteEmployee)throw new Error("Employee management not yet implemented for mobile CSV storage");await Gi.deleteEmployee(e)}else try{nr.deleteEmployee(e)}catch(ba){if(console.warn("SQLite not available for employees, falling back to localStorage storage:",ba),!Qi.deleteEmployee)throw new Error("Neither SQLite nor localStorage storage available for employees");Qi.deleteEmployee(e)}}};const mr=new class{constructor(){n(this,"currentUser",null),n(this,"initialized",!1)}async initialize(){this.initialized||(this.currentUser=await ur.getCurrentUser(),this.initialized=!0)}async login(e,t){await this.initialize();const n=(await ur.getUsers()).find(n=>n.email===e&&n.motDePasse===t&&n.actif);return n?(this.currentUser=n,await ur.setCurrentUser(n),{success:!0,user:n}):{success:!1,message:"Email ou mot de passe incorrect"}}async logout(){this.currentUser=null,await ur.remove("currentUser")}getCurrentUser(){return this.currentUser}async getCurrentUserAsync(){return await this.initialize(),this.currentUser}isAuthenticated(){return null!==this.currentUser}getUserPermissions(e){var t;switch(e||(null==(t=this.currentUser)?void 0:t.role)){case"super_admin":return{canViewDashboard:!0,canViewProducts:!0,canManageProducts:!0,canViewSales:!0,canManageSales:!0,canViewDebts:!0,canManageDebts:!0,canViewReports:!0,canViewUsers:!0,canManageUsers:!0,canViewSettings:!0,canManageSettings:!0,canViewExpenses:!0,canManageExpenses:!0,canViewFinancials:!0,canViewRevenue:!0,canViewEmployeePayments:!0,canManageEmployeePayments:!0};case"admin":return{canViewDashboard:!0,canViewProducts:!0,canManageProducts:!0,canViewSales:!0,canManageSales:!0,canViewDebts:!0,canManageDebts:!0,canViewReports:!0,canViewUsers:!0,canManageUsers:!1,canViewSettings:!0,canManageSettings:!1,canViewExpenses:!0,canManageExpenses:!0,canViewFinancials:!0,canViewRevenue:!1,canViewEmployeePayments:!0,canManageEmployeePayments:!0};case"employee":return{canViewDashboard:!1,canViewProducts:!0,canManageProducts:!0,canViewSales:!0,canManageSales:!0,canViewDebts:!1,canManageDebts:!1,canViewReports:!1,canViewUsers:!1,canManageUsers:!1,canViewSettings:!1,canManageSettings:!1,canViewExpenses:!1,canManageExpenses:!1,canViewFinancials:!1,canViewRevenue:!1,canViewEmployeePayments:!1,canManageEmployeePayments:!1};default:return{canViewDashboard:!1,canViewProducts:!1,canManageProducts:!1,canViewSales:!1,canManageSales:!1,canViewDebts:!1,canManageDebts:!1,canViewReports:!1,canViewUsers:!1,canManageUsers:!1,canViewSettings:!1,canManageSettings:!1,canViewExpenses:!1,canManageExpenses:!1,canViewFinancials:!1,canViewRevenue:!1,canViewEmployeePayments:!1,canManageEmployeePayments:!1}}}hasPermission(e){if(!this.currentUser)return!1;return this.getUserPermissions()[e]}hasRole(e){return!!this.currentUser&&e.includes(this.currentUser.role)}canAccessRoute(e){if(!this.currentUser)return!1;const t=this.getUserPermissions();switch(e){case"/dashboard":return t.canViewDashboard;case"/products":return t.canViewProducts;case"/sales":return t.canViewSales;case"/debts":return t.canViewDebts;case"/reports":return t.canViewReports;case"/users":return t.canViewUsers;case"/settings":return t.canViewSettings;case"/expenses":return t.canViewExpenses;case"/employee-payments":return t.canViewEmployeePayments;default:return!0}}getNavigationItems(){if(!this.currentUser)return[];const e=this.getUserPermissions(),t=[];return e.canViewDashboard&&t.push({label:"Tableau de bord",path:"/dashboard",icon:"Dashboard"}),e.canViewProducts&&t.push({label:"Inventaire",path:"/products",icon:"Inventory"}),e.canViewSales&&t.push({label:"Ventes",path:"/sales",icon:"PointOfSale"}),e.canViewDebts&&t.push({label:"Dettes",path:"/debts",icon:"AccountBalance"}),e.canViewExpenses&&t.push({label:"Dépenses",path:"/expenses",icon:"Receipt"}),e.canViewEmployeePayments&&t.push({label:"Paiements Employés",path:"/employee-payments",icon:"Payment"}),e.canViewReports&&t.push({label:"Rapports",path:"/reports",icon:"Assessment"}),e.canViewUsers&&t.push({label:"Utilisateurs",path:"/users",icon:"People"}),e.canViewSettings&&t.push({label:"Paramètres",path:"/settings",icon:"Settings"}),t}async updateProfile(e){if(!this.currentUser)return{success:!1,message:"Utilisateur non connecté"};try{const t=await ur.getUsers(),n=t.findIndex(e=>e.id===this.currentUser.id);if(-1===n)return{success:!1,message:"Utilisateur non trouvé"};const i={...t[n],...e};return t[n]=i,await ur.setUsers(t),this.currentUser=i,await ur.setCurrentUser(i),{success:!0}}catch(ba){return{success:!1,message:"Erreur lors de la mise à jour du profil"}}}async changePassword(e,t){return this.currentUser?this.currentUser.motDePasse!==e?{success:!1,message:"Mot de passe actuel incorrect"}:t.length<6?{success:!1,message:"Le nouveau mot de passe doit contenir au moins 6 caractères"}:this.updateProfile({motDePasse:t}):{success:!1,message:"Utilisateur non connecté"}}},hr=({currentUser:e,onLogout:t})=>{var n,F;const[U,T]=vt.useState(!1),k=Yn(),A=Hn(),R=e?mr.getUserPermissions():null,M=[{label:"Inventaire",path:"/products",icon:i.jsx(D,{}),permission:"canViewProducts"},{label:"Ventes",path:"/sales",icon:i.jsx(C,{}),permission:"canViewSales"},{label:"Dettes",path:"/debts",icon:i.jsx(v,{}),permission:"canViewDebts"},{label:"Plus",path:"/more",icon:i.jsx(S,{}),permission:null}],I=[{label:"Tableau de bord",path:"/dashboard",icon:i.jsx(b,{}),permission:"canViewDashboard"},{label:"Dépenses",path:"/expenses",icon:i.jsx(f,{}),permission:"canViewExpenses"},{label:"Rapports",path:"/reports",icon:i.jsx(w,{}),permission:"canViewReports"},{label:"Utilisateurs",path:"/users",icon:i.jsx(E,{}),permission:"canViewUsers"},{label:"Paramètres",path:"/settings",icon:i.jsx(P,{}),permission:"canViewSettings"}],N=e=>!e||!R||R[e];return i.jsxs(r,{sx:{display:"flex",flexDirection:"column",height:"100vh"},children:[i.jsx(a,{position:"fixed",sx:{zIndex:e=>e.zIndex.drawer+1},children:i.jsxs(s,{children:[i.jsx(o,{variant:"h6",component:"div",sx:{flexGrow:1},children:"SmartBoutique"}),i.jsx(l,{sx:{bgcolor:"secondary.main",width:32,height:32},children:(null==(n=null==e?void 0:e.nom)?void 0:n.charAt(0))||"U"})]})}),i.jsx(r,{component:"main",sx:{flexGrow:1,pt:8,pb:7,overflow:"auto"},children:i.jsx(ci,{})}),i.jsx(c,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:3,children:i.jsx(d,{value:(()=>{const e=A.pathname;return M.find(t=>t.path===e)?e:"/more"})(),onChange:(e,t)=>{var n;"/more"===(n=t)?T(!0):k(n)},showLabels:!0,children:M.map(e=>N(e.permission)&&i.jsx(u,{label:e.label,value:e.path,icon:e.icon},e.path))})}),i.jsxs(m,{anchor:"right",open:U,onClose:()=>T(!1),PaperProps:{sx:{width:280}},children:[i.jsx(s,{}),i.jsx(r,{sx:{p:2},children:i.jsxs(r,{sx:{display:"flex",alignItems:"center",mb:2},children:[i.jsx(l,{sx:{bgcolor:"primary.main",mr:2},children:(null==(F=null==e?void 0:e.nom)?void 0:F.charAt(0))||"U"}),i.jsxs(r,{children:[i.jsx(o,{variant:"subtitle1",fontWeight:"bold",children:(null==e?void 0:e.nom)||"Utilisateur"}),i.jsx(o,{variant:"body2",color:"text.secondary",children:(null==e?void 0:e.role)||"Rôle"})]})]})}),i.jsx(h,{}),i.jsxs(p,{children:[I.map(e=>N(e.permission)&&i.jsxs(x,{onClick:()=>{return t=e.path,k(t),void T(!1);var t},sx:{cursor:"pointer"},children:[i.jsx(g,{children:e.icon}),i.jsx(y,{primary:e.label})]},e.path)),i.jsx(h,{sx:{my:1}}),i.jsxs(x,{onClick:()=>{t(),T(!1)},sx:{cursor:"pointer"},children:[i.jsx(g,{children:i.jsx(j,{})}),i.jsx(y,{primary:"Déconnexion"})]})]})]})]})},pr={lessThanXSeconds:{one:"moins d’une seconde",other:"moins de {{count}} secondes"},xSeconds:{one:"1 seconde",other:"{{count}} secondes"},halfAMinute:"30 secondes",lessThanXMinutes:{one:"moins d’une minute",other:"moins de {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"environ 1 heure",other:"environ {{count}} heures"},xHours:{one:"1 heure",other:"{{count}} heures"},xDays:{one:"1 jour",other:"{{count}} jours"},aboutXWeeks:{one:"environ 1 semaine",other:"environ {{count}} semaines"},xWeeks:{one:"1 semaine",other:"{{count}} semaines"},aboutXMonths:{one:"environ 1 mois",other:"environ {{count}} mois"},xMonths:{one:"1 mois",other:"{{count}} mois"},aboutXYears:{one:"environ 1 an",other:"environ {{count}} ans"},xYears:{one:"1 an",other:"{{count}} ans"},overXYears:{one:"plus d’un an",other:"plus de {{count}} ans"},almostXYears:{one:"presqu’un an",other:"presque {{count}} ans"}},xr={date:ft({formats:{full:"EEEE d MMMM y",long:"d MMMM y",medium:"d MMM y",short:"dd/MM/y"},defaultWidth:"full"}),time:ft({formats:{full:"HH:mm:ss zzzz",long:"HH:mm:ss z",medium:"HH:mm:ss",short:"HH:mm"},defaultWidth:"full"}),dateTime:ft({formats:{full:"{{date}} 'à' {{time}}",long:"{{date}} 'à' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},defaultWidth:"full"})},gr={lastWeek:"eeee 'dernier à' p",yesterday:"'hier à' p",today:"'aujourd’hui à' p",tomorrow:"'demain à' p'",nextWeek:"eeee 'prochain à' p",other:"P"},yr=["MMM","MMMM"],jr={code:"fr",formatDistance:(e,t,n)=>{let i;const r=pr[e];return i="string"==typeof r?r:1===t?r.one:r.other.replace("{{count}}",String(t)),(null==n?void 0:n.addSuffix)?n.comparison&&n.comparison>0?"dans "+i:"il y a "+i:i},formatLong:xr,formatRelative:(e,t,n,i)=>gr[e],localize:{preprocessor:(e,t)=>{if(1===e.getDate())return t;return t.some(e=>e.isToken&&yr.includes(e.value))?t.map(e=>e.isToken&&"do"===e.value?{isToken:!0,value:"d"}:e):t},ordinalNumber:(e,t)=>{const n=Number(e),i=null==t?void 0:t.unit;if(0===n)return"0";let r;return r=1===n?i&&["year","week","hour","minute","second"].includes(i)?"ère":"er":"ème",n+r},era:wt({values:{narrow:["av. J.-C","ap. J.-C"],abbreviated:["av. J.-C","ap. J.-C"],wide:["avant Jésus-Christ","après Jésus-Christ"]},defaultWidth:"wide"}),quarter:wt({values:{narrow:["T1","T2","T3","T4"],abbreviated:["1er trim.","2ème trim.","3ème trim.","4ème trim."],wide:["1er trimestre","2ème trimestre","3ème trimestre","4ème trimestre"]},defaultWidth:"wide",argumentCallback:e=>e-1}),month:wt({values:{narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["janv.","févr.","mars","avr.","mai","juin","juil.","août","sept.","oct.","nov.","déc."],wide:["janvier","février","mars","avril","mai","juin","juillet","août","septembre","octobre","novembre","décembre"]},defaultWidth:"wide"}),day:wt({values:{narrow:["D","L","M","M","J","V","S"],short:["di","lu","ma","me","je","ve","sa"],abbreviated:["dim.","lun.","mar.","mer.","jeu.","ven.","sam."],wide:["dimanche","lundi","mardi","mercredi","jeudi","vendredi","samedi"]},defaultWidth:"wide"}),dayPeriod:wt({values:{narrow:{am:"AM",pm:"PM",midnight:"minuit",noon:"midi",morning:"mat.",afternoon:"ap.m.",evening:"soir",night:"mat."},abbreviated:{am:"AM",pm:"PM",midnight:"minuit",noon:"midi",morning:"matin",afternoon:"après-midi",evening:"soir",night:"matin"},wide:{am:"AM",pm:"PM",midnight:"minuit",noon:"midi",morning:"du matin",afternoon:"de l’après-midi",evening:"du soir",night:"du matin"}},defaultWidth:"wide"})},match:{ordinalNumber:Pt({matchPattern:/^(\d+)(ième|ère|ème|er|e)?/i,parsePattern:/\d+/i,valueCallback:e=>parseInt(e)}),era:Et({matchPatterns:{narrow:/^(av\.J\.C|ap\.J\.C|ap\.J\.-C)/i,abbreviated:/^(av\.J\.-C|av\.J-C|apr\.J\.-C|apr\.J-C|ap\.J-C)/i,wide:/^(avant Jésus-Christ|après Jésus-Christ)/i},defaultMatchWidth:"wide",parsePatterns:{any:[/^av/i,/^ap/i]},defaultParseWidth:"any"}),quarter:Et({matchPatterns:{narrow:/^T?[1234]/i,abbreviated:/^[1234](er|ème|e)? trim\.?/i,wide:/^[1234](er|ème|e)? trimestre/i},defaultMatchWidth:"wide",parsePatterns:{any:[/1/i,/2/i,/3/i,/4/i]},defaultParseWidth:"any",valueCallback:e=>e+1}),month:Et({matchPatterns:{narrow:/^[jfmasond]/i,abbreviated:/^(janv|févr|mars|avr|mai|juin|juill|juil|août|sept|oct|nov|déc)\.?/i,wide:/^(janvier|février|mars|avril|mai|juin|juillet|août|septembre|octobre|novembre|décembre)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^av/i,/^ma/i,/^juin/i,/^juil/i,/^ao/i,/^s/i,/^o/i,/^n/i,/^d/i]},defaultParseWidth:"any"}),day:Et({matchPatterns:{narrow:/^[lmjvsd]/i,short:/^(di|lu|ma|me|je|ve|sa)/i,abbreviated:/^(dim|lun|mar|mer|jeu|ven|sam)\.?/i,wide:/^(dimanche|lundi|mardi|mercredi|jeudi|vendredi|samedi)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^d/i,/^l/i,/^m/i,/^m/i,/^j/i,/^v/i,/^s/i],any:[/^di/i,/^lu/i,/^ma/i,/^me/i,/^je/i,/^ve/i,/^sa/i]},defaultParseWidth:"any"}),dayPeriod:Et({matchPatterns:{narrow:/^(a|p|minuit|midi|mat\.?|ap\.?m\.?|soir|nuit)/i,any:/^([ap]\.?\s?m\.?|du matin|de l'après[-\s]midi|du soir|de la nuit)/i},defaultMatchWidth:"any",parsePatterns:{any:{am:/^a/i,pm:/^p/i,midnight:/^min/i,noon:/^mid/i,morning:/mat/i,afternoon:/ap/i,evening:/soir/i,night:/nuit/i}},defaultParseWidth:"any"})},options:{weekStartsOn:1,firstWeekContainsDate:4}};const Dr=new class{constructor(){n(this,"currentUser",null),this.currentUser=Qi.getCurrentUser()}login(e,t){const n=Qi.getUsers().find(n=>n.email===e&&n.motDePasse===t&&n.actif);return n?(this.currentUser=n,Qi.setCurrentUser(n),{success:!0,user:n}):{success:!1,message:"Email ou mot de passe incorrect"}}logout(){this.currentUser=null,Qi.remove("currentUser")}getCurrentUser(){return this.currentUser}isAuthenticated(){return null!==this.currentUser}getUserPermissions(e){var t;switch(e||(null==(t=this.currentUser)?void 0:t.role)){case"super_admin":return{canViewDashboard:!0,canViewProducts:!0,canManageProducts:!0,canViewSales:!0,canManageSales:!0,canViewDebts:!0,canManageDebts:!0,canViewReports:!0,canViewUsers:!0,canManageUsers:!0,canViewSettings:!0,canManageSettings:!0,canViewExpenses:!0,canManageExpenses:!0,canViewFinancials:!0,canViewRevenue:!0};case"admin":return{canViewDashboard:!0,canViewProducts:!0,canManageProducts:!0,canViewSales:!0,canManageSales:!0,canViewDebts:!0,canManageDebts:!0,canViewReports:!0,canViewUsers:!0,canManageUsers:!1,canViewSettings:!0,canManageSettings:!1,canViewExpenses:!0,canManageExpenses:!0,canViewFinancials:!0,canViewRevenue:!1};case"employee":return{canViewDashboard:!1,canViewProducts:!0,canManageProducts:!0,canViewSales:!0,canManageSales:!0,canViewDebts:!1,canManageDebts:!1,canViewReports:!1,canViewUsers:!1,canManageUsers:!1,canViewSettings:!1,canManageSettings:!1,canViewExpenses:!1,canManageExpenses:!1,canViewFinancials:!1,canViewRevenue:!1};default:return{canViewDashboard:!1,canViewProducts:!1,canManageProducts:!1,canViewSales:!1,canManageSales:!1,canViewDebts:!1,canManageDebts:!1,canViewReports:!1,canViewUsers:!1,canManageUsers:!1,canViewSettings:!1,canManageSettings:!1,canViewExpenses:!1,canManageExpenses:!1,canViewFinancials:!1,canViewRevenue:!1}}}hasPermission(e){if(!this.currentUser)return!1;return this.getUserPermissions()[e]}hasRole(e){return!!this.currentUser&&e.includes(this.currentUser.role)}canAccessRoute(e){if(!this.currentUser)return!1;const t=this.getUserPermissions();switch(e){case"/dashboard":return t.canViewDashboard;case"/products":return t.canViewProducts;case"/sales":return t.canViewSales;case"/debts":return t.canViewDebts;case"/reports":return t.canViewReports;case"/users":return t.canViewUsers;case"/settings":return t.canViewSettings;case"/expenses":return t.canViewExpenses;default:return!0}}getNavigationItems(){if(!this.currentUser)return[];const e=this.getUserPermissions(),t=[];return e.canViewDashboard&&t.push({label:"Tableau de bord",path:"/dashboard",icon:"Dashboard"}),e.canViewProducts&&t.push({label:"Inventaire",path:"/products",icon:"Inventory"}),e.canViewSales&&t.push({label:"Ventes",path:"/sales",icon:"PointOfSale"}),e.canViewDebts&&t.push({label:"Dettes",path:"/debts",icon:"AccountBalance"}),e.canViewExpenses&&t.push({label:"Dépenses",path:"/expenses",icon:"Receipt"}),e.canViewReports&&t.push({label:"Rapports",path:"/reports",icon:"Assessment"}),e.canViewUsers&&t.push({label:"Utilisateurs",path:"/users",icon:"People"}),e.canViewSettings&&t.push({label:"Paramètres",path:"/settings",icon:"Settings"}),t}updateProfile(e){if(!this.currentUser)return{success:!1,message:"Utilisateur non connecté"};try{const t=Qi.getUsers(),n=t.findIndex(e=>e.id===this.currentUser.id);if(-1===n)return{success:!1,message:"Utilisateur non trouvé"};const i={...t[n],...e};return t[n]=i,Qi.setUsers(t),this.currentUser=i,Qi.setCurrentUser(i),{success:!0}}catch(ba){return{success:!1,message:"Erreur lors de la mise à jour du profil"}}}changePassword(e,t){return this.currentUser?this.currentUser.motDePasse!==e?{success:!1,message:"Mot de passe actuel incorrect"}:t.length<6?{success:!1,message:"Le nouveau mot de passe doit contenir au moins 6 caractères"}:this.updateProfile({motDePasse:t}):{success:!1,message:"Utilisateur non connecté"}}};const Cr=new class{constructor(){n(this,"STORAGE_KEY","notifications"),n(this,"LOW_STOCK_THRESHOLD",5),n(this,"notifications",[]),n(this,"listeners",[]),this.loadNotifications()}loadNotifications(){try{const e=localStorage.getItem(this.STORAGE_KEY);this.notifications=e?JSON.parse(e):[]}catch(ba){console.error("Erreur lors du chargement des notifications:",ba),this.notifications=[]}}saveNotifications(){try{localStorage.setItem(this.STORAGE_KEY,JSON.stringify(this.notifications)),this.notifyListeners()}catch(ba){console.error("Erreur lors de la sauvegarde des notifications:",ba)}}notifyListeners(){this.listeners.forEach(e=>e(this.notifications))}subscribe(e){return this.listeners.push(e),e(this.notifications),()=>{const t=this.listeners.indexOf(e);t>-1&&this.listeners.splice(t,1)}}getNotifications(){return[...this.notifications]}getUnreadCount(){return this.notifications.filter(e=>!e.lu).length}markAsRead(e){const t=this.notifications.find(t=>t.id===e);t&&!t.lu&&(t.lu=!0,this.saveNotifications())}markAllAsRead(){let e=!1;this.notifications.forEach(t=>{t.lu||(t.lu=!0,e=!0)}),e&&this.saveNotifications()}deleteNotification(e){const t=this.notifications.findIndex(t=>t.id===e);t>-1&&(this.notifications.splice(t,1),this.saveNotifications())}clearAll(){this.notifications=[],this.saveNotifications()}shouldReceiveStockNotifications(e){if(!e)return!1;const t=Dr.getUserPermissions();return"admin"===e.role||"super_admin"===e.role||!0===(null==t?void 0:t.canManageProducts)}calculateSuggestedReorder(e){return Math.max(3*e.stockMin,20)-e.stock}checkLowStock(e){const t=Dr.getCurrentUser();if(!this.shouldReceiveStockNotifications(t))return;e.filter(e=>e.stock>0&&e.stock<=this.LOW_STOCK_THRESHOLD).forEach(e=>{this.notifications.find(t=>"warning"===t.type&&t.titre.includes(e.nom)&&t.message.includes("stock bas")&&!t.lu&&Date.now()-new Date(t.dateCreation).getTime()<864e5)||this.createLowStockNotification(e)})}createLowStockNotification(e){const t=this.calculateSuggestedReorder(e),n={id:`stock-${e.id}-${Date.now()}`,type:"warning",titre:`Stock bas: ${e.nom}`,message:`Le produit "${e.nom}" a un stock critique de ${e.stock} unité(s). Stock minimum: ${e.stockMin}. Quantité suggérée à commander: ${t} unité(s).`,dateCreation:(new Date).toISOString(),lu:!1,productId:e.id,productName:e.nom,currentStock:e.stock,minimumStock:e.stockMin,suggestedReorder:t};this.notifications.unshift(n),this.saveNotifications(),this.showBrowserNotification(n)}showBrowserNotification(e){"Notification"in window&&"granted"===Notification.permission&&new Notification(`SmartBoutique - ${e.titre}`,{body:`Stock actuel: ${e.currentStock} unité(s). Commande suggérée: ${e.suggestedReorder} unité(s).`,icon:"/favicon.ico",tag:`stock-${e.productId}`})}async requestNotificationPermission(){if(!("Notification"in window))return!1;if("granted"===Notification.permission)return!0;if("denied"===Notification.permission)return!1;return"granted"===await Notification.requestPermission()}createNotification(e,t,n){const i={id:`notif-${Date.now()}`,type:e,titre:t,message:n,dateCreation:(new Date).toISOString(),lu:!1};this.notifications.unshift(i),this.saveNotifications()}},vr=({color:e="inherit"})=>{const[t,n]=vt.useState(null),{notifications:a,unreadCount:s,markAsRead:l,markAllAsRead:d,deleteNotification:u,clearAll:m,requestPermission:j}=(()=>{const[e,t]=vt.useState([]),[n,i]=vt.useState(0);return vt.useEffect(()=>Cr.subscribe(e=>{t(e),i(Cr.getUnreadCount())}),[]),{notifications:e,unreadCount:n,markAsRead:e=>{Cr.markAsRead(e)},markAllAsRead:()=>{Cr.markAllAsRead()},deleteNotification:e=>{Cr.deleteNotification(e)},clearAll:()=>{Cr.clearAll()},requestPermission:async()=>await Cr.requestNotificationPermission()}})(),C=e=>{switch(e){case"info":default:return i.jsx(B,{color:"info"});case"warning":return i.jsx($,{color:"warning"});case"error":return i.jsx(z,{color:"error"});case"success":return i.jsx(_,{color:"success"})}},v=e=>{switch(e){case"info":return"info";case"warning":return"warning";case"error":return"error";case"success":return"success";default:return"default"}},S=Boolean(t),b=S?"notification-popover":void 0;return i.jsxs(i.Fragment,{children:[i.jsx(F,{title:"Notifications",children:i.jsx(U,{color:e,onClick:e=>{n(e.currentTarget)},children:i.jsx(T,{badgeContent:s,color:"error",children:s>0?i.jsx(k,{}):i.jsx(A,{})})})}),i.jsx(R,{id:b,open:S,anchorEl:t,onClose:()=>{n(null)},anchorOrigin:{vertical:"bottom",horizontal:"right"},transformOrigin:{vertical:"top",horizontal:"right"},children:i.jsxs(c,{sx:{width:400,maxHeight:500},children:[i.jsxs(r,{sx:{p:2,borderBottom:"1px solid",borderColor:"divider"},children:[i.jsxs(r,{display:"flex",justifyContent:"space-between",alignItems:"center",children:[i.jsx(o,{variant:"h6",children:"Notifications"}),s>0&&i.jsx(M,{label:`${s} non lue(s)`,color:"primary",size:"small"})]}),a.length>0&&i.jsxs(r,{sx:{mt:1,display:"flex",gap:1},children:[s>0&&i.jsx(I,{size:"small",startIcon:i.jsx(N,{}),onClick:d,children:"Tout marquer lu"}),i.jsx(I,{size:"small",startIcon:i.jsx(L,{}),onClick:m,color:"error",children:"Tout effacer"})]})]}),"Notification"in window&&"default"===Notification.permission&&i.jsx(O,{severity:"info",sx:{m:1},action:i.jsx(I,{size:"small",onClick:async()=>{await j()},children:"Activer"}),children:"Activez les notifications du navigateur pour les alertes de stock"}),0===a.length?i.jsxs(r,{sx:{p:3,textAlign:"center"},children:[i.jsx(A,{sx:{fontSize:48,color:"text.secondary",mb:1}}),i.jsx(o,{variant:"body2",color:"text.secondary",children:"Aucune notification"})]}):i.jsx(p,{sx:{maxHeight:350,overflow:"auto"},children:a.map((e,t)=>i.jsxs(bt.Fragment,{children:[i.jsxs(x,{button:!0,onClick:()=>(e=>{e.lu||l(e.id)})(e),sx:{bgcolor:e.lu?"transparent":"action.hover","&:hover":{bgcolor:"action.selected"}},children:[i.jsx(g,{children:e.titre.includes("Stock bas")?i.jsx(D,{color:"warning"}):C(e.type)}),i.jsx(y,{primary:i.jsxs(r,{display:"flex",alignItems:"center",gap:1,children:[i.jsx(o,{variant:"subtitle2",sx:{fontWeight:e.lu?"normal":"bold",flex:1},children:e.titre}),i.jsx(M,{label:e.type,size:"small",color:v(e.type),variant:"outlined"})]}),secondary:i.jsxs(r,{children:[i.jsx(o,{variant:"body2",color:"text.secondary",sx:{mb:.5},children:e.message}),i.jsx(o,{variant:"caption",color:"text.secondary",children:Ft(new Date(e.dateCreation),"dd/MM/yyyy HH:mm",{locale:jr})})]})}),i.jsx(V,{children:i.jsx(F,{title:"Supprimer",children:i.jsx(U,{edge:"end",size:"small",onClick:t=>{return n=t,i=e.id,n.stopPropagation(),void u(i);var n,i},children:i.jsx(q,{fontSize:"small"})})})})]}),t<a.length-1&&i.jsx(h,{})]},e.id))})]})})]})},Sr=240,br=({currentUser:e,onLogout:t})=>{var n,c,d;if(Ni())return i.jsx(hr,{currentUser:e,onLogout:t});const[u,T]=vt.useState(!1),[k,A]=vt.useState(null),R=Yn(),M=Hn(),I=()=>{T(!u)},N=()=>{A(null)},L=mr.getNavigationItems(),O=e=>{switch(e){case"Dashboard":default:return i.jsx(b,{});case"Inventory":return i.jsx(D,{});case"PointOfSale":return i.jsx(C,{});case"AccountBalance":return i.jsx(v,{});case"Receipt":return i.jsx(f,{});case"Payment":return i.jsx(G,{});case"Assessment":return i.jsx(w,{});case"People":return i.jsx(E,{});case"Settings":return i.jsx(P,{})}},V=i.jsxs("div",{children:[i.jsx(s,{children:i.jsxs(r,{display:"flex",alignItems:"center",width:"100%",children:[i.jsx(o,{variant:"h6",noWrap:!0,component:"div",sx:{flexGrow:1},children:"SmartBoutique"}),i.jsx(U,{onClick:I,sx:{display:{sm:"none"}},children:i.jsx(W,{})})]})}),i.jsx(h,{}),i.jsx(p,{children:L.map(e=>i.jsx(x,{disablePadding:!0,children:i.jsxs(X,{selected:M.pathname===e.path,onClick:()=>{R(e.path),T(!1)},children:[i.jsx(g,{children:O(e.icon)}),i.jsx(y,{primary:e.label})]})},e.path))})]});return i.jsxs(r,{sx:{display:"flex"},children:[i.jsx(a,{position:"fixed",sx:{width:{sm:"calc(100% - 240px)"},ml:{sm:"240px"}},children:i.jsxs(s,{children:[i.jsx(U,{color:"inherit","aria-label":"open drawer",edge:"start",onClick:I,sx:{mr:2,display:{sm:"none"}},children:i.jsx(S,{})}),i.jsx(o,{variant:"h6",noWrap:!0,component:"div",sx:{flexGrow:1},children:(null==(n=L.find(e=>e.path===M.pathname))?void 0:n.label)||"SmartBoutique"}),i.jsx(vr,{color:"inherit"}),i.jsx(F,{title:"Profil utilisateur",children:i.jsx(U,{color:"inherit",onClick:e=>{A(e.currentTarget)},sx:{ml:1},children:i.jsx(l,{sx:{width:32,height:32,bgcolor:"secondary.main"},children:(null==(d=null==(c=null==e?void 0:e.nom)?void 0:c.charAt(0))?void 0:d.toUpperCase())||"U"})})}),i.jsxs(Q,{anchorEl:k,open:Boolean(k),onClose:N,onClick:N,children:[i.jsx(J,{disabled:!0,children:i.jsxs(r,{children:[i.jsx(o,{variant:"subtitle2",children:null==e?void 0:e.nom}),i.jsxs(o,{variant:"caption",color:"text.secondary",children:["super_admin"===(null==e?void 0:e.role)&&"Super Administrateur","admin"===(null==e?void 0:e.role)&&"Administrateur","employee"===(null==e?void 0:e.role)&&"Employé"]})]})}),i.jsx(h,{}),i.jsxs(J,{onClick:()=>R("/settings"),children:[i.jsx(g,{children:i.jsx(H,{fontSize:"small"})}),"Mon Profil"]}),i.jsxs(J,{onClick:()=>{N(),t(),R("/login")},children:[i.jsx(g,{children:i.jsx(j,{fontSize:"small"})}),"Déconnexion"]})]})]})}),i.jsxs(r,{component:"nav",sx:{width:{sm:Sr},flexShrink:{sm:0}},children:[i.jsx(m,{variant:"temporary",open:u,onClose:I,ModalProps:{keepMounted:!0},sx:{display:{xs:"block",sm:"none"},"& .MuiDrawer-paper":{boxSizing:"border-box",width:Sr}},children:V}),i.jsx(m,{variant:"permanent",sx:{display:{xs:"none",sm:"block"},"& .MuiDrawer-paper":{boxSizing:"border-box",width:Sr}},open:!0,children:V})]}),i.jsxs(r,{component:"main",sx:{flexGrow:1,p:3,width:{sm:"calc(100% - 240px)"},minHeight:"100vh",backgroundColor:"background.default"},children:[i.jsx(s,{}),i.jsx(ci,{})]})]})},fr=({children:e,requiredPermission:t,requiredRole:n})=>mr.getCurrentUser()?n&&!mr.hasRole(n)?i.jsx(r,{display:"flex",justifyContent:"center",alignItems:"center",minHeight:"60vh",children:i.jsxs(c,{sx:{p:4,textAlign:"center",maxWidth:400},children:[i.jsx(Y,{sx:{fontSize:64,color:"error.main",mb:2}}),i.jsx(o,{variant:"h5",gutterBottom:!0,children:"Accès Refusé"}),i.jsx(o,{variant:"body1",color:"text.secondary",paragraph:!0,children:"Vous n'avez pas les permissions nécessaires pour accéder à cette page."}),i.jsx(O,{severity:"warning",sx:{mt:2},children:"Contactez votre administrateur pour obtenir l'accès requis."})]})}):t&&!mr.hasPermission(t)?i.jsx(r,{display:"flex",justifyContent:"center",alignItems:"center",minHeight:"60vh",children:i.jsxs(c,{sx:{p:4,textAlign:"center",maxWidth:400},children:[i.jsx(Y,{sx:{fontSize:64,color:"error.main",mb:2}}),i.jsx(o,{variant:"h5",gutterBottom:!0,children:"Accès Refusé"}),i.jsx(o,{variant:"body1",color:"text.secondary",paragraph:!0,children:"Vous n'avez pas les permissions nécessaires pour accéder à cette page."}),i.jsx(O,{severity:"warning",sx:{mt:2},children:"Cette fonctionnalité est réservée aux utilisateurs autorisés."})]})}):i.jsx(i.Fragment,{children:e}):i.jsx(li,{to:"/login",replace:!0}),wr=({onLogin:e})=>{const[t,n]=vt.useState(""),[a,s]=vt.useState(""),[c,d]=vt.useState(!1),[u,m]=vt.useState(""),[h,p]=vt.useState(!1),x=async t=>{p(!0),m("");let n={email:"",password:""};switch(t){case"admin":n={email:"<EMAIL>",password:"admin123"};break;case"manager":n={email:"<EMAIL>",password:"manager123"};break;case"employee":n={email:"<EMAIL>",password:"employee123"}}try{const t=await mr.login(n.email,n.password);t.success&&t.user?e(t.user):m(t.message||"Erreur de connexion")}catch(i){m("Erreur de connexion. Veuillez réessayer.")}finally{p(!1)}};return i.jsx(K,{maxWidth:"sm",sx:{height:"100vh",display:"flex",alignItems:"center"},children:i.jsxs(r,{sx:{width:"100%",px:2},children:[i.jsxs(r,{sx:{textAlign:"center",mb:4},children:[i.jsx(l,{sx:{width:80,height:80,bgcolor:"primary.main",mx:"auto",mb:2},children:i.jsx(Z,{sx:{fontSize:40}})}),i.jsx(o,{variant:"h4",component:"h1",fontWeight:"bold",color:"primary",children:"SmartBoutique"}),i.jsx(o,{variant:"subtitle1",color:"text.secondary",sx:{mt:1},children:"Gestion de boutique mobile"})]}),i.jsx(ee,{elevation:4,sx:{borderRadius:3},children:i.jsxs(te,{sx:{p:4},children:[i.jsx(o,{variant:"h5",component:"h2",textAlign:"center",sx:{mb:3},children:"Connexion"}),u&&i.jsx(O,{severity:"error",sx:{mb:3},children:u}),i.jsxs(r,{component:"form",onSubmit:async n=>{n.preventDefault(),m(""),p(!0);try{const n=await mr.login(t,a);n.success&&n.user?e(n.user):m(n.message||"Erreur de connexion")}catch(i){m("Erreur de connexion. Veuillez réessayer.")}finally{p(!1)}},children:[i.jsx(ne,{fullWidth:!0,label:"Email",type:"email",value:t,onChange:e=>n(e.target.value),required:!0,sx:{mb:3},InputProps:{startAdornment:i.jsx(ie,{position:"start",children:i.jsx(re,{color:"action"})}),style:{fontSize:"16px"}},inputProps:{autoComplete:"email",style:{fontSize:"16px"}}}),i.jsx(ne,{fullWidth:!0,label:"Mot de passe",type:c?"text":"password",value:a,onChange:e=>s(e.target.value),required:!0,sx:{mb:4},InputProps:{startAdornment:i.jsx(ie,{position:"start",children:i.jsx(Y,{color:"action"})}),endAdornment:i.jsx(ie,{position:"end",children:i.jsx(U,{onClick:()=>{d(!c)},edge:"end",size:"large",children:c?i.jsx(ae,{}):i.jsx(se,{})})}),style:{fontSize:"16px"}},inputProps:{autoComplete:"current-password",style:{fontSize:"16px"}}}),i.jsx(I,{type:"submit",fullWidth:!0,variant:"contained",size:"large",disabled:h,sx:{py:2,fontSize:"1.1rem",fontWeight:"bold",borderRadius:2,mb:3},children:h?"Connexion...":"Se connecter"})]}),i.jsxs(r,{sx:{mt:3},children:[i.jsx(o,{variant:"body2",color:"text.secondary",textAlign:"center",sx:{mb:2},children:"Connexion rapide (Démo)"}),i.jsxs(r,{sx:{display:"flex",flexDirection:"column",gap:1},children:[i.jsx(I,{variant:"outlined",size:"large",onClick:()=>x("admin"),disabled:h,sx:{py:1.5},children:"Super Admin"}),i.jsx(I,{variant:"outlined",size:"large",onClick:()=>x("manager"),disabled:h,sx:{py:1.5},children:"Gestionnaire"}),i.jsx(I,{variant:"outlined",size:"large",onClick:()=>x("employee"),disabled:h,sx:{py:1.5},children:"Employé"})]})]})]})}),i.jsx(o,{variant:"body2",color:"text.secondary",textAlign:"center",sx:{mt:3},children:"Version Mobile • SmartBoutique 2024"})]})})},Er=({onLogin:e})=>{if(Ni())return i.jsx(wr,{onLogin:e});const[t,n]=vt.useState(""),[a,s]=vt.useState(""),[c,d]=vt.useState(""),[u,m]=vt.useState(!1),p=(e,t)=>{n(e),s(t)};return i.jsx(K,{component:"main",maxWidth:"sm",children:i.jsxs(r,{sx:{minHeight:"100vh",display:"flex",flexDirection:"column",justifyContent:"center",alignItems:"center",py:4},children:[i.jsxs(r,{sx:{mb:4,textAlign:"center"},children:[i.jsx(l,{sx:{mx:"auto",mb:2,bgcolor:"primary.main",width:64,height:64},children:i.jsx(Z,{sx:{fontSize:32}})}),i.jsx(o,{component:"h1",variant:"h4",gutterBottom:!0,children:"SmartBoutique"}),i.jsx(o,{variant:"subtitle1",color:"text.secondary",children:"Système de Gestion de Boutique"})]}),i.jsx(ee,{sx:{width:"100%",maxWidth:400},children:i.jsxs(te,{sx:{p:4},children:[i.jsxs(r,{sx:{display:"flex",alignItems:"center",mb:3},children:[i.jsx(l,{sx:{mr:2,bgcolor:"secondary.main"},children:i.jsx(oe,{})}),i.jsx(o,{component:"h2",variant:"h5",children:"Connexion"})]}),c&&i.jsx(O,{severity:"error",sx:{mb:2},children:c}),i.jsxs(r,{component:"form",onSubmit:async n=>{n.preventDefault(),d(""),m(!0);try{const n=await mr.login(t,a);n.success&&n.user?e(n.user):d(n.message||"Erreur de connexion")}catch(i){d("Une erreur est survenue lors de la connexion")}finally{m(!1)}},children:[i.jsx(ne,{margin:"normal",required:!0,fullWidth:!0,id:"email",label:"Adresse email",name:"email",autoComplete:"email",autoFocus:!0,value:t,onChange:e=>n(e.target.value),disabled:u}),i.jsx(ne,{margin:"normal",required:!0,fullWidth:!0,name:"password",label:"Mot de passe",type:"password",id:"password",autoComplete:"current-password",value:a,onChange:e=>s(e.target.value),disabled:u}),i.jsx(I,{type:"submit",fullWidth:!0,variant:"contained",sx:{mt:3,mb:2},disabled:u,children:u?"Connexion...":"Se connecter"})]}),i.jsx(h,{sx:{my:3},children:i.jsx(M,{label:"Comptes de démonstration",size:"small"})}),i.jsxs(r,{sx:{display:"flex",flexDirection:"column",gap:1},children:[i.jsx(I,{variant:"outlined",size:"small",onClick:()=>p("<EMAIL>","admin123"),disabled:u,children:"Super Admin (<EMAIL>)"}),i.jsx(I,{variant:"outlined",size:"small",onClick:()=>p("<EMAIL>","manager123"),disabled:u,children:"Gestionnaire (<EMAIL>)"}),i.jsx(I,{variant:"outlined",size:"small",onClick:()=>p("<EMAIL>","employee123"),disabled:u,children:"Employé (<EMAIL>)"})]})]})}),i.jsxs(r,{sx:{mt:4,textAlign:"center"},children:[i.jsx(o,{variant:"body2",color:"text.secondary",children:"© 2024 SmartBoutique. Tous droits réservés."}),i.jsx(o,{variant:"caption",color:"text.secondary",sx:{mt:1,display:"block"},children:"Version 1.0.0"})]})]})})},Pr=2800,Fr=e=>null==e||isNaN(e)?0:Math.round(100*e)/100,Ur=(e,t,n,i=2800)=>{if(null==e||isNaN(e))return 0;if((null==i||isNaN(i)||i<=0)&&(console.warn("Invalid exchange rate provided, using default:",Pr),i=Pr),t===n)return Fr(e);let r;if("CDF"===t&&"USD"===n)r=e/i;else{if("USD"!==t||"CDF"!==n)return console.error("Unsupported currency conversion:",t,"to",n),0;r=e*i}return Fr(r)},Tr=(e,t=2800)=>Ur(e,"CDF","USD",t),kr=(e,t=0,n=2)=>null==e||isNaN(e)?"0":e.toLocaleString("fr-FR",{minimumFractionDigits:t,maximumFractionDigits:n}),Ar=(e,t,n={})=>{const{showSymbol:i=!0,minimumFractionDigits:r=("USD"===t?2:0),maximumFractionDigits:a=2}=n,s=null==e||isNaN(e)?0:e,o=kr(s,r,a);return i?"USD"===t?`$${o}`:"CDF"===t?`${o} CDF`:`${o} ${t}`:o},Rr=(e,t="valeur",n={})=>{const{allowZero:i=!1,allowNegative:r=!1,minValue:a=null,maxValue:s=null}=n,o=[];return null==e||isNaN(e)?(o.push(`${t} doit être un nombre valide`),{isValid:!1,errors:o}):(!r&&e<0&&o.push(`${t} ne peut pas être négatif`),i||0!==e||o.push(`${t} doit être supérieur à zéro`),null!==a&&e<a&&o.push(`${t} doit être supérieur ou égal à ${a}`),null!==s&&e>s&&o.push(`${t} doit être inférieur ou égal à ${s}`),{isValid:0===o.length,errors:o})},Mr=(e,t=0,n="Erreur de calcul financier")=>{try{const n=e();return null==n||isNaN(n)?t:n}catch(ba){return console.error(n,ba),t}},Ir="Le prix ne peut pas être négatif",Nr=(e,t,n=2800)=>{const i=Fr((r=t,a=e,Mr(()=>r-a,0,"Erreur lors du calcul du bénéfice unitaire")));var r,a;return{beneficeUnitaireCDF:i,beneficeUnitaireUSD:Fr(Tr(i,n))}},Lr=(e,t)=>{const n=((e,t,n={})=>{const{purchaseFieldName:i="Le prix d'achat",sellingFieldName:r="Le prix de vente"}=n,a=Rr(e,i),s=Rr(t,r),o=[...a.errors,...s.errors];return a.isValid&&s.isValid&&t<=e&&o.push("Le prix de vente doit être supérieur au prix d'achat"),{isValid:0===o.length,errors:o}})(e,t);return{isValid:n.isValid,errorMessage:n.errors.length>0?n.errors[0]:void 0}},Or=(e,t)=>"USD"===t?`$${e.toLocaleString("fr-FR",{minimumFractionDigits:2,maximumFractionDigits:2})}`:`${e.toLocaleString("fr-FR")} CDF`,Vr=(e,t)=>{console.log("🔍 Revenue Analytics Debug:",{totalProducts:e.length,totalSales:t.length,productsWithPricing:e.filter(e=>e.prixCDF&&e.prixAchatCDF).length,productsWithStock:e.filter(e=>e.stock>0).length,profitableProducts:e.filter(e=>e.prixCDF&&e.prixAchatCDF&&e.prixCDF-e.prixAchatCDF>0).length});const n=(e=>Mr(()=>{let t=0,n=0;return e.forEach(e=>{if(e.prixCDF&&e.prixAchatCDF&&e.stock>0){const n=e.prixCDF-e.prixAchatCDF;n>0&&(t+=Fr(n*e.stock))}if(e.prixUSD&&e.prixAchatUSD&&e.stock>0){const t=e.prixUSD-e.prixAchatUSD;t>0&&(n+=Fr(t*e.stock))}else if(e.prixCDF&&e.prixAchatCDF&&e.stock>0){const t=e.prixCDF-e.prixAchatCDF;if(t>0){const i=Tr(t,Pr);n+=Fr(i*e.stock)}}}),{totalRevenueCDF:Fr(t),totalRevenueUSD:Fr(n)}},{totalRevenueCDF:0,totalRevenueUSD:0},"Erreur lors du calcul des revenus d'inventaire"))(e),i=((e,t)=>Mr(()=>{let n=0,i=0;return e.forEach(e=>{e.produits.forEach(e=>{const r=t.find(t=>t.id===e.produitId);if(r&&e.prixUnitaireCDF&&r.prixAchatCDF){const t=e.prixUnitaireCDF-r.prixAchatCDF;t>0&&(n+=Fr(t*e.quantite))}if(r&&e.prixUnitaireUSD&&r.prixAchatUSD){const t=e.prixUnitaireUSD-r.prixAchatUSD;t>0&&(i+=Fr(t*e.quantite))}else if(r&&e.prixUnitaireCDF&&r.prixAchatCDF){const t=e.prixUnitaireCDF-r.prixAchatCDF;if(t>0){const n=Tr(t,Pr);i+=Fr(n*e.quantite)}}})}),{realizedRevenueCDF:Fr(n),realizedRevenueUSD:Fr(i)}},{realizedRevenueCDF:0,realizedRevenueUSD:0},"Erreur lors du calcul des revenus réalisés"))(t,e),r=(e=>{const t={};return e.forEach(e=>{if(t[e.categorie]||(t[e.categorie]={revenueCDF:0,revenueUSD:0,productCount:0}),e.prixCDF&&e.prixAchatCDF&&e.stock>0){const n=e.prixCDF-e.prixAchatCDF;n>0&&(t[e.categorie].revenueCDF+=Fr(n*e.stock),t[e.categorie].productCount+=1)}if(e.prixUSD&&e.prixAchatUSD&&e.stock>0){const n=e.prixUSD-e.prixAchatUSD;n>0&&(t[e.categorie].revenueUSD+=Fr(n*e.stock))}else if(e.prixCDF&&e.prixAchatCDF&&e.stock>0){const n=e.prixCDF-e.prixAchatCDF;if(n>0){const i=Tr(n,Pr);t[e.categorie].revenueUSD+=Fr(i*e.stock)}}}),t})(e),a=((e,t=10)=>e.filter(e=>e.prixCDF&&e.prixAchatCDF&&e.stock>0).filter(e=>e.prixCDF-e.prixAchatCDF>0).sort((e,t)=>{const n=(e.prixCDF-e.prixAchatCDF)*e.stock;return(t.prixCDF-t.prixAchatCDF)*t.stock-n}).slice(0,t))(e,5),s=e.filter(e=>e.prixCDF&&e.prixAchatCDF&&e.prixCDF-e.prixAchatCDF>0).length;return console.log("💰 Calculated Revenue:",{potentialProfitCDF:n.totalRevenueCDF,realizedProfitCDF:i.realizedRevenueCDF,topProductsCount:a.length,totalProductsWithProfit:s}),{totalInventoryRevenueCDF:n.totalRevenueCDF,totalInventoryRevenueUSD:n.totalRevenueUSD,realizedRevenueCDF:i.realizedRevenueCDF,realizedRevenueUSD:i.realizedRevenueUSD,categoryBreakdown:r,topProducts:a,totalProductsWithProfit:s}},qr=(e,t)=>Ar(e,t),Br=(e,t=2800)=>{const n=null==e||isNaN(e)?0:e,i=null==t||isNaN(t)||t<=0?Pr:t,r=Fr(Tr(n,i));return{primaryAmount:Ar(n,"CDF",{showSymbol:!1}),secondaryAmount:Ar(r,"USD",{showSymbol:!1}),primaryCurrency:"CDF",secondaryCurrency:"USD"}},_r=e=>0===e.stock?"out_of_stock":e.stock<=e.stockMin?"low_stock":"in_stock",zr=()=>"123"+Date.now().toString().slice(-10),$r=()=>{const e=Date.now().toString(),t=Math.random().toString(36).substr(2,4).toUpperCase();return`SB${e.slice(-8)}${t}`},Wr=(e,t)=>Tr(e,t),Xr=(e,t)=>((e,t=2800)=>Ur(e,"USD","CDF",t))(e,t),Qr=(e,t)=>Tr(e,t),Jr=({analytics:e,products:t,todaySalesData:n,debtsData:a,isLoading:s=!1})=>{if(s)return i.jsxs(r,{children:[i.jsx(o,{variant:"h5",gutterBottom:!0,children:"Profit - Analyse des Bénéfices"}),i.jsx(le,{})]});if(0===e.totalProductsWithProfit)return i.jsxs(r,{children:[i.jsx(o,{variant:"h5",gutterBottom:!0,children:"Profit - Analyse des Bénéfices"}),i.jsx(O,{severity:"info",children:"Aucun produit avec prix d'achat et de vente configurés. Ajoutez des prix d'achat aux produits pour voir l'analyse des profits."})]});const l=Object.entries(e.categoryBreakdown);return i.jsxs(r,{children:[i.jsx(o,{variant:"h5",gutterBottom:!0,sx:{mb:3},children:"Profit - Analyse des Bénéfices"}),i.jsxs(ce,{container:!0,spacing:3,sx:{mb:3},children:[i.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:i.jsx(ee,{children:i.jsx(te,{children:i.jsxs(r,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[i.jsxs(r,{children:[i.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Profit Potentiel"}),i.jsxs(o,{variant:"h6",fontWeight:"medium",children:[Br(e.totalInventoryRevenueCDF,2800).primaryAmount," ",Br(e.totalInventoryRevenueCDF,2800).primaryCurrency]}),i.jsxs(o,{variant:"body2",color:"primary",children:["≈ $",Br(e.totalInventoryRevenueCDF,2800).secondaryAmount]})]}),i.jsx(D,{color:"primary",sx:{fontSize:40}})]})})})}),i.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:i.jsx(ee,{children:i.jsx(te,{children:i.jsxs(r,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[i.jsxs(r,{children:[i.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Profit Réalisé"}),i.jsxs(o,{variant:"h6",fontWeight:"medium",children:[Br(e.realizedRevenueCDF,2800).primaryAmount," ",Br(e.realizedRevenueCDF,2800).primaryCurrency]}),i.jsxs(o,{variant:"body2",color:"success.main",children:["≈ $",Br(e.realizedRevenueCDF,2800).secondaryAmount]})]}),i.jsx(de,{color:"success",sx:{fontSize:40}})]})})})}),n&&i.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:i.jsx(ee,{children:i.jsx(te,{children:i.jsxs(r,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[i.jsxs(r,{children:[i.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Ventes du jour"}),i.jsxs(o,{variant:"h6",fontWeight:"medium",children:[Br(n.revenusCDF,2800).primaryAmount," ",Br(n.revenusCDF,2800).primaryCurrency]}),i.jsxs(o,{variant:"body2",color:"primary",children:["≈ $",Br(n.revenusCDF,2800).secondaryAmount]}),i.jsxs(o,{variant:"caption",color:"textSecondary",children:[n.nombreVentes," vente",1!==n.nombreVentes?"s":""]})]}),i.jsx(C,{color:"primary",sx:{fontSize:40}})]})})})}),a&&i.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:i.jsx(ee,{children:i.jsx(te,{children:i.jsxs(r,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[i.jsxs(r,{children:[i.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Total des Dettes"}),i.jsxs(o,{variant:"h6",color:"error",fontWeight:"medium",children:[Br(a.montantDettesTotalCDF,2800).primaryAmount," ",Br(a.montantDettesTotalCDF,2800).primaryCurrency]}),i.jsxs(o,{variant:"body2",color:"error",children:["≈ $",Br(a.montantDettesTotalCDF,2800).secondaryAmount]}),i.jsxs(o,{variant:"caption",color:"textSecondary",children:[a.dettesActives," dette",1!==a.dettesActives?"s":""," active",1!==a.dettesActives?"s":""]})]}),i.jsx(v,{color:"warning",sx:{fontSize:40}})]})})})})]}),i.jsxs(ce,{container:!0,spacing:3,children:[i.jsx(ce,{item:!0,xs:12,md:6,children:i.jsxs(ee,{children:[i.jsx(ue,{title:"Produits les Plus Rentables"}),i.jsx(te,{children:i.jsx(me,{children:i.jsxs(he,{size:"small",children:[i.jsx(pe,{children:i.jsxs(xe,{children:[i.jsx(ge,{children:"Produit"}),i.jsx(ge,{align:"right",children:"Profit Potentiel"})]})}),i.jsx(ye,{children:e.topProducts.map(e=>{const t=((e.prixCDF||0)-(e.prixAchatCDF||0))*e.stock;return i.jsxs(xe,{children:[i.jsx(ge,{children:i.jsxs(r,{children:[i.jsx(o,{variant:"body2",fontWeight:"medium",children:e.nom}),i.jsxs(o,{variant:"caption",color:"textSecondary",children:["Stock: ",e.stock]})]})}),i.jsx(ge,{align:"right",children:i.jsx(o,{variant:"body2",children:Or(t,"CDF")})})]},e.id)})})]})})})]})}),i.jsx(ce,{item:!0,xs:12,md:6,children:i.jsxs(ee,{children:[i.jsx(ue,{title:"Profit par Catégorie"}),i.jsx(te,{children:i.jsx(me,{children:i.jsxs(he,{size:"small",children:[i.jsx(pe,{children:i.jsxs(xe,{children:[i.jsx(ge,{children:"Catégorie"}),i.jsx(ge,{align:"right",children:"Produits"}),i.jsx(ge,{align:"right",children:"Profit (CDF)"})]})}),i.jsx(ye,{children:l.sort(([,e],[,t])=>t.revenueCDF-e.revenueCDF).map(([e,t])=>i.jsxs(xe,{children:[i.jsx(ge,{children:i.jsx(M,{label:e,size:"small"})}),i.jsx(ge,{align:"right",children:t.productCount}),i.jsxs(ge,{align:"right",children:[i.jsx(o,{variant:"body2",children:Or(t.revenueCDF,"CDF")}),i.jsx(o,{variant:"caption",color:"textSecondary",children:Or(t.revenueUSD,"USD")})]})]},e))})]})})})]})})]})]})},Hr=()=>{const[e,t]=vt.useState(null),[n,a]=vt.useState([]),[s,l]=vt.useState([]),[d,u]=vt.useState(null),[m,h]=vt.useState(null),[j,v]=vt.useState(null),[S,b]=vt.useState({tauxChangeUSDCDF:2800}),[f,w]=vt.useState("jour"),E=mr.getUserPermissions(),P=e=>{try{const t=new Date(e);return isNaN(t.getTime())?(console.warn("Invalid date found:",e),null):t}catch(ba){return console.warn("Error parsing date:",e,ba),null}};vt.useEffect(()=>{(async()=>{try{const e=await ur.getSettings();b(e),await U()}catch(ba){console.error("Error loading data:",ba),await U()}})()},[]);const F=(e,t)=>{switch(e){case"jour":default:return t.ventesDuJour;case"semaine":return t.ventesDeLaSemaine;case"mois":return t.ventesDuMois}},U=async()=>{const e=await ur.getProducts(),n=await ur.getSales(),i=await ur.getDebts(),r=await ur.getSettings(),s=new Date,o=Ut(s),c=Tt(s),d=kt(s,7),u=kt(s,30),m=n.filter(e=>{const t=P(e.datevente);return!!t&&(At(t,o)&&Rt(t,c))}),h=n.filter(e=>{const t=P(e.datevente);return!!t&&At(t,d)}),p=n.filter(e=>{const t=P(e.datevente);return!!t&&At(t,u)}),x=e.filter(e=>e.stock>0),g=e.filter(e=>e.stock<=e.stockMin&&e.stock>0),y=e.filter(e=>0===e.stock),j=i.filter(e=>"active"===e.statut),D=i.filter(e=>"overdue"===e.statut),C=Fr(m.reduce((e,t)=>e+t.totalCDF,0)),S=Fr(h.reduce((e,t)=>e+t.totalCDF,0)),b=Fr(p.reduce((e,t)=>e+t.totalCDF,0)),f=Fr(e.reduce((e,t)=>e+t.prixCDF*t.stock,0)),w=Fr(j.reduce((e,t)=>e+t.montantRestantCDF,0)),F=r.tauxChangeUSDCDF||Pr,U={ventesDuJour:{nombreVentes:m.length,revenusCDF:C,revenusUSD:Tr(C,F)},ventesDeLaSemaine:{nombreVentes:h.length,revenusCDF:S,revenusUSD:Tr(S,F)},ventesDuMois:{nombreVentes:p.length,revenusCDF:b,revenusUSD:Tr(b,F)},articlesActifs:x.length,produitsStockBas:g.length,articlesEnRupture:y.length,valeurInventaireCDF:f,valeurInventaireUSD:f/r.tauxChangeUSDCDF,dettesActives:j.length,dettesEnRetard:D.length,montantDettesTotalCDF:w,montantDettesTotalUSD:w/r.tauxChangeUSDCDF};if(t(U),a(g.slice(0,5)),l(n.slice(-5).reverse()),E.canViewRevenue){const t=Vr(e,n);v(t)}T(n),k(n)},T=e=>{const t=Array.from({length:14},(e,t)=>{const n=kt(new Date,13-t);return{date:n,label:Ft(n,"dd/MM",{locale:jr}),sales:0,revenue:0}});e.forEach(e=>{const n=P(e.datevente);if(!n)return;const i=t.find(e=>Ft(e.date,"yyyy-MM-dd")===Ft(n,"yyyy-MM-dd"));i&&(i.sales+=1,i.revenue+=e.totalCDF)}),u({labels:t.map(e=>e.label),datasets:[{label:"Profit (CDF)",data:t.map(e=>e.revenue),borderColor:"rgb(75, 192, 192)",backgroundColor:"rgba(75, 192, 192, 0.2)",tension:.1}]})},k=e=>{const t=e.reduce((e,t)=>(e[t.methodePaiement]=(e[t.methodePaiement]||0)+1,e),{});h({labels:["Cash","Banque","Mobile Money"],datasets:[{data:[t.cash||0,t.banque||0,t.mobile_money||0],backgroundColor:["#FF6384","#36A2EB","#FFCE56"]}]})},A=e=>0===e.stock?i.jsx(z,{color:"error"}):e.stock<=e.stockMin?i.jsx($,{color:"warning"}):i.jsx(_,{color:"success"});return e?i.jsxs(r,{children:[i.jsx(o,{variant:"h4",gutterBottom:!0,children:"Tableau de bord"}),E.canViewRevenue&&j&&i.jsx(r,{sx:{mb:4},children:i.jsx(Jr,{analytics:j,products:n,todaySalesData:null==e?void 0:e.ventesDuJour,debtsData:e?{montantDettesTotalCDF:e.montantDettesTotalCDF,montantDettesTotalUSD:e.montantDettesTotalUSD,dettesActives:e.dettesActives}:void 0,isLoading:!j})}),i.jsxs(ce,{container:!0,spacing:3,sx:{mb:3},children:[i.jsx(ce,{item:!0,xs:12,sm:6,md:4,lg:2.4,children:i.jsx(ee,{children:i.jsxs(te,{children:[i.jsxs(r,{display:"flex",alignItems:"center",justifyContent:"space-between",mb:1,children:[i.jsxs(je,{size:"small",sx:{minWidth:120},children:[i.jsx(De,{children:"Période"}),i.jsxs(Ce,{value:f,label:"Période",onChange:e=>w(e.target.value),children:[i.jsx(J,{value:"jour",children:"Jour"}),i.jsx(J,{value:"semaine",children:"Semaine"}),i.jsx(J,{value:"mois",children:"Mois"})]})]}),i.jsx(C,{color:"primary",sx:{fontSize:40}})]}),i.jsxs(r,{children:[i.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:(e=>{switch(e){case"jour":default:return"Ventes du jour";case"semaine":return"Ventes de la semaine";case"mois":return"Ventes du mois"}})(f)}),i.jsx(o,{variant:"h6",children:F(f,e).nombreVentes}),E.canViewFinancials&&i.jsxs(i.Fragment,{children:[i.jsxs(o,{variant:"body2",color:"primary",fontWeight:"medium",children:[Br(F(f,e).revenusCDF,S.tauxChangeUSDCDF).primaryAmount," ",Br(F(f,e).revenusCDF,S.tauxChangeUSDCDF).primaryCurrency]}),i.jsxs(o,{variant:"caption",color:"text.secondary",children:["≈ $",Br(F(f,e).revenusCDF,S.tauxChangeUSDCDF).secondaryAmount]})]})]})]})})}),i.jsx(ce,{item:!0,xs:12,sm:6,md:4,lg:2.4,children:i.jsx(ee,{children:i.jsx(te,{children:i.jsxs(r,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[i.jsxs(r,{children:[i.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Ventes de la semaine"}),i.jsx(o,{variant:"h6",children:e.ventesDeLaSemaine.nombreVentes}),E.canViewFinancials&&i.jsxs(i.Fragment,{children:[i.jsxs(o,{variant:"body2",color:"primary",fontWeight:"medium",children:[Br(e.ventesDeLaSemaine.revenusCDF,S.tauxChangeUSDCDF).primaryAmount," ",Br(e.ventesDeLaSemaine.revenusCDF,S.tauxChangeUSDCDF).primaryCurrency]}),i.jsxs(o,{variant:"caption",color:"text.secondary",children:["≈ $",Br(e.ventesDeLaSemaine.revenusCDF,S.tauxChangeUSDCDF).secondaryAmount]})]})]}),i.jsx(ve,{color:"success",sx:{fontSize:40}})]})})})}),i.jsx(ce,{item:!0,xs:12,sm:6,md:4,lg:2.4,children:i.jsx(ee,{children:i.jsx(te,{children:i.jsxs(r,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[i.jsxs(r,{children:[i.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Articles actifs"}),i.jsx(o,{variant:"h6",children:e.articlesActifs}),e.produitsStockBas>0&&i.jsx(M,{label:`${e.produitsStockBas} stock bas`,color:"warning",size:"small"})]}),i.jsx(D,{color:"info",sx:{fontSize:40}})]})})})}),i.jsx(ce,{item:!0,xs:12,sm:6,md:4,lg:2.4,children:i.jsx(ee,{children:i.jsx(te,{children:i.jsxs(r,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[i.jsxs(r,{children:[i.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Valeur Inventaire"}),E.canViewFinancials?i.jsxs(i.Fragment,{children:[i.jsxs(o,{variant:"h6",color:"primary",fontWeight:"medium",children:[Br(e.valeurInventaireCDF,S.tauxChangeUSDCDF).primaryAmount," ",Br(e.valeurInventaireCDF,S.tauxChangeUSDCDF).primaryCurrency]}),i.jsxs(o,{variant:"body2",color:"text.secondary",children:["≈ $",Br(e.valeurInventaireCDF,S.tauxChangeUSDCDF).secondaryAmount]})]}):i.jsxs(o,{variant:"h6",children:[e.articlesActifs," articles"]})]}),i.jsx(D,{color:"success",sx:{fontSize:40}})]})})})})]}),e.articlesEnRupture>0&&i.jsx(ce,{container:!0,spacing:3,sx:{mb:3},children:i.jsx(ce,{item:!0,xs:12,children:i.jsx(O,{severity:"error",sx:{mb:2},children:i.jsxs(r,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[i.jsxs(r,{children:[i.jsx(o,{variant:"h6",component:"div",children:"Articles en rupture"}),i.jsxs(o,{variant:"body2",children:[e.articlesEnRupture," article",e.articlesEnRupture>1?"s":""," en rupture de stock nécessite",e.articlesEnRupture>1?"nt":""," un réapprovisionnement urgent"]})]}),i.jsx(z,{sx:{fontSize:40}})]})})})}),i.jsxs(ce,{container:!0,spacing:3,sx:{mb:3},children:[i.jsx(ce,{item:!0,xs:12,md:8,children:i.jsxs(c,{sx:{p:2},children:[i.jsx(o,{variant:"h6",gutterBottom:!0,children:"Tendance des ventes (14 derniers jours)"}),d&&i.jsx(_t,{data:d,options:{responsive:!0,plugins:{legend:{position:"top"}},scales:{y:{beginAtZero:!0}}}})]})}),i.jsx(ce,{item:!0,xs:12,md:4,children:i.jsxs(c,{sx:{p:2},children:[i.jsx(o,{variant:"h6",gutterBottom:!0,children:"Méthodes de paiement"}),m&&i.jsx(zt,{data:m,options:{responsive:!0,plugins:{legend:{position:"bottom"}}}})]})})]}),i.jsxs(ce,{container:!0,spacing:3,children:[i.jsx(ce,{item:!0,xs:12,md:6,children:i.jsxs(c,{sx:{p:2},children:[i.jsx(o,{variant:"h6",gutterBottom:!0,children:"Produits en stock bas"}),0===n.length?i.jsx(O,{severity:"success",children:"Aucun produit en stock bas"}):i.jsx(p,{dense:!0,children:n.map(e=>i.jsxs(x,{children:[i.jsx(g,{children:A(e)}),i.jsx(y,{primary:e.nom,secondary:`Stock: ${e.stock} (Min: ${e.stockMin})`})]},e.id))})]})}),i.jsx(ce,{item:!0,xs:12,md:6,children:i.jsxs(c,{sx:{p:2},children:[i.jsx(o,{variant:"h6",gutterBottom:!0,children:"Ventes récentes"}),0===s.length?i.jsx(O,{severity:"info",children:"Aucune vente récente"}):i.jsx(p,{dense:!0,children:s.map(e=>i.jsx(x,{children:i.jsx(y,{primary:E.canViewFinancials?`${e.nomClient} - ${Br(e.totalCDF,S.tauxChangeUSDCDF).primaryAmount} ${Br(e.totalCDF,S.tauxChangeUSDCDF).primaryCurrency}`:e.nomClient,secondary:(()=>{const t=P(e.datevente),n=t?Ft(t,"dd/MM/yyyy HH:mm",{locale:jr}):"Date invalide";if(E.canViewFinancials){return`${n} • ≈ $${Br(e.totalCDF,S.tauxChangeUSDCDF).secondaryAmount}`}return n})()})},e.id))})]})})]})]}):i.jsx(o,{children:"Chargement..."})},Gr=({label:e,value:t,onChange:n,min:a=0,max:s=Number.MAX_SAFE_INTEGER,step:l=100,exchangeRate:c,disabled:d=!1,required:u=!1,error:m=!1,helperText:h,allowUSDInput:p=!0,onCurrencyModeChange:x})=>{const[g,y]=vt.useState("CDF"),[j,D]=vt.useState("");vt.useEffect(()=>{if("CDF"===g)D(t.toString());else{const e=Wr(t,c);D(e.toFixed(2))}},[t,g,c]);const C=Wr(t,c),v=t;return i.jsxs(je,{fullWidth:!0,disabled:d,children:[i.jsxs(Se,{component:"legend",sx:{mb:1},children:[e," ",u&&"*"]}),i.jsxs(ce,{container:!0,spacing:2,children:[p&&i.jsx(ce,{item:!0,xs:12,children:i.jsxs(be,{value:g,exclusive:!0,onChange:(e,n)=>{if(n&&n!==g){if(y(n),"USD"===n){const e=Wr(t,c);D(e.toFixed(2))}else D(t.toString());x&&x(n)}},size:"small",disabled:d,children:[i.jsx(fe,{value:"CDF",children:"Saisie en CDF"}),i.jsx(fe,{value:"USD",children:"Saisie en USD"})]})}),i.jsx(ce,{item:!0,xs:12,children:i.jsx(ne,{fullWidth:!0,label:`Montant (${g})`,type:"text",value:j,onChange:e=>{const t=e.target.value;D(t);const i=parseFloat(t)||0;let r;r="CDF"===g?i:Xr(i,c);Rr(r,"Le montant",{allowZero:!0,allowNegative:!1,minValue:a,maxValue:s}).isValid&&(r=Math.max(a,Math.min(s,r)),n(r))},disabled:d,error:m,helperText:h,inputProps:{inputMode:"decimal",pattern:"CDF"===g?"[0-9]*":"[0-9]*\\.?[0-9]*"},InputProps:{startAdornment:i.jsx(ie,{position:"start",children:"USD"===g?"$":""}),endAdornment:i.jsx(ie,{position:"end",children:"CDF"===g?"CDF":"USD"})},sx:{"& input[type=number]":{MozAppearance:"textfield","&::-webkit-outer-spin-button":{WebkitAppearance:"none",margin:0},"&::-webkit-inner-spin-button":{WebkitAppearance:"none",margin:0}}}})}),i.jsx(ce,{item:!0,xs:12,children:i.jsxs(r,{sx:{p:2,bgcolor:"grey.50",borderRadius:1,border:"1px solid",borderColor:"grey.300"},children:[i.jsx(o,{variant:"body2",color:"text.secondary",gutterBottom:!0,children:"Équivalences:"}),i.jsx(o,{variant:"body1",color:"primary",fontWeight:"medium",children:qr(v,"CDF")}),i.jsxs(o,{variant:"body2",color:"text.secondary",children:["≈ ",qr(C,"USD")]})]})})]})]})},Yr=({value:e,onChange:t,min:n=1,max:a=Number.MAX_SAFE_INTEGER,disabled:s=!1,size:o="small",showButtons:l=!0,allowDirectInput:c=!0,label:d,error:u=!1,helperText:m})=>{const[h,p]=vt.useState(e.toString());vt.useEffect(()=>{p(e.toString())},[e]);const x=i=>{const r=i.target.value;if(p(r),""===r)return;const s=parseFloat(r);if(!isNaN(s)){const i=Math.round(s),r=Math.max(n,Math.min(a,i));i>=n&&i<=a?t(i):r!==e&&t(r)}},g=()=>{if(""===h||isNaN(parseFloat(h))){const i=e||n;return p(i.toString()),void(i!==e&&t(i))}const i=Math.round(parseFloat(h)),r=Math.max(n,Math.min(a,i));p(r.toString()),r!==e&&t(r)},y=()=>{const n=Math.min(a,e+1);t(n)},j=()=>{const i=Math.max(n,e-1);t(i)},D=e=>{e.ctrlKey&&["a","c","v","x"].includes(e.key.toLowerCase())||(/[0-9.]/.test(e.key)||["Backspace","Delete","ArrowLeft","ArrowRight","ArrowUp","ArrowDown","Tab","Enter","Home","End","Escape","."].includes(e.key)||e.preventDefault(),"."===e.key&&h.includes(".")&&e.preventDefault())};return!c&&l?i.jsxs(r,{display:"flex",alignItems:"center",gap:.5,children:[i.jsx(U,{size:o,onClick:j,disabled:s||e<=n,children:i.jsx(we,{fontSize:o})}),i.jsx(r,{sx:{minWidth:"small"===o?"30px":"40px",textAlign:"center",fontWeight:"medium",fontSize:"small"===o?"0.875rem":"1rem"},children:e}),i.jsx(U,{size:o,onClick:y,disabled:s||e>=a,children:i.jsx(Ee,{fontSize:o})})]}):c&&!l?i.jsx(ne,{size:o,label:d,type:"text",value:h,onChange:x,onBlur:g,onKeyPress:D,disabled:s,error:u,helperText:m,inputProps:{inputMode:"numeric",pattern:"[0-9]*"},sx:{minWidth:"80px","& input[type=number]":{MozAppearance:"textfield","&::-webkit-outer-spin-button":{WebkitAppearance:"none",margin:0},"&::-webkit-inner-spin-button":{WebkitAppearance:"none",margin:0}}}}):i.jsx(ne,{size:o,label:d,type:"text",value:h,onChange:x,onBlur:g,onKeyPress:D,disabled:s,error:u,helperText:m||(l?"Tapez directement la quantité (recommandé) ou utilisez +/-":"Tapez directement la quantité désirée"),placeholder:"Tapez la quantité...",inputProps:{inputMode:"numeric",pattern:"[0-9]*",style:{textAlign:"center",fontSize:"small"===o?"0.875rem":"1rem",fontWeight:500}},InputProps:{startAdornment:l?i.jsx(ie,{position:"start",children:i.jsx(U,{size:o,onClick:j,disabled:s||e<=n,edge:"start",sx:{opacity:.6,"&:hover":{opacity:.8},transition:"opacity 0.2s ease"},children:i.jsx(we,{fontSize:o})})}):void 0,endAdornment:l?i.jsx(ie,{position:"end",children:i.jsx(U,{size:o,onClick:y,disabled:s||e>=a,edge:"end",sx:{opacity:.6,"&:hover":{opacity:.8},transition:"opacity 0.2s ease"},children:i.jsx(Ee,{fontSize:o})})}):void 0},sx:{minWidth:l?"160px":"100px","& .MuiOutlinedInput-root":{"&:hover fieldset":{borderColor:"primary.main"},"&.Mui-focused fieldset":{borderWidth:2,borderColor:"primary.main"},"& input":{cursor:"text","&:focus":{backgroundColor:"rgba(25, 118, 210, 0.04)"}}},"& input[type=number]":{MozAppearance:"textfield","&::-webkit-outer-spin-button":{WebkitAppearance:"none",margin:0},"&::-webkit-inner-spin-button":{WebkitAppearance:"none",margin:0}}}})},Kr=()=>{const[e,t]=vt.useState([]),[n,a]=vt.useState([]),[s,l]=vt.useState([]),[d,u]=vt.useState(""),[m,h]=vt.useState(""),[p,x]=vt.useState("all"),[g,y]=vt.useState(0),[j,C]=vt.useState(10),[v,S]=vt.useState(!1),[b,f]=vt.useState(null),[w,E]=vt.useState({nom:"",description:"",prixAchatCDF:0,prixCDF:0,categorie:"",stock:0,stockMin:0,quantiteEnStock:0,coutAchatStockCDF:0,prixParPieceCDF:0}),[P,T]=vt.useState(""),[k,A]=vt.useState(""),[R,N]=vt.useState(!1),[L,V]=vt.useState(""),[B,W]=vt.useState({tauxChangeUSDCDF:2800}),X=mr.getUserPermissions();vt.useEffect(()=>{Q()},[]);vt.useEffect(()=>{e.length>0&&Cr.checkLowStock(e)},[e]),vt.useEffect(()=>{H()},[e,d,m,p]),vt.useEffect(()=>{-1===j&&C(s.length||1)},[s.length,j]);const Q=async()=>{try{const e=await ur.getProducts(),n=await ur.getSettings(),i=e.map(e=>{const t=(new Date).toISOString();return{...e,dateCreation:e.dateCreation&&!isNaN(new Date(e.dateCreation).getTime())?e.dateCreation:t,dateModification:e.dateModification&&!isNaN(new Date(e.dateModification).getTime())?e.dateModification:t}});t(i),a(n.categories),W(n)}catch(e){console.error("Error loading products:",e),T("Erreur lors du chargement des produits")}},H=()=>{let t=e;d&&(t=t.filter(e=>e.nom.toLowerCase().includes(d.toLowerCase())||e.codeQR.toLowerCase().includes(d.toLowerCase())||e.description.toLowerCase().includes(d.toLowerCase()))),m&&(t=t.filter(e=>e.categorie===m)),"all"!==p&&(t=t.filter(e=>_r(e)===p)),l(t)},G=e=>{switch(e){case"out_of_stock":return"error";case"low_stock":return"warning";case"in_stock":return"success";default:return"default"}},Y=e=>{switch(e){case"out_of_stock":return i.jsx(z,{});case"low_stock":return i.jsx($,{});case"in_stock":return i.jsx(_,{});default:return i.jsx(D,{})}},K=e=>{switch(e){case"out_of_stock":return"Rupture";case"low_stock":return"Stock bas";case"in_stock":return"En stock";default:return"Inconnu"}},Z=e=>{e?(f(e),E({nom:e.nom,description:e.description,prixAchatCDF:e.prixAchatCDF||0,prixCDF:e.prixCDF,categorie:e.categorie,stock:e.stock,stockMin:e.stockMin,quantiteEnStock:e.quantiteEnStock||e.stock,coutAchatStockCDF:e.coutAchatStockCDF||e.prixAchatCDF*e.stock,prixParPieceCDF:e.prixParPieceCDF||e.prixCDF})):(f(null),E({nom:"",description:"",prixAchatCDF:0,prixCDF:0,categorie:"",stock:0,stockMin:0,quantiteEnStock:0,coutAchatStockCDF:0,prixParPieceCDF:0})),S(!0),T(""),A(""),setTimeout(()=>{document.querySelectorAll('div[role="dialog"] input, div[role="dialog"] textarea').forEach(e=>{const t=e;t.style.pointerEvents="auto",t.style.userSelect="text",(!t.hasAttribute("tabindex")||t.tabIndex<0)&&(t.tabIndex=0)})},100)},re=()=>{S(!1),f(null),T(""),A("")},ae=e.length,se=e.filter(e=>"in_stock"===_r(e)).length,oe=e.filter(e=>"low_stock"===_r(e)).length,le=e.reduce((e,t)=>e+t.prixCDF*t.stock,0);return i.jsxs(r,{children:[i.jsxs(r,{display:"flex",justifyContent:"space-between",alignItems:"center",mb:3,children:[i.jsx(o,{variant:"h4",children:"Inventaire"}),X.canManageProducts&&i.jsx(I,{variant:"contained",startIcon:i.jsx(Ee,{}),onClick:()=>Z(),children:"Nouveau Produit"})]}),k&&i.jsx(O,{severity:"success",sx:{mb:2},onClose:()=>A(""),children:k}),P&&i.jsx(O,{severity:"error",sx:{mb:2},action:i.jsx(I,{color:"inherit",size:"small",onClick:()=>{window.confirm("Cela va supprimer toutes les données et réinitialiser l'application. Continuer?")&&(localStorage.clear(),window.location.reload())},children:"Réinitialiser les données"}),children:P}),i.jsxs(ce,{container:!0,spacing:3,sx:{mb:3},children:[i.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:i.jsx(ee,{children:i.jsx(te,{children:i.jsxs(r,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[i.jsxs(r,{children:[i.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Total Produits"}),i.jsx(o,{variant:"h6",children:ae})]}),i.jsx(D,{color:"primary",sx:{fontSize:40}})]})})})}),i.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:i.jsx(ee,{children:i.jsx(te,{children:i.jsxs(r,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[i.jsxs(r,{children:[i.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"En Stock"}),i.jsx(o,{variant:"h6",color:"success.main",children:se})]}),i.jsx(_,{color:"success",sx:{fontSize:40}})]})})})}),i.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:i.jsx(ee,{children:i.jsx(te,{children:i.jsxs(r,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[i.jsxs(r,{children:[i.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Stock Bas"}),i.jsx(o,{variant:"h6",color:"warning.main",children:oe})]}),i.jsx($,{color:"warning",sx:{fontSize:40}})]})})})}),i.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:i.jsx(ee,{children:i.jsx(te,{children:i.jsxs(r,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[i.jsxs(r,{children:[i.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Valeur Inventaire"}),i.jsxs(o,{variant:"h6",color:"primary",fontWeight:"medium",children:[Br(le,B.tauxChangeUSDCDF).primaryAmount," ",Br(le,B.tauxChangeUSDCDF).primaryCurrency]}),i.jsxs(o,{variant:"body2",color:"text.secondary",children:["≈ $",Br(le,B.tauxChangeUSDCDF).secondaryAmount]})]}),i.jsx(D,{color:"info",sx:{fontSize:40}})]})})})})]}),i.jsx(c,{sx:{p:2,mb:3},children:i.jsxs(ce,{container:!0,spacing:2,alignItems:"center",children:[i.jsx(ce,{item:!0,xs:12,md:4,children:i.jsx(ne,{fullWidth:!0,placeholder:"Rechercher par nom, Code QR ou description...",value:d,onChange:e=>u(e.target.value),InputProps:{startAdornment:i.jsx(ie,{position:"start",children:i.jsx(Pe,{})})}})}),i.jsx(ce,{item:!0,xs:12,md:3,children:i.jsxs(je,{fullWidth:!0,children:[i.jsx(De,{children:"Catégorie"}),i.jsxs(Ce,{value:m,label:"Catégorie",onChange:e=>h(e.target.value),children:[i.jsx(J,{value:"",children:"Toutes les catégories"}),n.map(e=>i.jsx(J,{value:e.nom,children:e.nom},e.id))]})]})}),i.jsx(ce,{item:!0,xs:12,md:3,children:i.jsxs(je,{fullWidth:!0,children:[i.jsx(De,{children:"Statut Stock"}),i.jsxs(Ce,{value:p,label:"Statut Stock",onChange:e=>x(e.target.value),children:[i.jsx(J,{value:"all",children:"Tous"}),i.jsx(J,{value:"in_stock",children:"En stock"}),i.jsx(J,{value:"low_stock",children:"Stock bas"}),i.jsx(J,{value:"out_of_stock",children:"Rupture"})]})]})}),i.jsx(ce,{item:!0,xs:12,md:2,children:i.jsxs(r,{display:"flex",gap:1,children:[i.jsx(F,{title:"Exporter CSV",children:i.jsx(U,{onClick:()=>{const e=`SmartBoutique_Produits_${(new Date).toISOString().split("T")[0]}.csv`;Li.downloadCSV(s,Oi,e),A("Produits exportés en CSV avec succès (compatible Excel)"),setTimeout(()=>A(""),3e3)},children:i.jsx(Fe,{})})}),i.jsx(F,{title:"Importer CSV",children:i.jsx(U,{onClick:()=>N(!0),children:i.jsx(Ue,{})})})]})})]})}),i.jsxs(me,{component:c,children:[i.jsxs(he,{children:[i.jsx(pe,{children:i.jsxs(xe,{children:[i.jsx(ge,{children:"Produit"}),i.jsx(ge,{children:"Code QR"}),i.jsx(ge,{children:"Catégorie"}),i.jsx(ge,{align:"right",children:"Prix de Vente"}),i.jsx(ge,{align:"center",children:"Stock"}),i.jsx(ge,{align:"center",children:"Qté Stock"}),i.jsx(ge,{align:"right",children:"Coût Stock"}),i.jsx(ge,{align:"center",children:"Statut"}),i.jsx(ge,{children:"Dernière Modif."}),X.canManageProducts&&i.jsx(ge,{align:"center",children:"Actions"})]})}),i.jsx(ye,{children:(-1===j?s:s.slice(g*j,g*j+j)).map(n=>{const a=_r(n);return i.jsxs(xe,{hover:!0,onClick:()=>Z(n),sx:{cursor:"pointer"},children:[i.jsx(ge,{children:i.jsxs(r,{children:[i.jsx(o,{variant:"subtitle2",children:n.nom}),i.jsx(o,{variant:"caption",color:"text.secondary",children:n.description})]})}),i.jsx(ge,{children:i.jsxs(r,{display:"flex",alignItems:"center",gap:1,children:[n.codeQR,i.jsx(F,{title:"Code QR",children:i.jsx(U,{size:"small",children:i.jsx(Te,{fontSize:"small"})})})]})}),i.jsx(ge,{children:n.categorie}),i.jsx(ge,{align:"right",children:i.jsxs(r,{children:[i.jsxs(o,{variant:"body2",fontWeight:"medium",children:[Br(n.prixCDF,B.tauxChangeUSDCDF).primaryAmount," ",Br(n.prixCDF,B.tauxChangeUSDCDF).primaryCurrency]}),i.jsxs(o,{variant:"caption",color:"text.secondary",children:["≈ $",Br(n.prixCDF,B.tauxChangeUSDCDF).secondaryAmount]})]})}),i.jsx(ge,{align:"center",children:i.jsxs(r,{children:[i.jsx(o,{variant:"body2",children:n.stock}),i.jsxs(o,{variant:"caption",color:"text.secondary",children:["Min: ",n.stockMin]})]})}),i.jsx(ge,{align:"center",children:i.jsxs(r,{children:[i.jsx(o,{variant:"body2",fontWeight:"medium",children:n.quantiteEnStock||n.stock}),i.jsx(o,{variant:"caption",color:"text.secondary",children:"Réel"})]})}),i.jsx(ge,{align:"right",children:i.jsxs(r,{children:[i.jsx(o,{variant:"body2",fontWeight:"medium",children:n.coutAchatStockCDF?`${Br(n.coutAchatStockCDF,B.tauxChangeUSDCDF).primaryAmount} ${Br(n.coutAchatStockCDF,B.tauxChangeUSDCDF).primaryCurrency}`:`${Br(n.prixAchatCDF*n.stock,B.tauxChangeUSDCDF).primaryAmount} ${Br(n.prixAchatCDF*n.stock,B.tauxChangeUSDCDF).primaryCurrency}`}),i.jsx(o,{variant:"caption",color:"text.secondary",children:n.coutAchatStockCDF?`≈ $${Br(n.coutAchatStockCDF,B.tauxChangeUSDCDF).secondaryAmount} • Total`:`≈ $${Br(n.prixAchatCDF*n.stock,B.tauxChangeUSDCDF).secondaryAmount} • Total`})]})}),i.jsx(ge,{align:"center",children:i.jsx(M,{icon:Y(a),label:K(a),color:G(a),size:"small"})}),i.jsx(ge,{children:(()=>{try{const e=new Date(n.dateModification);return isNaN(e.getTime())?"Date invalide":Ft(e,"dd/MM/yyyy",{locale:jr})}catch(e){return"Date invalide"}})()}),X.canManageProducts&&i.jsx(ge,{align:"center",children:i.jsxs(r,{display:"flex",gap:1,children:[i.jsx(F,{title:"Modifier",children:i.jsx(U,{size:"small",onClick:()=>Z(n),children:i.jsx(ke,{fontSize:"small"})})}),mr.hasRole(["super_admin"])&&i.jsx(F,{title:"Supprimer",children:i.jsx(U,{size:"small",color:"error",onClick:()=>(async n=>{if(window.confirm(`Êtes-vous sûr de vouloir supprimer le produit "${n.nom}" ?`)){const i=e.filter(e=>e.id!==n.id);t(i),await ur.setProducts(i),A("Produit supprimé avec succès"),setTimeout(()=>A(""),3e3)}})(n),children:i.jsx(q,{fontSize:"small"})})})]})})]},n.id)})})]}),i.jsx(Ae,{rowsPerPageOptions:[5,10,25,50,100,{label:"Voir tout",value:-1}],component:"div",count:s.length,rowsPerPage:-1===j?s.length:j,page:-1===j?0:g,onPageChange:(e,t)=>{-1!==j&&y(t)},onRowsPerPageChange:e=>{const t=parseInt(e.target.value,10);C(t),y(0)},labelRowsPerPage:"Lignes par page:",labelDisplayedRows:({from:e,to:t,count:n})=>-1===j?`Affichage de tous les ${n} éléments`:`${e}-${t} sur ${-1!==n?n:`plus de ${t}`}`})]}),i.jsxs(Re,{open:v,onClose:re,maxWidth:"md",fullWidth:!0,children:[i.jsx(Me,{children:b?"Modifier le Produit":"Nouveau Produit"}),i.jsxs(Ie,{children:[P&&i.jsx(O,{severity:"error",sx:{mb:2},children:P}),k&&i.jsx(O,{severity:"success",sx:{mb:2},children:k}),i.jsxs(ce,{container:!0,spacing:2,sx:{mt:1},children:[i.jsx(ce,{item:!0,xs:12,md:6,children:i.jsx(ne,{fullWidth:!0,label:"Nom du produit *",value:w.nom,onChange:e=>E({...w,nom:e.target.value})})}),i.jsx(ce,{item:!0,xs:12,children:i.jsx(ne,{fullWidth:!0,label:"Description",multiline:!0,rows:3,value:w.description,onChange:e=>E({...w,description:e.target.value})})}),i.jsx(ce,{item:!0,xs:12,md:6,children:i.jsx(Gr,{label:"Prix d'achat *",value:w.prixAchatCDF,onChange:e=>E({...w,prixAchatCDF:e}),min:0,step:100,exchangeRate:B.tauxChangeUSDCDF,required:!0,allowUSDInput:!0,error:w.prixAchatCDF<=0,helperText:w.prixAchatCDF<=0?"Le prix d'achat doit être supérieur à zéro":"Prix d'achat du produit en CDF"})}),i.jsx(ce,{item:!0,xs:12,md:6,children:i.jsx(Gr,{label:"Prix de vente *",value:w.prixCDF,onChange:e=>E({...w,prixCDF:e}),min:0,step:100,exchangeRate:B.tauxChangeUSDCDF,required:!0,allowUSDInput:!0,error:w.prixCDF<=w.prixAchatCDF,helperText:w.prixCDF<=w.prixAchatCDF?"Le prix de vente doit être supérieur au prix d'achat pour générer un bénéfice":"Prix de vente du produit en CDF"})}),i.jsx(ce,{item:!0,xs:12,md:6,children:i.jsxs(je,{fullWidth:!0,children:[i.jsx(De,{children:"Catégorie *"}),i.jsx(Ce,{value:w.categorie,label:"Catégorie *",onChange:e=>E({...w,categorie:e.target.value}),children:n.map(e=>i.jsx(J,{value:e.nom,children:e.nom},e.id))})]})}),i.jsx(ce,{item:!0,xs:12,md:6,children:i.jsx(Yr,{value:w.stock,onChange:e=>E({...w,stock:e}),min:0,max:999999,size:"medium",showButtons:!0,allowDirectInput:!0,label:"Stock actuel",helperText:"Quantité actuelle en stock"})}),i.jsx(ce,{item:!0,xs:12,md:6,children:i.jsx(Yr,{value:w.stockMin,onChange:e=>E({...w,stockMin:e}),min:0,max:999999,size:"medium",showButtons:!0,allowDirectInput:!0,label:"Stock minimum",helperText:"Seuil d'alerte pour stock bas"})}),i.jsx(ce,{item:!0,xs:12,children:i.jsx(o,{variant:"h6",sx:{mt:2,mb:1,color:"primary.main"},children:"Gestion Avancée de l'Inventaire"})}),i.jsx(ce,{item:!0,xs:12,md:6,children:i.jsx(Yr,{value:w.quantiteEnStock,onChange:e=>E({...w,quantiteEnStock:e}),min:0,max:999999,size:"medium",showButtons:!0,allowDirectInput:!0,label:"Quantité en Stock",helperText:"Quantité réelle en stock (éditable)"})}),i.jsx(ce,{item:!0,xs:12,md:6,children:i.jsx(Gr,{label:"Coût d'Achat du Stock",value:w.coutAchatStockCDF,onChange:e=>E({...w,coutAchatStockCDF:e}),min:0,step:100,exchangeRate:B.tauxChangeUSDCDF,allowUSDInput:!0,helperText:"Coût total d'achat du stock actuel"})}),i.jsx(ce,{item:!0,xs:12,md:6,children:i.jsx(Gr,{label:"Prix par Pièce",value:w.prixParPieceCDF,onChange:e=>E({...w,prixParPieceCDF:e}),min:0,step:100,exchangeRate:B.tauxChangeUSDCDF,allowUSDInput:!0,helperText:"Prix unitaire pour vente au détail"})}),i.jsx(ce,{item:!0,xs:12,children:i.jsx(o,{variant:"h6",sx:{mt:2,mb:1,color:"primary.main"},children:"Valeur du Stock avec Bénéfice"})}),i.jsx(ce,{item:!0,xs:12,md:6,children:i.jsx(ne,{fullWidth:!0,label:"Valeur Stock avec Bénéfice (CDF)",value:(()=>{const e=(w.quantiteEnStock||0)*(w.prixParPieceCDF||w.prixCDF||0);return qr(e,"CDF")})(),InputProps:{readOnly:!0,style:{backgroundColor:"#f5f5f5",fontWeight:"bold",color:"#1976d2"}},helperText:"Valeur totale du stock actuel avec marge bénéficiaire incluse",variant:"outlined"})}),i.jsx(ce,{item:!0,xs:12,md:6,children:i.jsx(ne,{fullWidth:!0,label:"Valeur Stock avec Bénéfice (USD)",value:(()=>{const e=(w.quantiteEnStock||0)*(w.prixParPieceCDF||w.prixCDF||0)/(B.tauxChangeUSDCDF||2800);return qr(e,"USD")})(),InputProps:{readOnly:!0,style:{backgroundColor:"#f5f5f5",fontWeight:"bold",color:"#1976d2"}},helperText:"Équivalent en USD de la valeur du stock avec bénéfice",variant:"outlined"})})]})]}),i.jsxs(Ne,{children:[i.jsx(I,{onClick:re,children:"Annuler"}),i.jsx(I,{onClick:async()=>{if(!w.nom.trim())return void T("Le nom du produit est requis");if(w.prixAchatCDF<=0)return void T("Le prix d'achat doit être supérieur à zéro");if(w.prixCDF<=0)return void T("Le prix de vente doit être supérieur à zéro");if(w.stock<0)return void T("Le stock ne peut pas être négatif");if(w.stockMin<0)return void T("Le stock minimum ne peut pas être négatif");if(!w.categorie)return void T("La catégorie est requise");const n=Lr(w.prixAchatCDF,w.prixCDF);if(!n.isValid)return void T(n.errorMessage||"Erreur de validation des prix");const i=await ur.getSettings(),r=(new Date).toISOString(),a=Nr(w.prixAchatCDF,w.prixCDF,i.tauxChangeUSDCDF);if(b){const n={...b,nom:w.nom.trim(),description:w.description.trim(),prixAchatCDF:w.prixAchatCDF,prixAchatUSD:Qr(w.prixAchatCDF,i.tauxChangeUSDCDF),prixCDF:w.prixCDF,prixUSD:Qr(w.prixCDF,i.tauxChangeUSDCDF),beneficeUnitaireCDF:a.beneficeUnitaireCDF,beneficeUnitaireUSD:a.beneficeUnitaireUSD,categorie:w.categorie,stock:w.stock,stockMin:w.stockMin,dateModification:r,quantiteEnStock:w.quantiteEnStock,coutAchatStockCDF:w.coutAchatStockCDF,coutAchatStockUSD:Qr(w.coutAchatStockCDF,i.tauxChangeUSDCDF),prixParPieceCDF:w.prixParPieceCDF,prixParPieceUSD:Qr(w.prixParPieceCDF,i.tauxChangeUSDCDF)},s=e.map(e=>e.id===b.id?n:e);t(s),await ur.setProducts(s),A("Produit mis à jour avec succès")}else{const n={id:Date.now().toString(),nom:w.nom.trim(),description:w.description.trim(),prixAchatCDF:w.prixAchatCDF,prixAchatUSD:Qr(w.prixAchatCDF,i.tauxChangeUSDCDF),prixCDF:w.prixCDF,prixUSD:Qr(w.prixCDF,i.tauxChangeUSDCDF),beneficeUnitaireCDF:a.beneficeUnitaireCDF,beneficeUnitaireUSD:a.beneficeUnitaireUSD,codeQR:$r(),categorie:w.categorie,stock:w.stock,stockMin:w.stockMin,codeBarres:zr(),dateCreation:r,dateModification:r,quantiteEnStock:w.quantiteEnStock,coutAchatStockCDF:w.coutAchatStockCDF,coutAchatStockUSD:Qr(w.coutAchatStockCDF,i.tauxChangeUSDCDF),prixParPieceCDF:w.prixParPieceCDF,prixParPieceUSD:Qr(w.prixParPieceCDF,i.tauxChangeUSDCDF)},s=[...e,n];t(s),await ur.setProducts(s),A("Produit créé avec succès")}setTimeout(()=>{re()},1500)},variant:"contained",children:b?"Mettre à jour":"Créer"})]})]}),i.jsxs(Re,{open:R,onClose:()=>N(!1),maxWidth:"md",fullWidth:!0,children:[i.jsx(Me,{children:"Importer des Produits depuis CSV"}),i.jsxs(Ie,{children:[i.jsx(Le,{sx:{mb:2},children:"Collez le contenu CSV des produits à importer. Format attendu: ID, Nom du Produit, Description, Prix CDF, Prix USD, Code QR, Catégorie, Stock, Stock Minimum, Code Barres, Date de Création, Date de Modification"}),i.jsx(ne,{fullWidth:!0,multiline:!0,rows:10,value:L,onChange:e=>V(e.target.value),placeholder:"ID,Nom du Produit,Description,Prix CDF,Prix USD,Code QR,Catégorie,Stock,Stock Minimum,Code Barres,Date de Création,Date de Modification\n1,iPhone 15,Smartphone Apple,2240000,800,SB123,Électronique,25,5,1234567890123,2024-01-01,2024-01-01",variant:"outlined"})]}),i.jsxs(Ne,{children:[i.jsx(I,{onClick:()=>N(!1),children:"Annuler"}),i.jsx(I,{onClick:async()=>{if(L.trim())try{const n=Li.csvToArray(L,Oi),i=Li.validateCSVData(n,Oi);if(!i.isValid)return void T("Données CSV invalides: "+i.errors.join(", "));const r=n.map((e,t)=>({...e,id:e.id||Date.now().toString()+t,dateCreation:e.dateCreation||(new Date).toISOString(),dateModification:e.dateModification||(new Date).toISOString(),codeQR:e.codeQR||$r(),codeBarres:e.codeBarres||zr()})),a=new Set(e.map(e=>e.id)),s=r.filter(e=>!a.has(e.id)),o=[...e,...s];t(o),await ur.setProducts(o),A(`${s.length} produits importés avec succès`),N(!1),V(""),setTimeout(()=>A(""),3e3)}catch(n){T("Erreur lors de l'importation: "+n.message)}else T("Veuillez saisir le contenu CSV à importer")},variant:"contained",children:"Importer"})]})]})]})};class Zr{static async generateSalesReceiptNumber(){const e=(new Date).toISOString().slice(0,10).replace(/-/g,""),t=((await ur.getSales()).filter(t=>{var n;return new Date(t.datevente).toISOString().slice(0,10).replace(/-/g,"")===e&&(null==(n=t.numeroRecu)?void 0:n.startsWith(`RV-${e}`))}).length+1).toString().padStart(4,"0");return`RV-${e}-${t}`}static async generateExpenseReceiptNumber(){const e=(new Date).toISOString().slice(0,10).replace(/-/g,""),t=((await ur.getExpenses()).filter(t=>{var n;return new Date(t.dateDepense).toISOString().slice(0,10).replace(/-/g,"")===e&&(null==(n=t.numeroRecu)?void 0:n.startsWith(`RD-${e}`))}).length+1).toString().padStart(4,"0");return`RD-${e}-${t}`}static async createSalesReceiptData(e){const t=await ur.getSettings();return{type:"sale",numero:e.numeroRecu||await this.generateSalesReceiptNumber(),date:e.datevente,entreprise:t.entreprise,sale:e,vendeur:e.vendeur}}static async createExpenseReceiptData(e){const t=await ur.getSettings();return{type:"expense",numero:e.numeroRecu||await this.generateExpenseReceiptNumber(),date:e.dateDepense,entreprise:t.entreprise,expense:e,creePar:e.creePar}}static getPaymentMethodLabel(e){switch(e){case"cash":return"Cash";case"banque":return"Banque";case"mobile_money":return"Mobile Money";default:return e}}static formatCurrency(e,t){const{formatCurrency:n}=require("@/utils/currencyUtils.js");return n(e,t)}static formatReceiptDate(e){return new Date(e).toLocaleDateString("fr-FR",{day:"2-digit",month:"2-digit",year:"numeric",hour:"2-digit",minute:"2-digit"})}static async printReceipt(){return new Promise(e=>{try{if("undefined"!=typeof window&&window.process&&"renderer"===window.process.type)try{const{ipcRenderer:t}=window.require("electron"),n={silent:!1,printBackground:!0,color:!1,margin:{marginType:"none"},landscape:!1,pagesPerSheet:1,collate:!1,copies:1,header:"",footer:""};t.invoke("print-receipt",n).then(()=>{console.log("Receipt printed successfully via Electron IPC"),e()}).catch(t=>{console.error("Electron IPC print failed:",t),window.print(),e()})}catch(t){console.error("IPC not available, falling back to window.print():",t),window.print(),e()}else window.print(),e()}catch(ba){console.error("Error during printing:",ba),window.print(),e()}})}static async shouldAutoPrint(){var e;return(null==(e=(await ur.getSettings()).impression)?void 0:e.impressionAutomatique)||!1}static async getPaperSize(){var e;return(null==(e=(await ur.getSettings()).impression)?void 0:e.taillePapier)||"thermal"}}const ea=({receiptData:e,paperSize:t="thermal"})=>{const{sale:n,entreprise:a,numero:s,vendeur:l}=e;return i.jsxs(c,{className:"receipt-container "+("thermal"===t?"thermal-receipt":"a4-receipt"),elevation:0,sx:{p:2,fontFamily:"monospace",fontSize:"thermal"===t?"12px":"14px",lineHeight:1.4,maxWidth:"thermal"===t?"80mm":"210mm",margin:"0 auto",backgroundColor:"white",color:"black","@media print":{backgroundColor:"white !important",color:"black !important",boxShadow:"none !important",border:"none !important"}},children:[i.jsxs(r,{textAlign:"center",mb:2,children:[a.logo&&i.jsx(r,{mb:1,display:"flex",justifyContent:"center",alignItems:"center",children:i.jsx(r,{component:"img",src:a.logo,alt:`${a.nom} Logo`,sx:{maxWidth:"thermal"===t?"70mm":"150px",maxHeight:"60px",objectFit:"contain",mb:1}})}),i.jsx(o,{variant:"h6",fontWeight:"bold",sx:{fontSize:"16px"},children:a.nom}),i.jsx(o,{variant:"body2",sx:{fontSize:"12px"},children:a.adresse}),i.jsxs(o,{variant:"body2",sx:{fontSize:"12px"},children:["Tél: ",a.telephone]}),a.rccm&&i.jsxs(o,{variant:"body2",sx:{fontSize:"11px"},children:["RCCM: ",a.rccm]}),a.idNat&&i.jsxs(o,{variant:"body2",sx:{fontSize:"11px"},children:["ID NAT: ",a.idNat]})]}),i.jsx(h,{sx:{my:1}}),i.jsxs(r,{textAlign:"center",mb:2,children:[i.jsxs(o,{variant:"h6",fontWeight:"bold",children:["REÇU DE VENTE #",s]}),i.jsx(o,{variant:"body2",children:Zr.formatReceiptDate(n.datevente)})]}),i.jsx(r,{mb:1,children:i.jsxs(o,{variant:"body2",children:[i.jsx("strong",{children:"Vendeur:"})," ",l]})}),i.jsxs(r,{mb:2,children:[i.jsxs(o,{variant:"body2",children:[i.jsx("strong",{children:"Client:"})," ",n.nomClient]}),n.telephoneClient&&i.jsxs(o,{variant:"body2",children:[i.jsx("strong",{children:"Téléphone:"})," ",n.telephoneClient]}),n.adresseClient&&i.jsxs(o,{variant:"body2",children:[i.jsx("strong",{children:"Adresse:"})," ",n.adresseClient]})]}),i.jsx(h,{sx:{my:1}}),i.jsx(me,{children:i.jsxs(he,{size:"small",sx:{"& .MuiTableCell-root":{padding:"4px",border:"none"}},children:[i.jsx(pe,{children:i.jsxs(xe,{children:[i.jsx(ge,{children:i.jsx("strong",{children:"Produit"})}),i.jsx(ge,{align:"center",children:i.jsx("strong",{children:"Qté"})}),i.jsx(ge,{align:"right",children:i.jsx("strong",{children:"Prix unit."})}),i.jsx(ge,{align:"right",children:i.jsx("strong",{children:"Sous-total"})})]})}),i.jsx(ye,{children:n.produits.map((e,t)=>i.jsxs(xe,{children:[i.jsx(ge,{sx:{fontSize:"11px"},children:e.nomProduit}),i.jsx(ge,{align:"center",sx:{fontSize:"11px"},children:e.quantite}),i.jsx(ge,{align:"right",sx:{fontSize:"11px"},children:Zr.formatCurrency(e.prixUnitaireCDF,"CDF")}),i.jsx(ge,{align:"right",sx:{fontSize:"11px"},children:Zr.formatCurrency(e.totalCDF,"CDF")})]},t))})]})}),i.jsx(h,{sx:{my:1}}),i.jsx(r,{mb:2,children:i.jsxs(o,{variant:"body2",children:[i.jsx("strong",{children:"Méthode de paiement:"})," ",Zr.getPaymentMethodLabel(n.methodePaiement)]})}),i.jsxs(r,{mb:2,children:[i.jsxs(r,{display:"flex",justifyContent:"space-between",children:[i.jsx(o,{variant:"body2",children:"Sous-total:"}),i.jsx(o,{variant:"body2",children:Zr.formatCurrency(n.totalCDF,"CDF")})]}),i.jsxs(r,{display:"flex",justifyContent:"space-between",children:[i.jsx(o,{variant:"body1",fontWeight:"bold",children:"Total CDF:"}),i.jsx(o,{variant:"body1",fontWeight:"bold",children:Zr.formatCurrency(n.totalCDF,"CDF")})]}),n.totalUSD&&i.jsxs(r,{display:"flex",justifyContent:"space-between",children:[i.jsx(o,{variant:"body2",children:"Total USD:"}),i.jsxs(o,{variant:"body2",children:["≈ ",Zr.formatCurrency(n.totalUSD,"USD")]})]})]}),i.jsx(h,{sx:{my:1}}),n.notes&&i.jsx(r,{mb:2,children:i.jsxs(o,{variant:"body2",children:[i.jsx("strong",{children:"Notes:"})," ",n.notes]})}),i.jsxs(r,{textAlign:"center",mt:2,children:[i.jsx(o,{variant:"body2",fontWeight:"bold",children:"Merci pour votre achat!"}),i.jsxs(o,{variant:"caption",sx:{fontSize:"10px"},children:["Reçu ID: ",s]})]})]})},ta=({receiptData:e,paperSize:t="thermal"})=>{const{expense:n,entreprise:a,numero:s,creePar:l}=e;return i.jsxs(c,{className:"receipt-container "+("thermal"===t?"thermal-receipt":"a4-receipt"),elevation:0,sx:{p:2,fontFamily:"monospace",fontSize:"thermal"===t?"12px":"14px",lineHeight:1.4,maxWidth:"thermal"===t?"80mm":"210mm",margin:"0 auto",backgroundColor:"white",color:"black","@media print":{backgroundColor:"white !important",color:"black !important",boxShadow:"none !important",border:"none !important"}},children:[i.jsxs(r,{textAlign:"center",mb:2,children:[a.logo&&i.jsx(r,{mb:1,display:"flex",justifyContent:"center",alignItems:"center",children:i.jsx(r,{component:"img",src:a.logo,alt:`${a.nom} Logo`,sx:{maxWidth:"thermal"===t?"70mm":"150px",maxHeight:"60px",objectFit:"contain",mb:1}})}),i.jsx(o,{variant:"h6",fontWeight:"bold",sx:{fontSize:"16px"},children:a.nom}),i.jsx(o,{variant:"body2",sx:{fontSize:"12px"},children:a.adresse}),i.jsxs(o,{variant:"body2",sx:{fontSize:"12px"},children:["Tél: ",a.telephone]}),a.rccm&&i.jsxs(o,{variant:"body2",sx:{fontSize:"11px"},children:["RCCM: ",a.rccm]}),a.idNat&&i.jsxs(o,{variant:"body2",sx:{fontSize:"11px"},children:["ID NAT: ",a.idNat]})]}),i.jsx(h,{sx:{my:1}}),i.jsxs(r,{textAlign:"center",mb:2,children:[i.jsxs(o,{variant:"h6",fontWeight:"bold",children:["REÇU DE DÉPENSE #",s]}),i.jsx(o,{variant:"body2",children:Zr.formatReceiptDate(n.dateDepense)})]}),i.jsx(r,{mb:1,children:i.jsxs(o,{variant:"body2",children:[i.jsx("strong",{children:"Créé par:"})," ",l]})}),i.jsx(h,{sx:{my:1}}),i.jsxs(r,{mb:2,children:[i.jsx(o,{variant:"body2",mb:1,children:i.jsx("strong",{children:"Description:"})}),i.jsx(o,{variant:"body2",sx:{pl:1,mb:2},children:n.description}),i.jsxs(o,{variant:"body2",children:[i.jsx("strong",{children:"Catégorie:"})," ",n.categorie]})]}),i.jsx(h,{sx:{my:1}}),i.jsxs(r,{mb:2,children:[i.jsxs(r,{display:"flex",justifyContent:"space-between",mb:1,children:[i.jsx(o,{variant:"body1",fontWeight:"bold",children:"Montant:"}),i.jsx(o,{variant:"body1",fontWeight:"bold",children:Zr.formatCurrency(n.montantCDF,"CDF")})]}),n.montantUSD&&i.jsxs(r,{display:"flex",justifyContent:"space-between",children:[i.jsx(o,{variant:"body2",children:"Équivalent USD:"}),i.jsxs(o,{variant:"body2",children:["≈ ",Zr.formatCurrency(n.montantUSD,"USD")]})]})]}),i.jsx(h,{sx:{my:1}}),n.notes&&i.jsxs(r,{mb:2,children:[i.jsx(o,{variant:"body2",mb:1,children:i.jsx("strong",{children:"Notes:"})}),i.jsx(o,{variant:"body2",sx:{pl:1},children:n.notes})]}),i.jsxs(r,{textAlign:"center",mt:2,children:[i.jsx(o,{variant:"body2",fontWeight:"bold",children:"Reçu de dépense validé"}),i.jsxs(o,{variant:"caption",sx:{fontSize:"10px"},children:["Reçu ID: ",s]})]})]})},na=({open:e,onClose:t,receiptData:n,paperSize:a="thermal",onPrintSuccess:s})=>{const[l,c]=vt.useState(!1);return n?i.jsxs(Re,{open:e,onClose:t,maxWidth:"md",fullWidth:!0,className:"receipt-preview-modal",PaperProps:{sx:{maxHeight:"90vh","@media print":{boxShadow:"none",margin:0,maxWidth:"none",maxHeight:"none"}}},children:[i.jsxs(Me,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center","@media print":{display:"none"}},children:[i.jsxs(r,{display:"flex",alignItems:"center",gap:1,children:[i.jsx(se,{}),i.jsx(o,{variant:"h6",children:"Aperçu du reçu"})]}),i.jsx(U,{onClick:t,size:"small",children:i.jsx(Oe,{})})]}),i.jsx(Ie,{sx:{p:0,"@media print":{padding:0,margin:0}},children:i.jsx(r,{sx:{p:2,"@media print":{padding:0,margin:0}},children:"sale"===n.type?i.jsx(ea,{receiptData:n,paperSize:a}):i.jsx(ta,{receiptData:n,paperSize:a})})}),i.jsxs(Ne,{sx:{p:2,gap:1,"@media print":{display:"none"}},children:[i.jsx(I,{onClick:t,variant:"outlined",disabled:l,children:"Fermer"}),i.jsx(I,{onClick:async()=>{if(n){c(!0);try{const e=document.createElement("div");e.className="receipt-print-container",e.style.cssText="\n        position: fixed;\n        top: -9999px;\n        left: -9999px;\n        width: 100%;\n        height: auto;\n        background: white;\n        z-index: 9999;\n      ";const n=document.querySelector(".receipt-container");if(n){const t=n.cloneNode(!0);t.style.cssText="\n          display: block !important;\n          position: static !important;\n          width: 100% !important;\n          height: auto !important;\n          margin: 0 !important;\n          padding: 5mm !important;\n          background: white !important;\n          color: black !important;\n        ",e.appendChild(t)}document.body.appendChild(e),await new Promise(e=>setTimeout(e,200));const i=document.querySelector(".receipt-preview-modal");i&&(i.style.display="none"),await Zr.printReceipt(),document.body.removeChild(e),i&&(i.style.display="block"),s&&s(),setTimeout(()=>{t()},500)}catch(ba){console.error("Error printing receipt:",ba)}finally{c(!1)}}},variant:"contained",startIcon:l?i.jsx(Ve,{size:16}):i.jsx(qe,{}),disabled:l,children:l?"Impression...":"Imprimer"})]})]}):null},ia=()=>{const[e,t]=vt.useState([]),[n,a]=vt.useState([]),[s,l]=vt.useState([]),[d,u]=vt.useState(""),[m,g]=vt.useState(0),[j,D]=vt.useState(10),[S,b]=vt.useState(!1),[w,E]=vt.useState(!1),[P,T]=vt.useState(null),[k,A]=vt.useState(""),[R,N]=vt.useState(""),[L,q]=vt.useState([]),[B,_]=vt.useState(null),[z,$]=vt.useState(1),[W,X]=vt.useState(""),[Q,H]=vt.useState(""),[G,Y]=vt.useState(""),[K,Z]=vt.useState("cash"),[re,ae]=vt.useState("cash"),[se,oe]=vt.useState("CDF"),[le,de]=vt.useState(""),[ue,ve]=vt.useState(""),[be,fe]=vt.useState(!1),[we,Fe]=vt.useState({tauxChangeUSDCDF:2800}),[Ue,Te]=vt.useState(!1),[ke,Le]=vt.useState(null),[Oe,qe]=vt.useState(!1),He=mr.getUserPermissions(),Ge=mr.getCurrentUser(),Ye=e=>{try{const t=new Date(e);return isNaN(t.getTime())?(console.warn("Invalid date found:",e),null):t}catch(t){return console.warn("Error parsing date:",e,t),null}};vt.useEffect(()=>{Ke()},[]),vt.useEffect(()=>{Ze()},[e,d]),vt.useEffect(()=>{-1===j&&D(s.length||1)},[s.length,j]);const Ke=async()=>{const e=await ur.getSales(),n=await ur.getProducts(),i=await ur.getSettings();t(e),a(n),Fe(i)},Ze=()=>{let t=e;d&&(t=t.filter(e=>e.nomClient.toLowerCase().includes(d.toLowerCase())||e.id.toLowerCase().includes(d.toLowerCase())||e.vendeur.toLowerCase().includes(d.toLowerCase()))),l(t)},et=()=>{b(!1),tt(),A(""),N("")},tt=()=>{q([]),_(null),$(1),X(""),H(""),Y(""),Z("cash"),ae("cash"),oe("CDF"),de(""),ve(""),fe(!1)},nt=e=>{const t=L.filter((t,n)=>n!==e);q(t)},it=()=>L.reduce((e,t)=>({CDF:Fr(e.CDF+t.totalCDF),USD:Fr(e.USD+(t.totalUSD||0))}),{CDF:0,USD:0}),rt=e=>"USD"===se?{price:e.prixUSD||e.prixCDF/((null==we?void 0:we.tauxChangeUSDCDF)||2800),currency:"USD",symbol:"$"}:{price:e.prixCDF,currency:"CDF",symbol:""},at=e=>{const t=rt(e);return`${t.symbol}${t.price.toLocaleString("fr-FR",{minimumFractionDigits:"USD"===t.currency?2:0,maximumFractionDigits:"USD"===t.currency?2:0})} ${t.currency}`},st=e=>{T(e),E(!0)},ot=e=>{switch(e){case"cash":default:return i.jsx(f,{});case"banque":return i.jsx(_e,{});case"mobile_money":return i.jsx(Je,{})}},lt=e=>{switch(e){case"cash":return"Cash";case"banque":return"Banque";case"mobile_money":return"Mobile Money";default:return e}},ct=e.filter(e=>{const t=Ye(e.datevente);if(!t)return!1;const n=new Date;return t.toDateString()===n.toDateString()}),dt=ct.reduce((e,t)=>e+t.totalCDF,0),ut=e.length,mt=e.reduce((e,t)=>e+t.totalCDF,0),ht=ut>0?mt/ut:0;return i.jsxs(r,{children:[i.jsxs(r,{display:"flex",justifyContent:"space-between",alignItems:"center",mb:3,children:[i.jsx(o,{variant:"h4",children:"Gestion des Ventes"}),He.canManageSales&&i.jsx(I,{variant:"contained",startIcon:i.jsx(Ee,{}),onClick:()=>{tt(),b(!0),A(""),N("")},children:"Nouvelle Vente"})]}),R&&i.jsx(O,{severity:"success",sx:{mb:2},onClose:()=>N(""),children:R}),i.jsxs(ce,{container:!0,spacing:3,sx:{mb:3},children:[i.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:i.jsx(ee,{children:i.jsx(te,{children:i.jsxs(r,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[i.jsxs(r,{children:[i.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Ventes du jour"}),i.jsx(o,{variant:"h6",children:ct.length}),i.jsxs(o,{variant:"body2",color:"primary",fontWeight:"medium",children:[Br(dt,we.tauxChangeUSDCDF).primaryAmount," ",Br(dt,we.tauxChangeUSDCDF).primaryCurrency]}),i.jsxs(o,{variant:"caption",color:"text.secondary",children:["≈ $",Br(dt,we.tauxChangeUSDCDF).secondaryAmount]})]}),i.jsx(C,{color:"primary",sx:{fontSize:40}})]})})})}),i.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:i.jsx(ee,{children:i.jsx(te,{children:i.jsxs(r,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[i.jsxs(r,{children:[i.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Total Ventes"}),i.jsx(o,{variant:"h6",children:ut}),i.jsxs(o,{variant:"body2",color:"success.main",fontWeight:"medium",children:[Br(mt,we.tauxChangeUSDCDF).primaryAmount," ",Br(mt,we.tauxChangeUSDCDF).primaryCurrency]}),i.jsxs(o,{variant:"caption",color:"text.secondary",children:["≈ $",Br(mt,we.tauxChangeUSDCDF).secondaryAmount]})]}),i.jsx(Be,{color:"success",sx:{fontSize:40}})]})})})}),i.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:i.jsx(ee,{children:i.jsx(te,{children:i.jsxs(r,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[i.jsxs(r,{children:[i.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Vente Moyenne"}),i.jsxs(o,{variant:"h6",fontWeight:"medium",children:[Br(ht,we.tauxChangeUSDCDF).primaryAmount," ",Br(ht,we.tauxChangeUSDCDF).primaryCurrency]}),i.jsxs(o,{variant:"caption",color:"text.secondary",children:["≈ $",Br(ht,we.tauxChangeUSDCDF).secondaryAmount]})]}),i.jsx(v,{color:"info",sx:{fontSize:40}})]})})})}),i.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:i.jsx(ee,{children:i.jsx(te,{children:i.jsxs(r,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[i.jsxs(r,{children:[i.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Ventes à Crédit"}),i.jsx(o,{variant:"h6",children:e.filter(e=>"credit"===e.typeVente).length})]}),i.jsx(_e,{color:"warning",sx:{fontSize:40}})]})})})})]}),i.jsx(c,{sx:{p:2,mb:3},children:i.jsx(ne,{fullWidth:!0,placeholder:"Rechercher par client, ID vente ou vendeur...",value:d,onChange:e=>u(e.target.value),InputProps:{startAdornment:i.jsx(ie,{position:"start",children:i.jsx(Pe,{})})}})}),i.jsxs(me,{component:c,children:[i.jsxs(he,{children:[i.jsx(pe,{children:i.jsxs(xe,{children:[i.jsx(ge,{children:"Produits Vendus"}),i.jsx(ge,{children:"Client"}),i.jsx(ge,{children:"Vendeur"}),i.jsx(ge,{align:"right",children:"Total CDF"}),i.jsx(ge,{align:"right",children:"Total USD"}),i.jsx(ge,{align:"center",children:"Paiement"}),i.jsx(ge,{align:"center",children:"Type"}),i.jsx(ge,{children:"Date"}),i.jsx(ge,{align:"center",children:"Actions"})]})}),i.jsx(ye,{children:(-1===j?s:s.slice(m*j,m*j+j)).map(e=>i.jsxs(xe,{hover:!0,onClick:()=>st(e),sx:{cursor:"pointer"},children:[i.jsx(ge,{children:i.jsxs(r,{children:[i.jsx(o,{variant:"subtitle2",color:"primary",gutterBottom:!0,children:e.produits.map(e=>e.nomProduit).join(", ")}),i.jsxs(o,{variant:"caption",color:"text.secondary",display:"block",children:["ID: ",e.id," • ",e.produits.length," article",e.produits.length>1?"s":""]})]})}),i.jsx(ge,{children:i.jsxs(r,{children:[i.jsx(o,{variant:"subtitle2",children:e.nomClient}),e.telephoneClient&&i.jsx(o,{variant:"caption",color:"text.secondary",children:e.telephoneClient})]})}),i.jsx(ge,{children:e.vendeur}),i.jsx(ge,{align:"right",children:qr(e.totalCDF,"CDF")}),i.jsx(ge,{align:"right",children:e.totalUSD?qr(e.totalUSD,"USD"):"-"}),i.jsx(ge,{align:"center",children:i.jsx(M,{icon:ot(e.methodePaiement),label:lt(e.methodePaiement),size:"small"})}),i.jsx(ge,{align:"center",children:i.jsx(M,{label:"cash"===e.typeVente?"Cash":"Crédit",color:"cash"===e.typeVente?"success":"warning",size:"small"})}),i.jsx(ge,{children:(()=>{const t=Ye(e.datevente);return t?Ft(t,"dd/MM/yyyy HH:mm",{locale:jr}):"Date invalide"})()}),i.jsx(ge,{align:"center",children:i.jsx(F,{title:"Voir détails",children:i.jsx(U,{size:"small",onClick:()=>st(e),children:i.jsx(f,{fontSize:"small"})})})})]},e.id))})]}),i.jsx(Ae,{rowsPerPageOptions:[5,10,25,50,100,{label:"Voir tout",value:-1}],component:"div",count:s.length,rowsPerPage:-1===j?s.length:j,page:-1===j?0:m,onPageChange:(e,t)=>{-1!==j&&g(t)},onRowsPerPageChange:e=>{const t=parseInt(e.target.value,10);D(t),g(0)},labelRowsPerPage:"Lignes par page:",labelDisplayedRows:({from:e,to:t,count:n})=>-1===j?`Affichage de tous les ${n} éléments`:`${e}-${t} sur ${-1!==n?n:`plus de ${t}`}`})]}),i.jsxs(Re,{open:S,onClose:et,maxWidth:"lg",fullWidth:!0,children:[i.jsx(Me,{children:"Nouvelle Vente"}),i.jsxs(Ie,{children:[k&&i.jsx(O,{severity:"error",sx:{mb:2},children:k}),R&&i.jsx(O,{severity:"success",sx:{mb:2},children:R}),i.jsxs(r,{sx:{mb:3,p:2,bgcolor:"primary.50",borderRadius:1,border:"1px solid",borderColor:"primary.200"},children:[i.jsx(o,{variant:"subtitle2",gutterBottom:!0,sx:{fontWeight:"bold",color:"primary.main"},children:"💱 Devise de la Vente"}),i.jsxs(je,{fullWidth:!0,size:"small",children:[i.jsx(De,{children:"Sélectionnez la devise pour cette vente"}),i.jsxs(Ce,{value:se,label:"Sélectionnez la devise pour cette vente",onChange:e=>oe(e.target.value),children:[i.jsx(J,{value:"CDF",children:i.jsxs(r,{display:"flex",alignItems:"center",gap:1,children:[i.jsx(o,{children:"🇨🇩"}),i.jsx(o,{children:"CDF (Franc Congolais) - Devise principale"})]})}),i.jsx(J,{value:"USD",children:i.jsxs(r,{display:"flex",alignItems:"center",gap:1,children:[i.jsx(o,{children:"🇺🇸"}),i.jsx(o,{children:"USD (Dollar Américain)"})]})})]})]}),i.jsx(o,{variant:"caption",color:"text.secondary",sx:{mt:1,display:"block"},children:"Les prix des produits seront affichés dans la devise sélectionnée"})]}),i.jsxs(ce,{container:!0,spacing:3,children:[i.jsxs(ce,{item:!0,xs:12,children:[i.jsx(o,{variant:"h6",gutterBottom:!0,children:"Sélection des Produits"}),i.jsxs(ce,{container:!0,spacing:2,alignItems:"center",children:[i.jsx(ce,{item:!0,xs:12,md:6,children:i.jsx(ze,{options:n.filter(e=>e.stock>0),getOptionLabel:e=>`${e.nom} - ${at(e)} - Stock: ${e.stock}`,value:B,onChange:(e,t)=>_(t),renderInput:e=>i.jsx(ne,{...e,label:"Produit",fullWidth:!0}),renderOption:(e,t)=>i.jsx(r,{component:"li",...e,children:i.jsxs(r,{sx:{display:"flex",flexDirection:"column",width:"100%"},children:[i.jsx(o,{variant:"body1",sx:{fontWeight:500},children:t.nom}),i.jsxs(o,{variant:"body2",color:"text.secondary",children:["Prix: ",at(t)," • Stock: ",t.stock," • Code: ",t.codeQR]})]})})})}),i.jsx(ce,{item:!0,xs:12,md:3,children:i.jsxs(r,{children:[i.jsx(Yr,{value:z,onChange:$,min:1,max:(null==B?void 0:B.stock)||999,size:"small",showButtons:!0,allowDirectInput:!0,label:"Quantité"}),B&&i.jsxs(o,{variant:"caption",color:"text.secondary",display:"block",sx:{mt:1},children:["Stock disponible: ",B.stock]})]})}),i.jsx(ce,{item:!0,xs:12,md:3,children:i.jsx(I,{fullWidth:!0,variant:"contained",onClick:()=>{if(!B)return void A("Veuillez sélectionner un produit");if(z<=0)return void A("La quantité doit être supérieure à 0");if(z>B.stock)return void A("Quantité insuffisante en stock");const e=L.findIndex(e=>e.produitId===B.id);if(e>=0){const t=[...L],n=t[e].quantite+z;if(n>B.stock)return void A("Quantité totale dépasse le stock disponible");t[e]={...t[e],quantite:n,totalCDF:n*B.prixCDF,totalUSD:B.prixUSD?n*B.prixUSD:void 0},q(t)}else{const e={produitId:B.id,nomProduit:B.nom,quantite:z,prixUnitaireCDF:B.prixCDF,prixUnitaireUSD:B.prixUSD,totalCDF:z*B.prixCDF,totalUSD:B.prixUSD?z*B.prixUSD:void 0};q([...L,e])}_(null),$(1),A("")},disabled:!B,children:"Ajouter"})})]}),B&&i.jsx(r,{sx:{mt:2,p:2,bgcolor:"grey.50",borderRadius:1,border:"1px solid",borderColor:"grey.300"},children:i.jsxs(ce,{container:!0,spacing:2,alignItems:"center",children:[i.jsxs(ce,{item:!0,xs:12,md:6,children:[i.jsxs(o,{variant:"subtitle2",sx:{fontWeight:"bold"},children:["Produit sélectionné: ",B.nom]}),i.jsxs(o,{variant:"body2",color:"text.secondary",children:["Code: ",B.codeQR," • Catégorie: ",B.categorie]})]}),i.jsxs(ce,{item:!0,xs:12,md:3,children:[i.jsxs(o,{variant:"subtitle2",color:"primary",sx:{fontWeight:"bold"},children:["Prix unitaire: ",at(B)]}),i.jsxs(o,{variant:"caption",color:"text.secondary",children:["Devise: ",se]})]}),i.jsxs(ce,{item:!0,xs:12,md:3,children:[i.jsxs(o,{variant:"subtitle2",sx:{fontWeight:"bold"},children:["Total: ",(()=>{const e=rt(B),t=e.price*z;return`${e.symbol}${t.toLocaleString("fr-FR",{minimumFractionDigits:"USD"===e.currency?2:0,maximumFractionDigits:"USD"===e.currency?2:0})} ${e.currency}`})()]}),i.jsxs(o,{variant:"caption",color:"text.secondary",children:[z," × ",at(B)]})]})]})})]}),i.jsxs(ce,{item:!0,xs:12,children:[i.jsxs(o,{variant:"h6",gutterBottom:!0,children:["Panier (",L.length," articles) - Devise: ",se]}),0===L.length?i.jsx(O,{severity:"info",children:"Aucun produit ajouté"}):i.jsxs(p,{children:[L.map((e,t)=>{var a;const s=(e=>{if("USD"===se){const t=e.prixUnitaireUSD||e.prixUnitaireCDF/((null==we?void 0:we.tauxChangeUSDCDF)||2800),n=e.totalUSD||e.totalCDF/((null==we?void 0:we.tauxChangeUSDCDF)||2800);return{unitPrice:`$${t.toLocaleString("fr-FR",{minimumFractionDigits:2,maximumFractionDigits:2})} USD`,total:`$${n.toLocaleString("fr-FR",{minimumFractionDigits:2,maximumFractionDigits:2})} USD`}}return{unitPrice:`${e.prixUnitaireCDF.toLocaleString("fr-FR")} CDF`,total:`${e.totalCDF.toLocaleString("fr-FR")} CDF`}})(e);return i.jsxs(bt.Fragment,{children:[i.jsxs(x,{children:[i.jsx(y,{primary:i.jsx(o,{variant:"subtitle1",sx:{fontWeight:500},children:e.nomProduit}),secondary:`Prix unitaire: ${s.unitPrice}`}),i.jsx(V,{children:i.jsxs(r,{display:"flex",alignItems:"center",gap:1,children:[i.jsx(Yr,{value:e.quantite,onChange:e=>((e,t)=>{if(t<=0)return void nt(e);const i=L[e],r=n.find(e=>e.id===i.produitId);if(r&&t>r.stock)return void A(`Quantité maximale disponible: ${r.stock}`);const a=[...L];a[e]={...i,quantite:t,totalCDF:t*i.prixUnitaireCDF,totalUSD:i.prixUnitaireUSD?t*i.prixUnitaireUSD:void 0},q(a),A("")})(t,e),min:1,max:(null==(a=n.find(t=>t.id===e.produitId))?void 0:a.stock)||999,size:"small",showButtons:!0,allowDirectInput:!0}),i.jsx(o,{variant:"subtitle1",sx:{minWidth:"120px",textAlign:"right",fontWeight:"bold",color:"primary.main"},children:s.total}),i.jsx(U,{size:"small",color:"error",onClick:()=>nt(t),title:"Supprimer l'article",children:i.jsx(Be,{fontSize:"small"})})]})})]}),t<L.length-1&&i.jsx(h,{})]},t)}),i.jsx(h,{sx:{my:2}}),i.jsx(x,{sx:{bgcolor:"primary.50",borderRadius:1},children:i.jsx(y,{primary:i.jsxs(o,{variant:"h6",sx:{fontWeight:"bold",color:"primary.main"},children:["Total: ",(()=>{const e=it();return"USD"===se?`$${e.USD.toLocaleString("fr-FR",{minimumFractionDigits:2,maximumFractionDigits:2})} USD`:`${e.CDF.toLocaleString("fr-FR")} CDF`})()]}),secondary:i.jsx(o,{variant:"body2",color:"text.secondary",children:"USD"===se?`≈ ${qr(it().CDF,"CDF")} (taux: ${(null==we?void 0:we.tauxChangeUSDCDF)||2800})`:`≈ ${qr(it().USD,"USD")} (taux: ${(null==we?void 0:we.tauxChangeUSDCDF)||2800})`})})})]})]}),i.jsxs(ce,{item:!0,xs:12,children:[i.jsx(o,{variant:"h6",gutterBottom:!0,children:"Informations Client"}),i.jsxs(ce,{container:!0,spacing:2,children:[i.jsx(ce,{item:!0,xs:12,md:4,children:i.jsx(ne,{fullWidth:!0,label:"credit"===re?"Nom du client *":"Nom du client",placeholder:"credit"===re?"Nom requis pour crédit":"Client (par défaut)",value:W,onChange:e=>X(e.target.value),required:"credit"===re,error:"credit"===re&&!W.trim()&&k.includes("nom du client"),helperText:"credit"===re&&!W.trim()&&k.includes("nom du client")?"Le nom du client est obligatoire pour les ventes à crédit":""})}),i.jsx(ce,{item:!0,xs:12,md:4,children:i.jsx(ne,{fullWidth:!0,label:"Téléphone",value:Q,onChange:e=>H(e.target.value)})}),i.jsx(ce,{item:!0,xs:12,md:4,children:i.jsx(ne,{fullWidth:!0,label:"Adresse",value:G,onChange:e=>Y(e.target.value)})})]})]}),i.jsxs(ce,{item:!0,xs:12,children:[i.jsx(o,{variant:"h6",gutterBottom:!0,children:"Informations de Paiement"}),i.jsxs(ce,{container:!0,spacing:2,children:[i.jsx(ce,{item:!0,xs:12,md:6,children:i.jsxs(je,{component:"fieldset",children:[i.jsx(Se,{component:"legend",children:"Type de vente"}),i.jsxs($e,{row:!0,value:re,onChange:e=>ae(e.target.value),children:[i.jsx(We,{value:"cash",control:i.jsx(Xe,{}),label:"Cash"}),i.jsx(We,{value:"credit",control:i.jsx(Xe,{}),label:"Crédit"})]})]})}),i.jsx(ce,{item:!0,xs:12,md:6,children:i.jsxs(je,{fullWidth:!0,children:[i.jsx(De,{children:"Méthode de paiement"}),i.jsxs(Ce,{value:K,label:"Méthode de paiement",onChange:e=>Z(e.target.value),children:[i.jsx(J,{value:"cash",children:"Cash"}),i.jsx(J,{value:"banque",children:"Banque"}),i.jsx(J,{value:"mobile_money",children:"Mobile Money"})]})]})}),"credit"===re&&i.jsx(ce,{item:!0,xs:12,md:6,children:i.jsx(ne,{fullWidth:!0,label:"Date d'échéance",type:"date",value:le,onChange:e=>de(e.target.value),InputLabelProps:{shrink:!0}})}),i.jsx(ce,{item:!0,xs:12,children:i.jsx(ne,{fullWidth:!0,label:"Notes",multiline:!0,rows:2,value:ue,onChange:e=>ve(e.target.value)})})]})]})]})]}),i.jsxs(Ne,{children:[i.jsx(We,{control:i.jsx(Qe,{checked:be,onChange:e=>fe(e.target.checked)}),label:"Imprimer reçu",sx:{mr:"auto"}}),Oe&&i.jsxs(r,{display:"flex",alignItems:"center",gap:1,mr:2,children:[i.jsx(Ve,{size:20}),i.jsx(o,{variant:"body2",children:"Génération du reçu..."})]}),i.jsx(I,{onClick:et,children:"Annuler"}),i.jsx(I,{onClick:async()=>{var i;if(0===L.length)return void A("Veuillez ajouter au moins un produit");if("credit"===re&&!W.trim())return void A("Le nom du client est obligatoire pour les ventes à crédit");if("credit"===re&&!le)return void A("La date d'échéance est requise pour les ventes à crédit");const r=it(),s=(new Date).toISOString(),o=be||(null==(i=null==we?void 0:we.impression)?void 0:i.impressionAutomatique)||!1,l=o?await Zr.generateSalesReceiptNumber():void 0,c={id:`VTE-${Date.now()}`,produits:L,nomClient:W.trim()||"Client",telephoneClient:Q.trim()||void 0,adresseClient:G.trim()||void 0,totalCDF:r.CDF,totalUSD:r.USD,methodePaiement:K,typeVente:re,datevente:s,vendeur:(null==Ge?void 0:Ge.nom)||"Inconnu",notes:ue.trim()||void 0,numeroRecu:l},d=n.map(e=>{const t=L.find(t=>t.produitId===e.id);return t?{...e,stock:e.stock-t.quantite,dateModification:s}:e}),u=[...e,c];if(t(u),a(d),await ur.setSales(u),await ur.setProducts(d),"credit"===re){const e={id:`DET-${Date.now()}`,venteId:c.id,nomClient:W.trim()||"Client",telephoneClient:Q.trim()||void 0,adresseClient:G.trim()||void 0,montantTotalCDF:r.CDF,montantTotalUSD:r.USD,montantPayeCDF:0,montantPayeUSD:0,montantRestantCDF:r.CDF,montantRestantUSD:r.USD,dateCreation:s,dateEcheance:le,statut:"active",statutPaiement:"impaye",paiements:[],notes:ue.trim()||void 0,deviseVente:se},t=await ur.getDebts();await ur.setDebts([...t,e])}if(N("Vente enregistrée avec succès"),o)try{qe(!0);const e=await Zr.createSalesReceiptData(c);Le(e),Te(!0)}catch(m){console.error("Erreur lors de la génération du reçu:",m),A("Erreur lors de la génération du reçu")}finally{qe(!1)}setTimeout(()=>{et()},1500)},variant:"contained",disabled:0===L.length||Oe,children:"Enregistrer la Vente"})]})]}),i.jsxs(Re,{open:w,onClose:()=>E(!1),maxWidth:"md",fullWidth:!0,children:[i.jsx(Me,{children:"Détails de la Vente"}),i.jsx(Ie,{children:P&&i.jsxs(ce,{container:!0,spacing:2,children:[i.jsxs(ce,{item:!0,xs:12,children:[i.jsx(o,{variant:"subtitle2",sx:{fontSize:"1.1rem",fontWeight:"bold"},children:"Produits Vendus:"}),i.jsx(o,{variant:"h6",color:"primary",sx:{mt:.5,mb:1},children:P.produits.map(e=>e.nomProduit).join(", ")})]}),i.jsxs(ce,{item:!0,xs:12,md:6,children:[i.jsx(o,{variant:"subtitle2",children:"Référence Vente:"}),i.jsx(o,{variant:"body2",color:"text.secondary",children:P.id})]}),i.jsxs(ce,{item:!0,xs:12,md:6,children:[i.jsx(o,{variant:"subtitle2",children:"Date:"}),i.jsx(o,{variant:"body1",children:Ft(new Date(P.datevente),"dd/MM/yyyy HH:mm",{locale:jr})})]}),i.jsxs(ce,{item:!0,xs:12,md:6,children:[i.jsx(o,{variant:"subtitle2",children:"Client:"}),i.jsx(o,{variant:"body1",children:P.nomClient}),P.telephoneClient&&i.jsx(o,{variant:"body2",color:"text.secondary",children:P.telephoneClient})]}),i.jsxs(ce,{item:!0,xs:12,md:6,children:[i.jsx(o,{variant:"subtitle2",children:"Vendeur:"}),i.jsx(o,{variant:"body1",children:P.vendeur})]}),i.jsxs(ce,{item:!0,xs:12,children:[i.jsx(o,{variant:"subtitle2",gutterBottom:!0,children:"Produits:"}),i.jsxs(p,{dense:!0,children:[P.produits.map((e,t)=>i.jsxs(x,{children:[i.jsx(y,{primary:e.nomProduit,secondary:i.jsxs(r,{children:[i.jsxs(o,{variant:"body2",component:"span",children:[e.quantite," × ",qr(e.prixUnitaireCDF,"CDF")]}),i.jsxs(o,{variant:"caption",display:"block",color:"text.secondary",children:["≈ ",e.quantite," × ",qr(e.prixUnitaireCDF/((null==we?void 0:we.tauxChangeUSDCDF)||2800),"USD")]})]})}),i.jsxs(r,{textAlign:"right",children:[i.jsx(o,{variant:"body2",fontWeight:"medium",children:qr(e.totalCDF,"CDF")}),i.jsxs(o,{variant:"caption",color:"text.secondary",children:["≈ ",qr(e.totalCDF/((null==we?void 0:we.tauxChangeUSDCDF)||2800),"USD")]})]})]},t)),i.jsx(h,{}),i.jsx(x,{children:i.jsx(y,{primary:i.jsxs(r,{children:[i.jsxs(o,{variant:"h6",color:"primary",fontWeight:"medium",children:["Total: ",Br(P.totalCDF,(null==we?void 0:we.tauxChangeUSDCDF)||2800).primaryAmount," ",Br(P.totalCDF,(null==we?void 0:we.tauxChangeUSDCDF)||2800).primaryCurrency]}),i.jsxs(o,{variant:"body2",color:"text.secondary",children:["≈ $",Br(P.totalCDF,(null==we?void 0:we.tauxChangeUSDCDF)||2800).secondaryAmount]}),i.jsxs(o,{variant:"caption",color:"text.secondary",children:["Taux de change: 1 USD = ",(null==we?void 0:we.tauxChangeUSDCDF)||2800," CDF"]})]})})})]})]}),i.jsxs(ce,{item:!0,xs:12,md:6,children:[i.jsx(o,{variant:"subtitle2",children:"Méthode de paiement:"}),i.jsx(M,{icon:ot(P.methodePaiement),label:lt(P.methodePaiement)})]}),i.jsxs(ce,{item:!0,xs:12,md:6,children:[i.jsx(o,{variant:"subtitle2",children:"Type de vente:"}),i.jsx(M,{label:"cash"===P.typeVente?"Cash":"Crédit",color:"cash"===P.typeVente?"success":"warning"})]}),P.notes&&i.jsxs(ce,{item:!0,xs:12,children:[i.jsx(o,{variant:"subtitle2",children:"Notes:"}),i.jsx(o,{variant:"body1",children:P.notes})]})]})}),i.jsx(Ne,{children:i.jsx(I,{onClick:()=>E(!1),children:"Fermer"})})]}),i.jsx(na,{open:Ue,onClose:()=>Te(!1),receiptData:ke,onPrintSuccess:()=>{N("Reçu imprimé avec succès"),setTimeout(()=>N(""),3e3)}})]})},ra=()=>{const[e,t]=vt.useState([]),[n,a]=vt.useState([]),[s,l]=vt.useState(""),[d,u]=vt.useState("all"),[m,g]=vt.useState("all"),[j,D]=vt.useState(0),[C,S]=vt.useState(10),[b,w]=vt.useState(!1),[E,P]=vt.useState(!1),[T,k]=vt.useState(null),[A,R]=vt.useState(0),[N,L]=vt.useState("cash"),[V,q]=vt.useState(""),[B,z]=vt.useState(""),[W,X]=vt.useState(""),[Q,H]=vt.useState({tauxChangeUSDCDF:2800}),[Y,K]=vt.useState([]),[Z,re]=vt.useState(!1),[ae,oe]=vt.useState("CDF"),de=mr.getUserPermissions(),ue=e=>Y.find(t=>t.id===e.venteId),ve=e=>{const t=ue(e);return t&&t.produits&&0!==t.produits.length?t.produits.map(e=>`${e.nomProduit} (x${e.quantite})`).join(", "):"Produits non disponibles"},Se=e=>{if(e.nomClient&&""!==e.nomClient.trim()&&"Client"!==e.nomClient)return e.nomClient;const t=ue(e);return t&&t.nomClient&&""!==t.nomClient.trim()&&"Client"!==t.nomClient?t.nomClient:"Client"},be=e=>{try{const t=new Date(e);return isNaN(t.getTime())?(console.warn("Invalid date found:",e),null):t}catch(t){return console.warn("Error parsing date:",e,t),null}};vt.useEffect(()=>{fe();(async()=>{0===(await ur.getDebts()).length&&(console.log("No debts found, forcing initialization..."),await ur.forceInitializeDebts(),setTimeout(()=>fe(),500))})()},[]),vt.useEffect(()=>{we()},[e,s,d,m]),vt.useEffect(()=>{-1===C&&S(n.length||1)},[n.length,C]);const fe=async()=>{try{const e=await ur.getDebts(),n=await ur.getSettings(),i=await ur.getSales(),r=e.filter(e=>e.id&&e.venteId?(e.nomClient&&""!==e.nomClient.trim()||(e.nomClient="Client"),e.montantTotalCDF=void 0!==e.montantTotalCDF&&null!==e.montantTotalCDF&&""!==e.montantTotalCDF?Number(e.montantTotalCDF):0,e.montantPayeCDF=void 0!==e.montantPayeCDF&&null!==e.montantPayeCDF&&""!==e.montantPayeCDF?Number(e.montantPayeCDF):0,e.montantRestantCDF=void 0!==e.montantRestantCDF&&null!==e.montantRestantCDF&&""!==e.montantRestantCDF?Number(e.montantRestantCDF):e.montantTotalCDF-e.montantPayeCDF,!0):(console.warn("Invalid debt record missing required fields:",e),!1)).map(e=>{try{const t=Xe(e);if("paid"===t.statut)return t;const n=new Date,i=be(t.dateEcheance);return t.montantRestantCDF<=0?{...t,statut:"paid",statutPaiement:"paye"}:i&&At(n,i)?{...t,statut:"overdue"}:{...t,statut:"active"}}catch(t){return console.warn("Error processing debt status:",e,t),e}});t(r),H(n),K(i),await ur.setDebts(r)}catch(e){console.error("Error loading debt data:",e),z("Erreur lors du chargement des données de dette.")}},we=()=>{try{let t=e;if(s&&s.trim()){const e=s.toLowerCase().trim();t=t.filter(t=>{var n,i,r,a;try{const s=(null==(n=t.nomClient)?void 0:n.toLowerCase())||"",o=(null==(i=t.id)?void 0:i.toLowerCase())||"",l=(null==(r=t.venteId)?void 0:r.toLowerCase())||"",c=(null==(a=t.telephoneClient)?void 0:a.toLowerCase())||"",d=ve(t).toLowerCase();return s.includes(e)||o.includes(e)||l.includes(e)||c.includes(e)||d.includes(e)}catch(s){return console.warn("Error filtering debt:",t,s),!1}})}"all"!==d&&(t=t.filter(e=>e.statut===d)),"all"!==m&&(t=t.filter(e=>e.statutPaiement===m)),a(t)}catch(t){console.error("Error in filterDebts:",t),a(e),z("Erreur lors de la recherche. Affichage de toutes les dettes.")}},Ee=e=>{switch(e){case"active":return"primary";case"overdue":return"error";case"paid":return"success";default:return"default"}},Ue=e=>{switch(e){case"active":return"Active";case"overdue":return"En retard";case"paid":return"Payée";default:return e}},Te=e=>{switch(e){case"active":return i.jsx(He,{});case"overdue":return i.jsx($,{});case"paid":return i.jsx(_,{});default:return i.jsx(v,{})}},ke=e=>{switch(e){case"paye":return"Payé";case"impaye":return"Impayé";default:return e}},Le=e=>{switch(e){case"paye":return i.jsx(Ge,{});case"impaye":return i.jsx(Ze,{});default:return i.jsx(v,{})}},Oe=()=>{w(!1),k(null),R(0),L("cash"),q(""),oe("CDF"),z(""),X("")},Ve=e=>{k(e),P(!0)},qe=()=>{P(!1),k(null),re(!1),R(0),q(""),oe("CDF"),z(""),X("")},Be=async()=>{if(!T)return;if(A<=0)return void z("Le montant doit être supérieur à 0");if(A>T.montantRestantCDF)return void z("Le montant ne peut pas dépasser le montant restant");const n=(new Date).toISOString();let i,r;"USD"===ae?(r=A/Q.tauxChangeUSDCDF,i=A):(i=A,r=A/Q.tauxChangeUSDCDF);const a={id:`PAY-${Date.now()}`,montantCDF:i,montantUSD:r,methodePaiement:N,datePaiement:n,notes:V.trim()||void 0,deviseOriginale:ae},s={...T,montantPayeCDF:T.montantPayeCDF+A,montantPayeUSD:(T.montantPayeUSD||0)+A/Q.tauxChangeUSDCDF,paiements:[...T.paiements,a]},o=Xe(s),l={...o,statut:o.montantRestantCDF<=0?"paid":T.statut,statutPaiement:et(o)},c=e.map(e=>e.id===T.id?l:e);t(c),await ur.setDebts(c),k(l),X("Paiement enregistré avec succès"),Z?setTimeout(()=>{re(!1),R(0),q(""),oe("CDF"),z(""),X("")},2e3):setTimeout(()=>{Oe()},1500)},ze=e=>{switch(e){case"cash":default:return i.jsx(f,{});case"banque":return i.jsx(_e,{});case"mobile_money":return i.jsx(Je,{})}},$e=e=>{switch(e){case"cash":return"Cash";case"banque":return"Banque";case"mobile_money":return"Mobile Money";default:return e}},Xe=e=>{const t=e.paiements.reduce((e,t)=>e+t.montantCDF,0),n=e.paiements.reduce((e,t)=>e+(t.montantUSD||0),0),i=e.paiements.length>0?t:e.montantPayeCDF,r=e.paiements.length>0?n:e.montantPayeUSD||0,a=e.montantTotalCDF-i,s=e.montantTotalUSD?e.montantTotalUSD-r:void 0,o=e.montantTotalUSD||e.montantTotalCDF/Q.tauxChangeUSDCDF,l=r||i/Q.tauxChangeUSDCDF,c=void 0!==s?Math.max(0,s):Math.max(0,a/Q.tauxChangeUSDCDF);return{...e,montantTotalCDF:e.montantTotalCDF,montantTotalUSD:o,montantPayeCDF:i,montantPayeUSD:l,montantRestantCDF:Math.max(0,a),montantRestantUSD:c}},Qe=e=>0===e.montantTotalCDF?100:Math.min(100,e.montantPayeCDF/e.montantTotalCDF*100),et=e=>e.montantPayeCDF>=e.montantTotalCDF?"paye":"impaye",tt=e=>"paye"===et(e)?"Payé":"Impayé",nt=e=>"paye"===et(e)?"success":"error",it=e=>"paye"===et(e)?i.jsx(_,{fontSize:"small"}):i.jsx(Ke,{fontSize:"small"}),rt=e.filter(e=>"active"===e.statut),at=e.filter(e=>"overdue"===e.statut),st=e.filter(e=>"paye"===e.statutPaiement),ot=e.filter(e=>"impaye"===e.statutPaiement),lt=e.reduce((e,t)=>e+t.montantTotalCDF,0),ct=e.reduce((e,t)=>e+t.montantRestantCDF,0);return i.jsxs(r,{children:[i.jsxs(r,{display:"flex",justifyContent:"space-between",alignItems:"center",mb:3,children:[i.jsx(o,{variant:"h4",children:"Gestion des Dettes"}),i.jsx(I,{variant:"contained",startIcon:i.jsx(Fe,{}),onClick:()=>{const e=`SmartBoutique_Dettes_${(new Date).toISOString().split("T")[0]}.csv`;Li.downloadCSV(n,Bi,e),X("Dettes exportées en CSV avec succès (compatible Excel)"),setTimeout(()=>X(""),3e3)},children:"Exporter les Dettes"})]}),W&&i.jsx(O,{severity:"success",sx:{mb:2},onClose:()=>X(""),children:W}),i.jsxs(ce,{container:!0,spacing:3,sx:{mb:3},children:[i.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:i.jsx(ee,{children:i.jsx(te,{children:i.jsxs(r,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[i.jsxs(r,{children:[i.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Dettes Actives"}),i.jsx(o,{variant:"h6",children:rt.length}),i.jsxs(o,{variant:"body2",color:"primary",fontWeight:"medium",children:[Br(rt.reduce((e,t)=>e+t.montantRestantCDF,0),Q.tauxChangeUSDCDF).primaryAmount," ",Br(rt.reduce((e,t)=>e+t.montantRestantCDF,0),Q.tauxChangeUSDCDF).primaryCurrency]}),i.jsxs(o,{variant:"caption",color:"text.secondary",children:["≈ $",Br(rt.reduce((e,t)=>e+t.montantRestantCDF,0),Q.tauxChangeUSDCDF).secondaryAmount]})]}),i.jsx(He,{color:"primary",sx:{fontSize:40}})]})})})}),i.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:i.jsx(ee,{children:i.jsx(te,{children:i.jsxs(r,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[i.jsxs(r,{children:[i.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"En Retard"}),i.jsx(o,{variant:"h6",color:"error.main",children:at.length}),i.jsxs(o,{variant:"body2",color:"error",fontWeight:"medium",children:[Br(at.reduce((e,t)=>e+t.montantRestantCDF,0),Q.tauxChangeUSDCDF).primaryAmount," ",Br(at.reduce((e,t)=>e+t.montantRestantCDF,0),Q.tauxChangeUSDCDF).primaryCurrency]}),i.jsxs(o,{variant:"caption",color:"text.secondary",children:["≈ $",Br(at.reduce((e,t)=>e+t.montantRestantCDF,0),Q.tauxChangeUSDCDF).secondaryAmount]})]}),i.jsx($,{color:"error",sx:{fontSize:40}})]})})})}),i.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:i.jsx(ee,{children:i.jsx(te,{children:i.jsxs(r,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[i.jsxs(r,{children:[i.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Statut Payé"}),i.jsx(o,{variant:"h6",color:"success.main",children:st.length}),i.jsxs(o,{variant:"body2",color:"error",children:["Impayé: ",ot.length]})]}),i.jsx(Ge,{color:"success",sx:{fontSize:40}})]})})})}),i.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:i.jsx(ee,{children:i.jsx(te,{children:i.jsxs(r,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[i.jsxs(r,{children:[i.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Total Restant"}),i.jsxs(o,{variant:"h6",fontWeight:"medium",children:[Br(ct,Q.tauxChangeUSDCDF).primaryAmount," ",Br(ct,Q.tauxChangeUSDCDF).primaryCurrency]}),i.jsxs(o,{variant:"body2",color:"text.secondary",children:["≈ $",Br(ct,Q.tauxChangeUSDCDF).secondaryAmount," sur ",Br(lt,Q.tauxChangeUSDCDF).primaryAmount," ",Br(lt,Q.tauxChangeUSDCDF).primaryCurrency]})]}),i.jsx(v,{color:"warning",sx:{fontSize:40}})]})})})})]}),i.jsx(c,{sx:{p:2,mb:3},children:i.jsxs(ce,{container:!0,spacing:2,alignItems:"center",children:[i.jsx(ce,{item:!0,xs:12,md:4,children:i.jsx(ne,{fullWidth:!0,placeholder:"Rechercher par client, produit, ID dette ou ID vente...",value:s,onChange:e=>l(e.target.value),InputProps:{startAdornment:i.jsx(ie,{position:"start",children:i.jsx(Pe,{})})}})}),i.jsx(ce,{item:!0,xs:12,md:3,children:i.jsxs(je,{fullWidth:!0,children:[i.jsx(De,{children:"Statut"}),i.jsxs(Ce,{value:d,label:"Statut",onChange:e=>u(e.target.value),children:[i.jsx(J,{value:"all",children:"Tous"}),i.jsx(J,{value:"active",children:"Actives"}),i.jsx(J,{value:"overdue",children:"En retard"}),i.jsx(J,{value:"paid",children:"Payées"})]})]})}),i.jsx(ce,{item:!0,xs:12,md:3,children:i.jsxs(je,{fullWidth:!0,children:[i.jsx(De,{children:"Statut Paiement"}),i.jsxs(Ce,{value:m,label:"Statut Paiement",onChange:e=>g(e.target.value),children:[i.jsx(J,{value:"all",children:"Tous"}),i.jsx(J,{value:"paye",children:"Payé"}),i.jsx(J,{value:"impaye",children:"Impayé"})]})]})})]})}),i.jsxs(me,{component:c,children:[i.jsxs(he,{children:[i.jsx(pe,{children:i.jsxs(xe,{children:[i.jsx(ge,{children:"Client"}),i.jsx(ge,{children:"Montant Dû"}),i.jsx(ge,{align:"right",children:"Payé"}),i.jsx(ge,{align:"right",children:"Restant"}),i.jsx(ge,{align:"center",children:"Progression"}),i.jsx(ge,{align:"center",children:"Statut"}),i.jsx(ge,{align:"center",children:"Statut Paiement"}),i.jsx(ge,{children:"Échéance"}),de.canManageDebts&&i.jsx(ge,{align:"center",children:"Actions"})]})}),i.jsx(ye,{children:(-1===C?n:n.slice(j*C,j*C+C)).map(e=>{try{const t=Qe(e),n="overdue"===e.statut;return i.jsxs(xe,{hover:!0,onClick:()=>Ve(e),sx:{cursor:"pointer"},children:[i.jsx(ge,{children:i.jsxs(r,{children:[i.jsx(o,{variant:"h6",fontWeight:"bold",color:"primary",gutterBottom:!0,children:Se(e)}),e.telephoneClient&&i.jsxs(o,{variant:"body2",color:"text.secondary",display:"block",sx:{mb:.5},children:["📞 ",e.telephoneClient]}),i.jsxs(o,{variant:"body2",color:"primary",fontWeight:"medium",display:"block",sx:{mb:.5},children:["🛍️ Produits: ",ve(e)]}),i.jsxs(o,{variant:"caption",color:"text.secondary",display:"block",children:["Créé le: ",(()=>{try{const t=be(e.dateCreation);return t?Ft(t,"dd/MM/yyyy",{locale:jr}):"Date invalide"}catch(t){return console.warn("Error formatting creation date:",e.dateCreation,t),"Date invalide"}})()," • ID: ",e.venteId||"N/A"]})]})}),i.jsx(ge,{align:"right",children:qr(e.montantTotalCDF,"CDF")}),i.jsx(ge,{align:"right",children:qr(e.montantPayeCDF,"CDF")}),i.jsx(ge,{align:"right",children:i.jsx(o,{variant:"body2",color:e.montantRestantCDF>0?"error":"success.main",children:qr(e.montantRestantCDF,"CDF")})}),i.jsx(ge,{align:"center",children:i.jsxs(r,{sx:{width:100},children:[i.jsx(le,{variant:"determinate",value:t,color:100===t?"success":n?"error":"primary"}),i.jsxs(o,{variant:"caption",color:"text.secondary",children:[Math.round(t),"%"]})]})}),i.jsx(ge,{align:"center",children:i.jsx(M,{icon:Te(e.statut),label:Ue(e.statut),color:Ee(e.statut),size:"small"})}),i.jsx(ge,{align:"center",children:i.jsx(M,{icon:it(e),label:tt(e),color:nt(e),size:"small"})}),i.jsx(ge,{children:i.jsx(o,{variant:"body2",color:n?"error":"text.primary",children:(()=>{const t=be(e.dateEcheance);return t?Ft(t,"dd/MM/yyyy",{locale:jr}):"Date invalide"})()})}),de.canManageDebts&&i.jsx(ge,{align:"center",children:i.jsxs(r,{display:"flex",gap:1,children:[i.jsx(F,{title:"Voir détails",children:i.jsx(U,{size:"small",onClick:()=>Ve(e),children:i.jsx(se,{fontSize:"small"})})}),"paid"!==e.statut&&i.jsx(F,{title:"Ajouter paiement",children:i.jsx(U,{size:"small",color:"primary",onClick:()=>(e=>{k(e),R(e.montantRestantCDF),L("cash"),q(""),oe("CDF"),w(!0),z(""),X("")})(e),children:i.jsx(G,{fontSize:"small"})})})]})})]},e.id)}catch(t){return console.error("Error rendering debt row:",e,t),i.jsx(xe,{children:i.jsx(ge,{colSpan:de.canManageDebts?9:8,children:i.jsxs(o,{color:"error",variant:"body2",children:["Erreur d'affichage pour cette dette. ID: ",e.id||"Inconnu"]})})},e.id||`error-${Math.random()}`)}})})]}),i.jsx(Ae,{rowsPerPageOptions:[5,10,25,50,100,{label:"Voir tout",value:-1}],component:"div",count:n.length,rowsPerPage:-1===C?n.length:C,page:-1===C?0:j,onPageChange:(e,t)=>{-1!==C&&D(t)},onRowsPerPageChange:e=>{const t=parseInt(e.target.value,10);S(t),D(0)},labelRowsPerPage:"Lignes par page:",labelDisplayedRows:({from:e,to:t,count:n})=>-1===C?`Affichage de tous les ${n} éléments`:`${e}-${t} sur ${-1!==n?n:`plus de ${t}`}`})]}),i.jsxs(Re,{open:b,onClose:Oe,maxWidth:"sm",fullWidth:!0,children:[i.jsx(Me,{children:"Ajouter un Paiement"}),i.jsxs(Ie,{children:[B&&i.jsx(O,{severity:"error",sx:{mb:2},children:B}),W&&i.jsx(O,{severity:"success",sx:{mb:2},children:W}),T&&i.jsxs(ce,{container:!0,spacing:2,sx:{mt:1},children:[i.jsxs(ce,{item:!0,xs:12,children:[i.jsxs(o,{variant:"subtitle2",gutterBottom:!0,children:["Client: ",Se(T)]}),i.jsxs(o,{variant:"body2",color:"text.secondary",gutterBottom:!0,children:["Montant restant: ",qr(T.montantRestantCDF,"CDF")]})]}),i.jsx(ce,{item:!0,xs:12,children:i.jsx(Gr,{label:"Montant du paiement",value:A,onChange:e=>R(e),min:0,max:T.montantRestantCDF,step:50,exchangeRate:Q.tauxChangeUSDCDF,required:!0,allowUSDInput:!0,onCurrencyModeChange:e=>oe(e)})}),i.jsx(ce,{item:!0,xs:12,children:i.jsxs(je,{fullWidth:!0,children:[i.jsx(De,{children:"Méthode de paiement"}),i.jsxs(Ce,{value:N,label:"Méthode de paiement",onChange:e=>L(e.target.value),children:[i.jsx(J,{value:"cash",children:"Cash"}),i.jsx(J,{value:"banque",children:"Banque"}),i.jsx(J,{value:"mobile_money",children:"Mobile Money"})]})]})}),i.jsx(ce,{item:!0,xs:12,children:i.jsx(ne,{fullWidth:!0,label:"Notes",multiline:!0,rows:2,value:V,onChange:e=>q(e.target.value)})})]})]}),i.jsxs(Ne,{children:[i.jsx(I,{onClick:Oe,children:"Annuler"}),i.jsx(I,{onClick:Be,variant:"contained",disabled:!T||A<=0,children:"Enregistrer le Paiement"})]})]}),i.jsxs(Re,{open:E,onClose:qe,maxWidth:"md",fullWidth:!0,children:[i.jsx(Me,{children:"Détails de la Dette"}),i.jsx(Ie,{children:T&&i.jsxs(ce,{container:!0,spacing:2,children:[i.jsxs(ce,{item:!0,xs:12,children:[i.jsx(o,{variant:"subtitle2",sx:{fontSize:"1.1rem",fontWeight:"bold"},children:"Produits Achetés à Crédit:"}),i.jsx(o,{variant:"h6",color:"primary",sx:{mt:.5,mb:1},children:ve(T)})]}),i.jsxs(ce,{item:!0,xs:12,md:6,children:[i.jsx(o,{variant:"subtitle2",children:"ID Dette:"}),i.jsx(o,{variant:"body1",children:T.id})]}),i.jsxs(ce,{item:!0,xs:12,md:6,children:[i.jsx(o,{variant:"subtitle2",children:"Référence Vente:"}),i.jsx(o,{variant:"body2",color:"text.secondary",children:T.venteId})]}),i.jsxs(ce,{item:!0,xs:12,md:6,children:[i.jsx(o,{variant:"subtitle2",children:"Client:"}),i.jsx(o,{variant:"body1",sx:{fontWeight:"medium"},children:Se(T)}),T.telephoneClient&&i.jsxs(r,{sx:{mt:.5},children:[i.jsx(o,{variant:"caption",color:"text.secondary",children:"Téléphone:"}),i.jsx(o,{variant:"body2",color:"primary",children:T.telephoneClient})]}),T.adresseClient&&i.jsxs(r,{sx:{mt:.5},children:[i.jsx(o,{variant:"caption",color:"text.secondary",children:"Adresse:"}),i.jsx(o,{variant:"body2",color:"text.primary",children:T.adresseClient})]})]}),i.jsxs(ce,{item:!0,xs:12,md:6,children:[i.jsx(o,{variant:"subtitle2",children:"Statut:"}),i.jsx(M,{icon:Te(T.statut),label:Ue(T.statut),color:Ee(T.statut)})]}),i.jsxs(ce,{item:!0,xs:12,md:6,children:[i.jsx(o,{variant:"subtitle2",children:"Statut Paiement:"}),i.jsx(r,{display:"flex",alignItems:"center",gap:1,sx:{mt:.5},children:de.canManageDebts?i.jsx(We,{control:i.jsx(Ye,{checked:"paye"===T.statutPaiement,onChange:n=>(async(n,i)=>{if(!de.canManageDebts)return;if(!window.confirm(`Êtes-vous sûr de vouloir changer le statut de paiement à "${ke(i)}" ?\n\nCette action modifiera le statut de paiement de la dette.`))return;const r=e.map(e=>e.id===n?{...e,statutPaiement:i}:e);t(r),await ur.setDebts(r),T&&T.id===n&&k({...T,statutPaiement:i}),X(`Statut de paiement mis à jour: ${ke(i)}`),setTimeout(()=>{X("")},3e3)})(T.id,n.target.checked?"paye":"impaye"),color:"success",size:"small"}),label:i.jsxs(r,{display:"flex",alignItems:"center",gap:.5,children:[Le(T.statutPaiement),i.jsx(o,{variant:"body2",children:ke(T.statutPaiement)})]}),labelPlacement:"end"}):i.jsx(M,{icon:Le(T.statutPaiement),label:ke(T.statutPaiement),color:(e=>{switch(e){case"paye":return"success";case"impaye":return"error";default:return"default"}})(T.statutPaiement)})})]}),i.jsxs(ce,{item:!0,xs:12,md:4,children:[i.jsx(o,{variant:"subtitle2",children:"Montant Dû:"}),i.jsx(o,{variant:"body1",color:"primary",sx:{fontWeight:"bold"},children:qr(T.montantTotalCDF,"CDF")})]}),i.jsxs(ce,{item:!0,xs:12,md:4,children:[i.jsx(o,{variant:"subtitle2",children:"Montant Payé:"}),i.jsx(o,{variant:"body1",color:"success.main",children:qr(T.montantPayeCDF,"CDF")})]}),i.jsxs(ce,{item:!0,xs:12,md:4,children:[i.jsx(o,{variant:"subtitle2",children:"Montant Restant:"}),i.jsx(o,{variant:"body1",color:"error",children:qr(T.montantRestantCDF,"CDF")})]}),i.jsxs(ce,{item:!0,xs:12,children:[i.jsx(o,{variant:"subtitle2",gutterBottom:!0,children:"Progression du Paiement:"}),i.jsxs(r,{sx:{width:"100%",mb:1},children:[i.jsx(le,{variant:"determinate",value:Qe(T),color:100===Qe(T)?"success":"overdue"===T.statut?"error":"primary",sx:{height:10,borderRadius:5}}),i.jsxs(r,{sx:{display:"flex",justifyContent:"space-between",mt:1},children:[i.jsxs(o,{variant:"caption",color:"text.secondary",children:[Math.round(Qe(T)),"% payé"]}),i.jsx(o,{variant:"caption",color:"text.secondary",children:T.montantRestantCDF>0?`${qr(T.montantRestantCDF,"CDF")} restant`:"Entièrement payé"})]})]})]}),i.jsxs(ce,{item:!0,xs:12,md:6,children:[i.jsx(o,{variant:"subtitle2",children:"Date de Création:"}),i.jsx(o,{variant:"body1",children:(()=>{const e=be(T.dateCreation);return e?Ft(e,"dd/MM/yyyy",{locale:jr}):"Date invalide"})()})]}),i.jsxs(ce,{item:!0,xs:12,md:6,children:[i.jsx(o,{variant:"subtitle2",children:"Date d'Échéance:"}),i.jsx(o,{variant:"body1",color:"overdue"===T.statut?"error":"text.primary",children:(()=>{const e=be(T.dateEcheance);return e?Ft(e,"dd/MM/yyyy",{locale:jr}):"Date invalide"})()})]}),i.jsxs(ce,{item:!0,xs:12,children:[i.jsxs(o,{variant:"subtitle2",gutterBottom:!0,children:["Historique des Paiements (",T.paiements.length,")"]}),0===T.paiements.length?i.jsx(O,{severity:"info",children:"Aucun paiement enregistré"}):i.jsx(p,{dense:!0,children:T.paiements.map((e,t)=>i.jsxs(bt.Fragment,{children:[i.jsx(x,{children:i.jsx(y,{primary:i.jsxs(r,{display:"flex",alignItems:"center",gap:1,children:[ze(e.methodePaiement),i.jsx(o,{variant:"body2",children:"USD"===e.deviseOriginale?`${qr(e.montantUSD||0,"USD")} (≈ ${qr(e.montantCDF,"CDF")})`:`${qr(e.montantCDF,"CDF")} (≈ ${qr(e.montantUSD||0,"USD")})`}),i.jsx(M,{label:$e(e.methodePaiement),size:"small"})]}),secondary:i.jsxs(r,{children:[i.jsx(o,{variant:"caption",children:(()=>{const t=be(e.datePaiement);return t?Ft(t,"dd/MM/yyyy HH:mm",{locale:jr}):"Date invalide"})()}),e.notes&&i.jsx(o,{variant:"caption",display:"block",children:e.notes})]})})}),t<T.paiements.length-1&&i.jsx(h,{})]},e.id))})]}),de.canManageDebts&&"paid"!==T.statut&&Z&&i.jsx(ce,{item:!0,xs:12,children:i.jsxs(c,{elevation:2,sx:{p:2,mt:2,bgcolor:"background.default"},children:[i.jsxs(o,{variant:"subtitle2",gutterBottom:!0,sx:{display:"flex",alignItems:"center",gap:1},children:[i.jsx(G,{}),"Ajouter un Paiement"]}),i.jsxs(ce,{container:!0,spacing:2,sx:{mt:1},children:[i.jsx(ce,{item:!0,xs:12,children:i.jsxs(o,{variant:"body2",color:"text.secondary",gutterBottom:!0,children:["Montant restant: ",qr(T.montantRestantCDF,"CDF")]})}),i.jsx(ce,{item:!0,xs:12,md:6,children:i.jsx(Gr,{label:"Montant à payer",value:A,onChange:e=>R(e),min:0,max:T.montantRestantCDF,step:50,exchangeRate:Q.tauxChangeUSDCDF,required:!0,allowUSDInput:!0,onCurrencyModeChange:e=>oe(e)})}),i.jsx(ce,{item:!0,xs:12,md:6,children:i.jsxs(je,{fullWidth:!0,children:[i.jsx(De,{children:"Méthode de paiement"}),i.jsxs(Ce,{value:N,onChange:e=>L(e.target.value),label:"Méthode de paiement",children:[i.jsx(J,{value:"cash",children:i.jsxs(r,{display:"flex",alignItems:"center",gap:1,children:[i.jsx(f,{fontSize:"small"}),"Cash"]})}),i.jsx(J,{value:"mobile_money",children:i.jsxs(r,{display:"flex",alignItems:"center",gap:1,children:[i.jsx(Je,{fontSize:"small"}),"Mobile Money"]})}),i.jsx(J,{value:"banque",children:i.jsxs(r,{display:"flex",alignItems:"center",gap:1,children:[i.jsx(_e,{fontSize:"small"}),"Banque"]})})]})]})}),i.jsx(ce,{item:!0,xs:12,children:i.jsx(ne,{fullWidth:!0,label:"Notes (optionnel)",value:V,onChange:e=>q(e.target.value),multiline:!0,rows:2,placeholder:"Ajouter des notes sur ce paiement..."})}),B&&i.jsx(ce,{item:!0,xs:12,children:i.jsx(O,{severity:"error",children:B})}),W&&i.jsx(ce,{item:!0,xs:12,children:i.jsx(O,{severity:"success",children:W})}),i.jsx(ce,{item:!0,xs:12,children:i.jsxs(r,{display:"flex",gap:1,justifyContent:"flex-end",children:[i.jsx(I,{variant:"outlined",onClick:()=>{re(!1),R(0),q(""),oe("CDF"),z(""),X("")},children:"Annuler"}),i.jsx(I,{variant:"contained",color:"success",onClick:()=>{R(T.montantRestantCDF),q("Paiement complet")},startIcon:i.jsx(G,{}),children:"Paiement Complet"}),i.jsx(I,{variant:"contained",color:"primary",onClick:Be,startIcon:i.jsx(G,{}),disabled:A<=0,children:"Payer"})]})})]})]})}),T.notes&&i.jsxs(ce,{item:!0,xs:12,children:[i.jsx(o,{variant:"subtitle2",children:"Notes:"}),i.jsx(o,{variant:"body1",children:T.notes})]})]})}),i.jsxs(Ne,{children:[de.canManageDebts&&T&&"paid"!==T.statut&&!Z&&i.jsx(I,{variant:"contained",color:"primary",onClick:()=>{re(!0),R(0),L("cash"),q(""),z(""),X("")},startIcon:i.jsx(G,{}),sx:{mr:1},children:"Ajouter Paiement"}),i.jsx(I,{onClick:qe,children:"Fermer"})]})]})]})},aa=(e,t="dd/MM/yyyy")=>{try{const n="string"==typeof e?new Date(e):e;return Nt(n)?Ft(n,t,{locale:jr}):"Date invalide"}catch(ba){return console.warn("Invalid date value:",e,ba),"Date invalide"}},sa=()=>{var e;try{const[t,n]=vt.useState([]),[a,s]=vt.useState([]),[l,d]=vt.useState(""),[u,m]=vt.useState(""),[h,p]=vt.useState(""),[x,g]=vt.useState(0),[y,j]=vt.useState(10),[D,C]=vt.useState(!1),[v,S]=vt.useState(null),[b,w]=vt.useState({description:"",montantCDF:0,categorie:"",dateDepense:aa(new Date,"yyyy-MM-dd"),notes:""}),[E,P]=vt.useState(""),[T,k]=vt.useState(""),[A,R]=vt.useState({tauxChangeUSDCDF:2800}),[N,L]=vt.useState(!1),[V,B]=vt.useState(!1),[_,z]=vt.useState(null),[$,W]=vt.useState(!1),X=mr.getUserPermissions(),Q=mr.getCurrentUser(),H=["Loyer","Électricité","Eau","Internet","Téléphone","Transport","Carburant","Fournitures de bureau","Marketing","Maintenance","Assurance","Taxes","Salaires","Formation","Équipement","Autres"];vt.useEffect(()=>{G()},[]),vt.useEffect(()=>{Y()},[t,l,u,h]),vt.useEffect(()=>{-1===y&&j(a.length||1)},[a.length,y]);const G=async()=>{const e=await ur.getExpenses(),t=await ur.getSettings();n(e),R(t)},Y=()=>{let e=t;if(l&&(e=e.filter(e=>e.description.toLowerCase().includes(l.toLowerCase())||e.categorie.toLowerCase().includes(l.toLowerCase())||e.notes&&e.notes.toLowerCase().includes(l.toLowerCase()))),u&&(e=e.filter(e=>e.categorie===u)),h){const t=new Date;let n,i;switch(h){case"today":n=new Date(t.getFullYear(),t.getMonth(),t.getDate()),i=new Date(t.getFullYear(),t.getMonth(),t.getDate(),23,59,59);break;case"this_month":n=Mt(t),i=It(t);break;case"last_month":const e=new Date(t.getFullYear(),t.getMonth()-1,1);n=Mt(e),i=It(e);break;default:n=new Date(0),i=new Date}e=e.filter(e=>{const t=new Date(e.dateDepense);return Nt(t)&&Lt(t,{start:n,end:i})})}s(e)},K=e=>{e?(S(e),w({description:e.description,montantCDF:e.montantCDF,categorie:e.categorie,dateDepense:aa(e.dateDepense,"yyyy-MM-dd"),notes:e.notes||""})):(S(null),w({description:"",montantCDF:0,categorie:"",dateDepense:aa(new Date,"yyyy-MM-dd"),notes:""})),C(!0),P(""),k("")},Z=()=>{C(!1),S(null),P(""),k(""),L(!1)},re=async()=>{var e;if(b.description.trim())if(b.montantCDF<=0)P("Le montant doit être supérieur à 0");else if(b.categorie)if(b.dateDepense){if(v){const e={...v,description:b.description.trim(),montantCDF:b.montantCDF,montantUSD:b.montantCDF/A.tauxChangeUSDCDF,categorie:b.categorie,dateDepense:b.dateDepense,notes:b.notes.trim()||void 0},i=t.map(t=>t.id===v.id?e:t);n(i),await ur.setExpenses(i),k("Dépense mise à jour avec succès")}else{const r=N||(null==(e=A.impression)?void 0:e.impressionAutomatique)||!1,a=r?await Zr.generateExpenseReceiptNumber():void 0,s={id:Date.now().toString(),description:b.description.trim(),montantCDF:b.montantCDF,montantUSD:b.montantCDF/A.tauxChangeUSDCDF,categorie:b.categorie,dateDepense:b.dateDepense,notes:b.notes.trim()||void 0,creePar:(null==Q?void 0:Q.nom)||"Inconnu",numeroRecu:a},o=[...t,s];if(n(o),await ur.setExpenses(o),k("Dépense créée avec succès"),r)try{W(!0);const e=await Zr.createExpenseReceiptData(s);z(e),B(!0)}catch(i){console.error("Erreur lors de la génération du reçu:",i),P("Erreur lors de la génération du reçu")}finally{W(!1)}}setTimeout(()=>{Z()},1500)}else P("La date est requise");else P("La catégorie est requise");else P("La description est requise")},ae=async e=>{if(window.confirm(`Êtes-vous sûr de vouloir supprimer cette dépense "${e.description}" ?`)){const i=t.filter(t=>t.id!==e.id);n(i),await ur.setExpenses(i),k("Dépense supprimée avec succès"),setTimeout(()=>k(""),3e3)}},se=(e,t)=>{-1!==y&&g(t)},oe=e=>{const t=parseInt(e.target.value,10);j(t),g(0)},le=new Date,de={start:Mt(le),end:It(le)},ue=t.filter(e=>{const t=new Date(e.dateDepense);return Nt(t)&&t.toDateString()===le.toDateString()}),ve=t.filter(e=>{const t=new Date(e.dateDepense);return Nt(t)&&Lt(t,de)}),Se=t.length,be=ue.reduce((e,t)=>e+t.montantCDF,0),fe=ve.reduce((e,t)=>e+t.montantCDF,0),we=t.reduce((e,t)=>e+t.montantCDF,0),Ue=t.reduce((e,t)=>(e[t.categorie]=(e[t.categorie]||0)+t.montantUSD,e),{}),Te=Object.entries(Ue).sort(([,e],[,t])=>t-e).slice(0,5),Le=()=>{const e=`SmartBoutique_Depenses_${aa(new Date,"yyyy-MM-dd")}.csv`;Li.downloadCSV(a,_i,e),k("Dépenses exportées en CSV avec succès (compatible Excel)"),setTimeout(()=>k(""),3e3)};return i.jsxs(r,{children:[i.jsxs(r,{display:"flex",justifyContent:"space-between",alignItems:"center",mb:3,children:[i.jsx(o,{variant:"h4",children:"Gestion des Dépenses"}),i.jsxs(r,{display:"flex",gap:2,children:[i.jsx(I,{variant:"outlined",startIcon:i.jsx(Fe,{}),onClick:Le,children:"Exporter les Dépenses"}),X.canManageExpenses&&i.jsx(I,{variant:"contained",startIcon:i.jsx(Ee,{}),onClick:()=>K(),children:"Nouvelle Dépense"})]})]}),T&&i.jsx(O,{severity:"success",sx:{mb:2},onClose:()=>k(""),children:T}),i.jsxs(ce,{container:!0,spacing:3,sx:{mb:3},children:[i.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:i.jsx(ee,{children:i.jsx(te,{children:i.jsxs(r,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[i.jsxs(r,{children:[i.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Dépenses du jour"}),i.jsx(o,{variant:"h6",children:ue.length}),i.jsxs(o,{variant:"body2",color:"error",fontWeight:"medium",children:[Br(be,A.tauxChangeUSDCDF).primaryAmount," ",Br(be,A.tauxChangeUSDCDF).primaryCurrency]}),i.jsxs(o,{variant:"caption",color:"text.secondary",children:["≈ $",Br(be,A.tauxChangeUSDCDF).secondaryAmount]})]}),i.jsx(f,{color:"primary",sx:{fontSize:40}})]})})})}),i.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:i.jsx(ee,{children:i.jsx(te,{children:i.jsxs(r,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[i.jsxs(r,{children:[i.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Dépenses du mois"}),i.jsx(o,{variant:"h6",children:ve.length}),i.jsxs(o,{variant:"body2",color:"error",fontWeight:"medium",children:[Br(fe,A.tauxChangeUSDCDF).primaryAmount," ",Br(fe,A.tauxChangeUSDCDF).primaryCurrency]}),i.jsxs(o,{variant:"caption",color:"text.secondary",children:["≈ $",Br(fe,A.tauxChangeUSDCDF).secondaryAmount]})]}),i.jsx(et,{color:"error",sx:{fontSize:40}})]})})})}),i.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:i.jsx(ee,{children:i.jsx(te,{children:i.jsxs(r,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[i.jsxs(r,{children:[i.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Total Dépenses"}),i.jsx(o,{variant:"h6",children:Se}),i.jsxs(o,{variant:"body2",color:"error",fontWeight:"medium",children:[Br(we,A.tauxChangeUSDCDF).primaryAmount," ",Br(we,A.tauxChangeUSDCDF).primaryCurrency]}),i.jsxs(o,{variant:"caption",color:"text.secondary",children:["≈ $",Br(we,A.tauxChangeUSDCDF).secondaryAmount]})]}),i.jsx(tt,{color:"info",sx:{fontSize:40}})]})})})}),i.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:i.jsx(ee,{children:i.jsx(te,{children:i.jsxs(r,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[i.jsxs(r,{children:[i.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Catégories"}),i.jsx(o,{variant:"h6",children:Object.keys(Ue).length}),i.jsxs(o,{variant:"body2",color:"text.secondary",children:["Top: ",(null==(e=Te[0])?void 0:e[0])||"N/A"]})]}),i.jsx(nt,{color:"warning",sx:{fontSize:40}})]})})})})]}),i.jsx(c,{sx:{p:2,mb:3},children:i.jsxs(ce,{container:!0,spacing:2,alignItems:"center",children:[i.jsx(ce,{item:!0,xs:12,md:4,children:i.jsx(ne,{fullWidth:!0,placeholder:"Rechercher par description, catégorie ou notes...",value:l,onChange:e=>d(e.target.value),InputProps:{startAdornment:i.jsx(ie,{position:"start",children:i.jsx(Pe,{})})}})}),i.jsx(ce,{item:!0,xs:12,md:3,children:i.jsxs(je,{fullWidth:!0,children:[i.jsx(De,{children:"Catégorie"}),i.jsxs(Ce,{value:u,label:"Catégorie",onChange:e=>m(e.target.value),children:[i.jsx(J,{value:"",children:"Toutes les catégories"}),H.map(e=>i.jsx(J,{value:e,children:e},e))]})]})}),i.jsx(ce,{item:!0,xs:12,md:3,children:i.jsxs(je,{fullWidth:!0,children:[i.jsx(De,{children:"Période"}),i.jsxs(Ce,{value:h,label:"Période",onChange:e=>p(e.target.value),children:[i.jsx(J,{value:"",children:"Toutes les périodes"}),i.jsx(J,{value:"today",children:"Aujourd'hui"}),i.jsx(J,{value:"this_month",children:"Ce mois"}),i.jsx(J,{value:"last_month",children:"Mois dernier"})]})]})})]})}),Te.length>0&&i.jsxs(c,{sx:{p:2,mb:3},children:[i.jsx(o,{variant:"h6",gutterBottom:!0,children:"Top 5 Catégories"}),i.jsx(ce,{container:!0,spacing:1,children:Te.map(([e,t])=>i.jsx(ce,{item:!0,children:i.jsx(M,{label:`${e}: ${qr(t,"CDF")}`,color:"primary",variant:"outlined"})},e))})]}),i.jsxs(me,{component:c,children:[i.jsxs(he,{children:[i.jsx(pe,{children:i.jsxs(xe,{children:[i.jsx(ge,{children:"Description"}),i.jsx(ge,{children:"Catégorie"}),i.jsx(ge,{align:"right",children:"Montant CDF"}),i.jsx(ge,{align:"right",children:"Montant USD"}),i.jsx(ge,{children:"Date"}),i.jsx(ge,{children:"Créé par"}),X.canManageExpenses&&i.jsx(ge,{align:"center",children:"Actions"})]})}),i.jsx(ye,{children:(-1===y?a:a.slice(x*y,x*y+y)).map(e=>i.jsxs(xe,{hover:!0,onClick:()=>K(e),sx:{cursor:"pointer"},children:[i.jsx(ge,{children:i.jsxs(r,{children:[i.jsx(o,{variant:"subtitle2",children:e.description}),e.notes&&i.jsx(o,{variant:"caption",color:"text.secondary",children:e.notes})]})}),i.jsx(ge,{children:i.jsx(M,{label:e.categorie,size:"small"})}),i.jsx(ge,{align:"right",children:qr(e.montantCDF,"CDF")}),i.jsx(ge,{align:"right",children:e.montantUSD?qr(e.montantUSD,"USD"):"-"}),i.jsx(ge,{children:aa(e.dateDepense)}),i.jsx(ge,{children:e.creePar}),X.canManageExpenses&&i.jsx(ge,{align:"center",children:i.jsxs(r,{display:"flex",gap:1,children:[i.jsx(F,{title:"Modifier",children:i.jsx(U,{size:"small",onClick:()=>K(e),children:i.jsx(ke,{fontSize:"small"})})}),mr.hasRole(["super_admin","admin"])&&i.jsx(F,{title:"Supprimer",children:i.jsx(U,{size:"small",color:"error",onClick:()=>ae(e),children:i.jsx(q,{fontSize:"small"})})})]})})]},e.id))})]}),i.jsx(Ae,{rowsPerPageOptions:[5,10,25,50,100,{label:"Voir tout",value:-1}],component:"div",count:a.length,rowsPerPage:-1===y?a.length:y,page:-1===y?0:x,onPageChange:se,onRowsPerPageChange:oe,labelRowsPerPage:"Lignes par page:",labelDisplayedRows:({from:e,to:t,count:n})=>-1===y?`Affichage de tous les ${n} éléments`:`${e}-${t} sur ${-1!==n?n:`plus de ${t}`}`})]}),i.jsxs(Re,{open:D,onClose:Z,maxWidth:"md",fullWidth:!0,children:[i.jsx(Me,{children:v?"Modifier la Dépense":"Nouvelle Dépense"}),i.jsxs(Ie,{children:[E&&i.jsx(O,{severity:"error",sx:{mb:2},children:E}),T&&i.jsx(O,{severity:"success",sx:{mb:2},children:T}),i.jsxs(ce,{container:!0,spacing:2,sx:{mt:1},children:[i.jsx(ce,{item:!0,xs:12,children:i.jsx(ne,{fullWidth:!0,label:"Description *",value:b.description,onChange:e=>w({...b,description:e.target.value})})}),i.jsx(ce,{item:!0,xs:12,children:i.jsx(Gr,{label:"Montant de la dépense",value:b.montantCDF,onChange:e=>w({...b,montantCDF:e}),min:0,step:50,exchangeRate:A.tauxChangeUSDCDF,required:!0,allowUSDInput:!0})}),i.jsx(ce,{item:!0,xs:12,md:6,children:i.jsxs(je,{fullWidth:!0,children:[i.jsx(De,{children:"Catégorie *"}),i.jsx(Ce,{value:b.categorie,label:"Catégorie *",onChange:e=>w({...b,categorie:e.target.value}),children:H.map(e=>i.jsx(J,{value:e,children:e},e))})]})}),i.jsx(ce,{item:!0,xs:12,md:6,children:i.jsx(ne,{fullWidth:!0,label:"Date de la dépense *",type:"date",value:b.dateDepense,onChange:e=>w({...b,dateDepense:e.target.value}),InputLabelProps:{shrink:!0}})}),i.jsx(ce,{item:!0,xs:12,children:i.jsx(ne,{fullWidth:!0,label:"Notes",multiline:!0,rows:3,value:b.notes,onChange:e=>w({...b,notes:e.target.value})})})]})]}),i.jsxs(Ne,{children:[!v&&i.jsx(We,{control:i.jsx(Qe,{checked:N,onChange:e=>L(e.target.checked)}),label:"Imprimer reçu",sx:{mr:"auto"}}),$&&i.jsxs(r,{display:"flex",alignItems:"center",gap:1,mr:2,children:[i.jsx(Ve,{size:20}),i.jsx(o,{variant:"body2",children:"Génération du reçu..."})]}),i.jsx(I,{onClick:Z,children:"Annuler"}),i.jsx(I,{onClick:re,variant:"contained",disabled:$,children:v?"Mettre à jour":"Créer"})]})]}),i.jsx(na,{open:V,onClose:()=>B(!1),receiptData:_,onPrintSuccess:()=>{k("Reçu imprimé avec succès"),setTimeout(()=>k(""),3e3)}})]})}catch(ba){return console.error("ExpensesPage error:",ba),i.jsxs(r,{p:3,children:[i.jsx(o,{variant:"h4",gutterBottom:!0,children:"Dépenses"}),i.jsx(O,{severity:"error",children:"Une erreur s'est produite lors du chargement de la page des dépenses. Veuillez recharger l'application ou contacter le support technique."})]})}};function oa(e){const{children:t,value:n,index:a,...s}=e;return i.jsx("div",{role:"tabpanel",hidden:n!==a,id:`reports-tabpanel-${a}`,"aria-labelledby":`reports-tab-${a}`,...s,children:n===a&&i.jsx(r,{sx:{p:3},children:t})})}const la=()=>{const[e,t]=vt.useState(0),[n,a]=vt.useState("this_month"),[s,l]=vt.useState(""),[d,u]=vt.useState(""),[m,p]=vt.useState([]),[x,g]=vt.useState([]),[y,j]=vt.useState([]),[S,b]=vt.useState([]),[E,P]=vt.useState([]),[F,U]=vt.useState([]),[T,k]=vt.useState({tauxChangeUSDCDF:2800}),[A,R]=vt.useState(""),[N,L]=vt.useState("");vt.useEffect(()=>{(async()=>{try{const e=await ur.getSettings();k(e)}catch(e){console.error("Error loading settings:",e)}})()},[]),vt.useEffect(()=>{V()},[]),vt.useEffect(()=>{q()},[m,S,n,s,d]);const V=async()=>{try{console.log("ReportsPage: Loading data..."),p(await ur.getSales()),g(await ur.getProducts()),j(await ur.getDebts()),b(await ur.getExpenses()),console.log("ReportsPage: Data loaded successfully")}catch(e){console.error("ReportsPage: Error loading data:",e),p([]),g([]),j([]),b([])}},q=()=>{const e=new Date;let t,i;switch(n){case"today":t=Ut(e),i=Tt(e);break;case"this_week":t=kt(e,7),i=e;break;case"this_month":default:t=Mt(e),i=It(e);break;case"last_month":const n=new Date(e.getFullYear(),e.getMonth()-1,1);t=Mt(n),i=It(n);break;case"custom":s&&d?(t=new Date(s),i=new Date(d)):(t=Mt(e),i=It(e))}const r=m.filter(e=>{const n=new Date(e.datevente);return Lt(n,{start:t,end:i})}),a=S.filter(e=>{const n=new Date(e.dateDepense);return Lt(n,{start:t,end:i})});P(r),U(a)},B=(e,t)=>"USD"===t?`$${e.toLocaleString("fr-FR",{minimumFractionDigits:2})}`:`${e.toLocaleString("fr-FR")} CDF`,_=e=>{try{const t=null==e||isNaN(e)?0:e,n=t/((null==T?void 0:T.tauxChangeUSDCDF)||2800);return{primary:`${t.toLocaleString("fr-FR")} CDF`,secondary:`$${n.toLocaleString("fr-FR",{minimumFractionDigits:2,maximumFractionDigits:2})}`}}catch(t){return console.error("ReportsPage: Error formatting dual currency:",t),{primary:"0 CDF",secondary:"$0.00"}}},z=E.reduce((e,t)=>e+t.totalCDF,0),$=E.length,W=$>0?z/$:0,X=F.reduce((e,t)=>e+t.montantCDF,0),Q=z-X,H=E.reduce((e,t)=>(t.produits.forEach(t=>{e[t.produitId]||(e[t.produitId]={nom:t.nomProduit,quantite:0,revenue:0}),e[t.produitId].quantite+=t.quantite,e[t.produitId].revenue+=t.totalCDF}),e),{}),G=Object.values(H).sort((e,t)=>t.revenue-e.revenue).slice(0,10),Y=E.reduce((e,t)=>(e[t.methodePaiement]=(e[t.methodePaiement]||0)+t.totalCDF,e),{}),K=E.reduce((e,t)=>(t.produits.forEach(t=>{const n=x.find(e=>e.id===t.produitId);n&&(e[n.categorie]=(e[n.categorie]||0)+t.totalCDF)}),e),{}),Z=E.reduce((e,t)=>{const n=Ft(new Date(t.datevente),"yyyy-MM-dd");return e[n]=(e[n]||0)+t.totalCDF,e},{}),ie=Array.from({length:30},(e,t)=>{const n=kt(new Date,29-t),i=Ft(n,"yyyy-MM-dd");return{date:Ft(n,"dd/MM"),revenue:Z[i]||0}}),re={labels:ie.map(e=>e.date),datasets:[{label:"Profit (USD)",data:ie.map(e=>e.revenue),borderColor:"rgb(75, 192, 192)",backgroundColor:"rgba(75, 192, 192, 0.2)",tension:.1}]},ae={labels:["Cash","Banque","Mobile Money"],datasets:[{data:[Y.cash||0,Y.banque||0,Y.mobile_money||0],backgroundColor:["#FF6384","#36A2EB","#FFCE56"]}]},se={labels:Object.keys(K),datasets:[{label:"Profit par catégorie (USD)",data:Object.values(K),backgroundColor:["#FF6384","#36A2EB","#FFCE56","#4BC0C0","#9966FF","#FF9F40"]}]};return i.jsxs(r,{children:[i.jsxs(r,{display:"flex",justifyContent:"space-between",alignItems:"center",mb:3,children:[i.jsx(o,{variant:"h4",children:"Rapports et Analyses"}),i.jsx(I,{variant:"contained",startIcon:i.jsx(Fe,{}),onClick:()=>{try{const e=(null==T?void 0:T.tauxChangeUSDCDF)||2800,t=(e,t)=>{const n=null==e||isNaN(e)?0:e;return"USD"===t?`$${kr(n,2,2)}`:`${kr(n,0,0)} CDF`},i=[{label:"Période",value:"custom"===n?`${s} - ${d}`:n},{label:"Date de Génération",value:Ft(new Date,"dd/MM/yyyy HH:mm")},{label:"Total Ventes",value:$.toString()},{label:"Chiffre d'Affaires (CDF)",value:t(z,"CDF")},{label:"Chiffre d'Affaires (USD)",value:t(z/e,"USD")},{label:"Total Dépenses (CDF)",value:t(X,"CDF")},{label:"Total Dépenses (USD)",value:t(X/e,"USD")},{label:"Profit Final (CDF)",value:t(Q,"CDF")},{label:"Profit Final (USD)",value:t(Q/e,"USD")},{label:"Vente Moyenne (CDF)",value:t(W,"CDF")},{label:"Vente Moyenne (USD)",value:t(W/e,"USD")}],r=G.map((n,i)=>({rang:i+1,produit:n.nom||"N/A",quantiteVendue:n.quantite||0,revenuesCDF:t(n.revenue||0,"CDF"),revenuesUSD:t((n.revenue||0)/e,"USD"),revenuMoyenCDF:t((n.revenue||0)/(n.quantite||1),"CDF"),revenuMoyenUSD:t((n.revenue||0)/(n.quantite||1)/e,"USD")})),a=new Date;let o,l;switch(n){case"today":o=Ut(a),l=Tt(a);break;case"this_week":o=kt(a,7),l=a;break;case"this_month":default:o=Mt(a),l=It(a);break;case"last_month":const e=new Date(a.getFullYear(),a.getMonth()-1,1);o=Mt(e),l=It(e);break;case"custom":s&&d?(o=new Date(s),l=new Date(d)):(o=Mt(a),l=It(a))}const c=Ot({start:o,end:l}).map(n=>{const i=Ft(n,"yyyy-MM-dd"),r=E.filter(e=>Ft(new Date(e.datevente),"yyyy-MM-dd")===i).reduce((e,t)=>e+t.totalCDF,0);return{date:Ft(n,"dd/MM/yyyy"),revenueCDF:t(r,"CDF"),revenueUSD:t(r/e,"USD")}}),u=Vt({start:o,end:l},{weekStartsOn:1}).map(n=>{const i=qt(n,{weekStartsOn:1}),r=E.filter(e=>{const t=new Date(e.datevente);return Lt(t,{start:n,end:i})}).reduce((e,t)=>e+t.totalCDF,0);return{semaine:`${Ft(n,"dd/MM")} - ${Ft(i,"dd/MM/yyyy")}`,revenueCDF:t(r,"CDF"),revenueUSD:t(r/e,"USD")}}),m=Bt({start:o,end:l}).map(n=>{const i=It(n),r=E.filter(e=>{const t=new Date(e.datevente);return Lt(t,{start:n,end:i})}).reduce((e,t)=>e+t.totalCDF,0);return{mois:Ft(n,"MMMM yyyy"),revenueCDF:t(r,"CDF"),revenueUSD:t(r/e,"USD")}}),h=y.filter(e=>"paid"!==e.statut),p=h.reduce((e,t)=>e+t.montantRestantCDF,0),g={nombreDettes:h.length,montantTotalCDF:t(p,"CDF"),montantTotalUSD:t(p/e,"USD")},j=Object.entries(K).map(([n,i])=>{const r=E.reduce((e,t)=>e+t.produits.filter(e=>{const t=x.find(t=>t.id===e.produitId);return t&&t.categorie===n}).length,0);return{categorie:n||"Non catégorisé",revenuesCDF:t(i,"CDF"),revenuesUSD:t(i/e,"USD"),nombreVentes:r}});let D="SmartBoutique - Rapport d'Analyse\n\n";D+="RÉSUMÉ DU RAPPORT\n",D+="Métrique,Valeur\n",i.forEach(e=>{D+=`"${e.label}","${e.value}"\n`}),D+="\n\nTOP PRODUITS LES PLUS VENDUS\n",D+="Rang,Produit,Quantité Vendue,Revenus (CDF),Revenus (USD),Revenu Moyen (CDF),Revenu Moyen (USD)\n",r.forEach(e=>{D+=`${e.rang},"${e.produit}",${e.quantiteVendue},"${e.revenuesCDF}","${e.revenuesUSD}","${e.revenuMoyenCDF}","${e.revenuMoyenUSD}"\n`}),D+="\n\nREVENU JOURNALIER\n",D+="Date,Revenus (CDF),Revenus (USD)\n",c.forEach(e=>{D+=`"${e.date}","${e.revenueCDF}","${e.revenueUSD}"\n`}),D+="\n\nREVENU PAR SEMAINE\n",D+="Semaine,Revenus (CDF),Revenus (USD)\n",u.forEach(e=>{D+=`"${e.semaine}","${e.revenueCDF}","${e.revenueUSD}"\n`}),D+="\n\nREVENU PAR MOIS\n",D+="Mois,Revenus (CDF),Revenus (USD)\n",m.forEach(e=>{D+=`"${e.mois}","${e.revenueCDF}","${e.revenueUSD}"\n`}),D+="\n\nDETTES DUES\n",D+="Métrique,Valeur\n",D+=`"Nombre de Dettes Impayées","${g.nombreDettes}"\n`,D+=`"Montant Total (CDF)","${g.montantTotalCDF}"\n`,D+=`"Montant Total (USD)","${g.montantTotalUSD}"\n`,D+="\n\nPERFORMANCE PAR CATÉGORIE\n",D+="Catégorie,Revenus (CDF),Revenus (USD),Nombre de Ventes\n",j.forEach(e=>{D+=`"${e.categorie}","${e.revenuesCDF}","${e.revenuesUSD}",${e.nombreVentes}\n`});const C=`rapport_${Ft(new Date,"yyyy-MM-dd_HH-mm")}.csv`,v=new Blob(["\ufeff"+D],{type:"text/csv;charset=utf-8"}),S=URL.createObjectURL(v),b=document.createElement("a");b.href=S,b.download=C,document.body.appendChild(b),b.click(),document.body.removeChild(b),URL.revokeObjectURL(S),R("Rapport exporté avec succès (compatible Excel)"),setTimeout(()=>R(""),3e3)}catch(e){console.error("Erreur lors de l'exportation du rapport:",e),L("Erreur lors de l'exportation du rapport. Veuillez réessayer."),setTimeout(()=>L(""),5e3)}},children:"Exporter le Rapport"})]}),A&&i.jsx(O,{severity:"success",sx:{mb:2},onClose:()=>R(""),children:A}),N&&i.jsx(O,{severity:"error",sx:{mb:2},onClose:()=>L(""),children:N}),i.jsx(c,{sx:{p:2,mb:3},children:i.jsxs(ce,{container:!0,spacing:2,alignItems:"center",children:[i.jsx(ce,{item:!0,xs:12,md:4,children:i.jsxs(je,{fullWidth:!0,children:[i.jsx(De,{children:"Période"}),i.jsxs(Ce,{value:n,label:"Période",onChange:e=>a(e.target.value),children:[i.jsx(J,{value:"today",children:"Aujourd'hui"}),i.jsx(J,{value:"this_week",children:"Cette semaine"}),i.jsx(J,{value:"this_month",children:"Ce mois"}),i.jsx(J,{value:"last_month",children:"Mois dernier"}),i.jsx(J,{value:"custom",children:"Personnalisée"})]})]})}),"custom"===n&&i.jsxs(i.Fragment,{children:[i.jsx(ce,{item:!0,xs:12,md:3,children:i.jsx(ne,{fullWidth:!0,label:"Date de début",type:"date",value:s,onChange:e=>l(e.target.value),InputLabelProps:{shrink:!0}})}),i.jsx(ce,{item:!0,xs:12,md:3,children:i.jsx(ne,{fullWidth:!0,label:"Date de fin",type:"date",value:d,onChange:e=>u(e.target.value),InputLabelProps:{shrink:!0}})})]})]})}),i.jsxs(ce,{container:!0,spacing:3,sx:{mb:3},children:[i.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:i.jsx(ee,{children:i.jsx(te,{children:i.jsxs(r,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[i.jsxs(r,{children:[i.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Chiffre d'Affaires"}),i.jsx(o,{variant:"h6",color:"primary",fontWeight:"medium",children:_(z*((null==T?void 0:T.tauxChangeUSDCDF)||2800)).primary}),i.jsxs(o,{variant:"body2",color:"text.secondary",children:["≈ ",_(z*((null==T?void 0:T.tauxChangeUSDCDF)||2800)).secondary]})]}),i.jsx(ve,{color:"primary",sx:{fontSize:40}})]})})})}),i.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:i.jsx(ee,{children:i.jsx(te,{children:i.jsxs(r,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[i.jsxs(r,{children:[i.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Nombre de Ventes"}),i.jsx(o,{variant:"h6",color:"success.main",children:$})]}),i.jsx(C,{color:"success",sx:{fontSize:40}})]})})})}),i.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:i.jsx(ee,{children:i.jsx(te,{children:i.jsxs(r,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[i.jsxs(r,{children:[i.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Total Dépenses"}),i.jsx(o,{variant:"h6",color:"error",fontWeight:"medium",children:_(X*((null==T?void 0:T.tauxChangeUSDCDF)||2800)).primary}),i.jsxs(o,{variant:"body2",color:"text.secondary",children:["≈ ",_(X*((null==T?void 0:T.tauxChangeUSDCDF)||2800)).secondary]})]}),i.jsx(f,{color:"error",sx:{fontSize:40}})]})})})}),i.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:i.jsx(ee,{children:i.jsx(te,{children:i.jsxs(r,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[i.jsxs(r,{children:[i.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Profit Après Dépenses"}),i.jsx(o,{variant:"h6",color:Q>=0?"success.main":"error",fontWeight:"medium",children:_(Q*((null==T?void 0:T.tauxChangeUSDCDF)||2800)).primary}),i.jsxs(o,{variant:"body2",color:"text.secondary",children:["≈ ",_(Q*((null==T?void 0:T.tauxChangeUSDCDF)||2800)).secondary]})]}),i.jsx(v,{color:Q>=0?"success":"error",sx:{fontSize:40}})]})})})})]}),i.jsx(c,{sx:{mb:3},children:i.jsxs(it,{value:e,onChange:(e,n)=>{t(n)},"aria-label":"reports tabs",children:[i.jsx(rt,{label:"Tendances",icon:i.jsx(ve,{})}),i.jsx(rt,{label:"Produits",icon:i.jsx(D,{})}),i.jsx(rt,{label:"Analyses",icon:i.jsx(w,{})})]})}),i.jsx(oa,{value:e,index:0,children:i.jsxs(ce,{container:!0,spacing:3,children:[i.jsx(ce,{item:!0,xs:12,md:8,children:i.jsxs(ee,{children:[i.jsx(ue,{title:"Évolution des Ventes (30 derniers jours)"}),i.jsx(te,{children:i.jsx(_t,{data:re,options:{responsive:!0,plugins:{legend:{position:"top"}},scales:{y:{beginAtZero:!0}}}})})]})}),i.jsx(ce,{item:!0,xs:12,md:4,children:i.jsxs(ee,{children:[i.jsx(ue,{title:"Méthodes de Paiement"}),i.jsx(te,{children:i.jsx(zt,{data:ae,options:{responsive:!0,plugins:{legend:{position:"bottom"}}}})})]})}),i.jsx(ce,{item:!0,xs:12,children:i.jsxs(ee,{children:[i.jsx(ue,{title:"Performance par Catégorie"}),i.jsx(te,{children:i.jsx($t,{data:se,options:{responsive:!0,plugins:{legend:{position:"top"}},scales:{y:{beginAtZero:!0}}}})})]})})]})}),i.jsx(oa,{value:e,index:1,children:i.jsxs(ee,{children:[i.jsx(ue,{title:"Top 10 Produits les Plus Vendus"}),i.jsx(te,{children:i.jsx(me,{children:i.jsxs(he,{children:[i.jsx(pe,{children:i.jsxs(xe,{children:[i.jsx(ge,{children:"Rang"}),i.jsx(ge,{children:"Produit"}),i.jsx(ge,{align:"right",children:"Quantité Vendue"}),i.jsx(ge,{align:"right",children:"Profit"}),i.jsx(ge,{align:"right",children:"Profit Moyen"})]})}),i.jsx(ye,{children:G.map((e,t)=>i.jsxs(xe,{children:[i.jsx(ge,{children:i.jsx(M,{label:`#${t+1}`,color:t<3?"primary":"default",size:"small"})}),i.jsx(ge,{children:e.nom}),i.jsx(ge,{align:"right",children:e.quantite}),i.jsx(ge,{align:"right",children:B(e.revenue,"USD")}),i.jsx(ge,{align:"right",children:B(e.revenue/e.quantite,"USD")})]},t))})]})})})]})}),i.jsx(oa,{value:e,index:2,children:i.jsxs(ce,{container:!0,spacing:3,children:[i.jsx(ce,{item:!0,xs:12,md:6,children:i.jsxs(ee,{children:[i.jsx(ue,{title:"Métriques de Performance"}),i.jsx(te,{children:i.jsxs(ce,{container:!0,spacing:2,children:[i.jsxs(ce,{item:!0,xs:6,children:[i.jsx(o,{variant:"body2",color:"text.secondary",children:"Vente Moyenne"}),i.jsx(o,{variant:"h6",children:B(W,"USD")})]}),i.jsxs(ce,{item:!0,xs:6,children:[i.jsx(o,{variant:"body2",color:"text.secondary",children:"Produits Vendus"}),i.jsx(o,{variant:"h6",children:E.reduce((e,t)=>e+t.produits.reduce((e,t)=>e+t.quantite,0),0)})]}),i.jsxs(ce,{item:!0,xs:6,children:[i.jsx(o,{variant:"body2",color:"text.secondary",children:"Panier Moyen"}),i.jsxs(o,{variant:"h6",children:[$>0?(E.reduce((e,t)=>e+t.produits.length,0)/$).toFixed(1):"0"," articles"]})]})]})})]})}),i.jsx(ce,{item:!0,xs:12,md:6,children:i.jsxs(ee,{children:[i.jsx(ue,{title:"Répartition des Profits"}),i.jsx(te,{children:i.jsxs(ce,{container:!0,spacing:2,children:[i.jsxs(ce,{item:!0,xs:12,children:[i.jsx(o,{variant:"body2",color:"text.secondary",children:"Ventes Cash"}),i.jsx(o,{variant:"h6",color:"success.main",children:B(E.filter(e=>"cash"===e.typeVente).reduce((e,t)=>e+t.totalCDF,0),"CDF")})]}),i.jsxs(ce,{item:!0,xs:12,children:[i.jsx(o,{variant:"body2",color:"text.secondary",children:"Ventes à Crédit"}),i.jsx(o,{variant:"h6",color:"warning.main",children:B(E.filter(e=>"credit"===e.typeVente).reduce((e,t)=>e+t.totalCDF,0),"CDF")})]}),i.jsx(h,{sx:{width:"100%",my:1}}),i.jsxs(ce,{item:!0,xs:12,children:[i.jsx(o,{variant:"body2",color:"text.secondary",children:"Dettes Impayées"}),i.jsx(o,{variant:"h6",color:"error",children:B(y.filter(e=>"paid"!==e.statut).reduce((e,t)=>e+t.montantRestantCDF,0),"CDF")})]})]})})]})}),i.jsx(ce,{item:!0,xs:12,children:i.jsxs(ee,{children:[i.jsx(ue,{title:"Analyse des Dépenses par Catégorie"}),i.jsx(te,{children:i.jsx(me,{children:i.jsxs(he,{children:[i.jsx(pe,{children:i.jsxs(xe,{children:[i.jsx(ge,{children:"Catégorie"}),i.jsx(ge,{align:"right",children:"Nombre"}),i.jsx(ge,{align:"right",children:"Montant Total"}),i.jsx(ge,{align:"right",children:"Montant Moyen"}),i.jsx(ge,{align:"right",children:"% du Total"})]})}),i.jsx(ye,{children:Object.entries(F.reduce((e,t)=>(e[t.categorie]||(e[t.categorie]={count:0,total:0}),e[t.categorie].count+=1,e[t.categorie].total+=t.montantCDF,e),{})).sort(([,e],[,t])=>t.total-e.total).map(([e,t])=>i.jsxs(xe,{children:[i.jsx(ge,{children:e}),i.jsx(ge,{align:"right",children:t.count}),i.jsx(ge,{align:"right",children:B(t.total,"USD")}),i.jsx(ge,{align:"right",children:B(t.total/t.count,"USD")}),i.jsx(ge,{align:"right",children:X>0?`${(t.total/X*100).toFixed(1)}%`:"0%"})]},e))})]})})})]})})]})})]})},ca=()=>{const[e,t]=vt.useState([]),[n,a]=vt.useState([]),[s,d]=vt.useState(""),[u,m]=vt.useState(""),[h,p]=vt.useState(""),[x,g]=vt.useState(0),[y,j]=vt.useState(10),[D,C]=vt.useState(!1),[v,S]=vt.useState(null),[b,f]=vt.useState(!1),[w,P]=vt.useState({nom:"",email:"",role:"employee",motDePasse:"",actif:!0}),[T,k]=vt.useState(""),[A,R]=vt.useState(""),N=mr.getUserPermissions(),L=mr.getCurrentUser();vt.useEffect(()=>{V()},[]),vt.useEffect(()=>{B()},[e,s,u,h]),vt.useEffect(()=>{-1===y&&j(n.length||1)},[n.length,y]);const V=async()=>{const e=await ur.getUsers();t(e)},B=()=>{let t=e;if(s&&(t=t.filter(e=>e.nom.toLowerCase().includes(s.toLowerCase())||e.email.toLowerCase().includes(s.toLowerCase()))),u&&(t=t.filter(e=>e.role===u)),h){const e="active"===h;t=t.filter(t=>t.actif===e)}a(t)},_=e=>{switch(e){case"super_admin":return"Super Admin";case"admin":return"Administrateur";case"employee":return"Employé";default:return e}},z=e=>{switch(e){case"super_admin":return"error";case"admin":return"warning";case"employee":return"primary";default:return"default"}},$=e=>{switch(e){case"super_admin":case"admin":return i.jsx(st,{});default:return i.jsx(ot,{})}},W=e=>{e?(S(e),P({nom:e.nom,email:e.email,role:e.role,motDePasse:"",actif:e.actif})):(S(null),P({nom:"",email:"",role:"employee",motDePasse:"",actif:!0})),C(!0),f(!1),k(""),R("")},X=()=>{C(!1),S(null),k(""),R("")},Q=e.length,H=e.filter(e=>e.actif).length,G=e.filter(e=>"admin"===e.role||"super_admin"===e.role).length,K=e.filter(e=>"employee"===e.role).length;return i.jsxs(r,{children:[i.jsxs(r,{display:"flex",justifyContent:"space-between",alignItems:"center",mb:3,children:[i.jsx(o,{variant:"h4",children:"Gestion des Utilisateurs"}),N.canManageUsers&&i.jsx(I,{variant:"contained",startIcon:i.jsx(Ee,{}),onClick:()=>W(),children:"Nouvel Utilisateur"})]}),A&&i.jsx(O,{severity:"success",sx:{mb:2},onClose:()=>R(""),children:A}),T&&i.jsx(O,{severity:"error",sx:{mb:2},onClose:()=>k(""),children:T}),i.jsxs(ce,{container:!0,spacing:3,sx:{mb:3},children:[i.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:i.jsx(ee,{children:i.jsx(te,{children:i.jsxs(r,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[i.jsxs(r,{children:[i.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Total Utilisateurs"}),i.jsx(o,{variant:"h6",children:Q})]}),i.jsx(E,{color:"primary",sx:{fontSize:40}})]})})})}),i.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:i.jsx(ee,{children:i.jsx(te,{children:i.jsxs(r,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[i.jsxs(r,{children:[i.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Utilisateurs Actifs"}),i.jsx(o,{variant:"h6",color:"success.main",children:H})]}),i.jsx(at,{color:"success",sx:{fontSize:40}})]})})})}),i.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:i.jsx(ee,{children:i.jsx(te,{children:i.jsxs(r,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[i.jsxs(r,{children:[i.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Administrateurs"}),i.jsx(o,{variant:"h6",color:"warning.main",children:G})]}),i.jsx(st,{color:"warning",sx:{fontSize:40}})]})})})}),i.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:i.jsx(ee,{children:i.jsx(te,{children:i.jsxs(r,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[i.jsxs(r,{children:[i.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Employés"}),i.jsx(o,{variant:"h6",color:"info.main",children:K})]}),i.jsx(ot,{color:"info",sx:{fontSize:40}})]})})})})]}),i.jsx(c,{sx:{p:2,mb:3},children:i.jsxs(ce,{container:!0,spacing:2,alignItems:"center",children:[i.jsx(ce,{item:!0,xs:12,md:4,children:i.jsx(ne,{fullWidth:!0,placeholder:"Rechercher par nom ou email...",value:s,onChange:e=>d(e.target.value),InputProps:{startAdornment:i.jsx(ie,{position:"start",children:i.jsx(Pe,{})})}})}),i.jsx(ce,{item:!0,xs:12,md:3,children:i.jsxs(je,{fullWidth:!0,children:[i.jsx(De,{children:"Rôle"}),i.jsxs(Ce,{value:u,label:"Rôle",onChange:e=>m(e.target.value),children:[i.jsx(J,{value:"",children:"Tous les rôles"}),i.jsx(J,{value:"super_admin",children:"Super Admin"}),i.jsx(J,{value:"admin",children:"Administrateur"}),i.jsx(J,{value:"employee",children:"Employé"})]})]})}),i.jsx(ce,{item:!0,xs:12,md:3,children:i.jsxs(je,{fullWidth:!0,children:[i.jsx(De,{children:"Statut"}),i.jsxs(Ce,{value:h,label:"Statut",onChange:e=>p(e.target.value),children:[i.jsx(J,{value:"",children:"Tous les statuts"}),i.jsx(J,{value:"active",children:"Actif"}),i.jsx(J,{value:"inactive",children:"Inactif"})]})]})})]})}),i.jsxs(me,{component:c,children:[i.jsxs(he,{children:[i.jsx(pe,{children:i.jsxs(xe,{children:[i.jsx(ge,{children:"Utilisateur"}),i.jsx(ge,{children:"Email"}),i.jsx(ge,{align:"center",children:"Rôle"}),i.jsx(ge,{align:"center",children:"Statut"}),i.jsx(ge,{children:"Date de Création"}),N.canManageUsers&&i.jsx(ge,{align:"center",children:"Actions"})]})}),i.jsx(ye,{children:(-1===y?n:n.slice(x*y,x*y+y)).map(n=>i.jsxs(xe,{hover:!0,onClick:()=>W(n),sx:{cursor:"pointer"},children:[i.jsx(ge,{children:i.jsxs(r,{display:"flex",alignItems:"center",gap:2,children:[i.jsx(l,{sx:{bgcolor:z(n.role)},children:n.nom.charAt(0).toUpperCase()}),i.jsxs(r,{children:[i.jsx(o,{variant:"subtitle2",children:n.nom}),n.id===(null==L?void 0:L.id)&&i.jsx(M,{label:"Vous",size:"small",color:"primary"})]})]})}),i.jsx(ge,{children:n.email}),i.jsx(ge,{align:"center",children:i.jsx(M,{icon:$(n.role),label:_(n.role),color:z(n.role),size:"small"})}),i.jsx(ge,{align:"center",children:i.jsx(M,{label:n.actif?"Actif":"Inactif",color:n.actif?"success":"error",size:"small"})}),i.jsx(ge,{children:Ft(new Date(n.dateCreation),"dd/MM/yyyy",{locale:jr})}),N.canManageUsers&&i.jsx(ge,{align:"center",children:i.jsxs(r,{display:"flex",gap:1,children:[i.jsx(F,{title:"Modifier",children:i.jsx(U,{size:"small",onClick:()=>W(n),disabled:"super_admin"===n.role&&!mr.hasRole(["super_admin"]),children:i.jsx(ke,{fontSize:"small"})})}),i.jsx(F,{title:n.actif?"Désactiver":"Activer",children:i.jsx(U,{size:"small",onClick:()=>(async n=>{if(n.id===(null==L?void 0:L.id)&&n.actif)return k("Vous ne pouvez pas désactiver votre propre compte"),void setTimeout(()=>k(""),3e3);const i={...n,actif:!n.actif},r=e.map(e=>e.id===n.id?i:e);t(r),await ur.setUsers(r),R(`Utilisateur ${i.actif?"activé":"désactivé"} avec succès`),setTimeout(()=>R(""),3e3)})(n),disabled:n.id===(null==L?void 0:L.id)&&n.actif,children:n.actif?i.jsx(Y,{fontSize:"small"}):i.jsx(at,{fontSize:"small"})})}),mr.hasRole(["super_admin"])&&i.jsx(F,{title:"Supprimer",children:i.jsx(U,{size:"small",color:"error",onClick:()=>(async n=>{if(n.id===(null==L?void 0:L.id))return k("Vous ne pouvez pas supprimer votre propre compte"),void setTimeout(()=>k(""),3e3);if("super_admin"===n.role&&!mr.hasRole(["super_admin"]))return k("Seul un Super Admin peut supprimer un compte Super Admin"),void setTimeout(()=>k(""),3e3);if(window.confirm(`Êtes-vous sûr de vouloir supprimer l'utilisateur "${n.nom}" ?`)){const i=e.filter(e=>e.id!==n.id);t(i),await ur.setUsers(i),R("Utilisateur supprimé avec succès"),setTimeout(()=>R(""),3e3)}})(n),disabled:n.id===(null==L?void 0:L.id),children:i.jsx(q,{fontSize:"small"})})})]})})]},n.id))})]}),i.jsx(Ae,{rowsPerPageOptions:[5,10,25,50,100,{label:"Voir tout",value:-1}],component:"div",count:n.length,rowsPerPage:-1===y?n.length:y,page:-1===y?0:x,onPageChange:(e,t)=>{-1!==y&&g(t)},onRowsPerPageChange:e=>{const t=parseInt(e.target.value,10);j(t),g(0)},labelRowsPerPage:"Lignes par page:",labelDisplayedRows:({from:e,to:t,count:n})=>-1===y?`Affichage de tous les ${n} éléments`:`${e}-${t} sur ${-1!==n?n:`plus de ${t}`}`})]}),i.jsxs(Re,{open:D,onClose:X,maxWidth:"sm",fullWidth:!0,children:[i.jsx(Me,{children:v?"Modifier l'Utilisateur":"Nouvel Utilisateur"}),i.jsxs(Ie,{children:[T&&i.jsx(O,{severity:"error",sx:{mb:2},children:T}),A&&i.jsx(O,{severity:"success",sx:{mb:2},children:A}),i.jsxs(ce,{container:!0,spacing:2,sx:{mt:1},children:[i.jsx(ce,{item:!0,xs:12,children:i.jsx(ne,{fullWidth:!0,label:"Nom complet *",value:w.nom,onChange:e=>P({...w,nom:e.target.value})})}),i.jsx(ce,{item:!0,xs:12,children:i.jsx(ne,{fullWidth:!0,label:"Email *",type:"email",value:w.email,onChange:e=>P({...w,email:e.target.value})})}),i.jsx(ce,{item:!0,xs:12,children:i.jsxs(je,{fullWidth:!0,children:[i.jsx(De,{children:"Rôle *"}),i.jsxs(Ce,{value:w.role,label:"Rôle *",onChange:e=>P({...w,role:e.target.value}),disabled:!mr.hasRole(["super_admin"])||!(!v||v.id!==(null==L?void 0:L.id)),children:[i.jsx(J,{value:"employee",children:"Employé"}),i.jsx(J,{value:"admin",children:"Administrateur"}),mr.hasRole(["super_admin"])&&i.jsx(J,{value:"super_admin",children:"Super Admin"})]})]})}),i.jsx(ce,{item:!0,xs:12,children:i.jsx(ne,{fullWidth:!0,label:v?"Nouveau mot de passe (optionnel)":"Mot de passe *",type:b?"text":"password",value:w.motDePasse,onChange:e=>P({...w,motDePasse:e.target.value}),InputProps:{endAdornment:i.jsx(ie,{position:"end",children:i.jsx(U,{onClick:()=>f(!b),edge:"end",children:b?i.jsx(ae,{}):i.jsx(se,{})})})},helperText:v?"Laissez vide pour conserver le mot de passe actuel":"Minimum 6 caractères"})}),i.jsx(ce,{item:!0,xs:12,children:i.jsx(We,{control:i.jsx(Ye,{checked:w.actif,onChange:e=>P({...w,actif:e.target.checked}),disabled:!(!v||v.id!==(null==L?void 0:L.id))}),label:"Compte actif"})})]})]}),i.jsxs(Ne,{children:[i.jsx(I,{onClick:X,children:"Annuler"}),i.jsx(I,{onClick:async()=>{if(!w.nom.trim())return void k("Le nom est requis");if(!w.email.trim())return void k("L'email est requis");if(!v&&!w.motDePasse.trim())return void k("Le mot de passe est requis pour un nouvel utilisateur");if(w.motDePasse&&w.motDePasse.length<6)return void k("Le mot de passe doit contenir au moins 6 caractères");if(e.some(e=>e.email.toLowerCase()===w.email.trim().toLowerCase()&&e.id!==(null==v?void 0:v.id)))return void k("Un utilisateur avec cet email existe déjà");if("super_admin"===w.role&&!mr.hasRole(["super_admin"]))return void k("Seul un Super Admin peut créer ou modifier un compte Super Admin");if(v&&v.id===(null==L?void 0:L.id)&&w.role!==v.role)return void k("Vous ne pouvez pas modifier votre propre rôle");if(v&&v.id===(null==L?void 0:L.id)&&!w.actif)return void k("Vous ne pouvez pas désactiver votre propre compte");const n=(new Date).toISOString();if(v){const n={...v,nom:w.nom.trim(),email:w.email.trim(),role:w.role,actif:w.actif,...w.motDePasse&&{motDePasse:w.motDePasse}},i=e.map(e=>e.id===v.id?n:e);t(i),await ur.setUsers(i),v.id===(null==L?void 0:L.id)&&await ur.setCurrentUser(n),R("Utilisateur mis à jour avec succès")}else{const i={id:Date.now().toString(),nom:w.nom.trim(),email:w.email.trim(),role:w.role,motDePasse:w.motDePasse,dateCreation:n,actif:w.actif},r=[...e,i];t(r),await ur.setUsers(r),R("Utilisateur créé avec succès")}setTimeout(()=>{X()},1500)},variant:"contained",children:v?"Mettre à jour":"Créer"})]})]})]})};class da{async exportAllData(){try{const[e,t,n,i,r,a]=await Promise.all([ur.getProducts(),ur.getUsers(),ur.getSales(),ur.getDebts(),ur.getExpenses(),ur.getSettings()]),s={exportDate:(new Date).toISOString(),products:Li.arrayToCSV(e,Oi),users:Li.arrayToCSV(t,Vi),sales:Li.arrayToCSV(n,qi),debts:Li.arrayToCSV(i,Bi),expenses:Li.arrayToCSV(r,_i),settings:Li.arrayToCSV(Xi(a),Wi)},o=`SmartBoutique - Sauvegarde Complète\nDate d'exportation: ${s.exportDate}\n\n=== PRODUITS ===\n${s.products}\n\n=== UTILISATEURS ===\n${s.users}\n\n=== VENTES ===\n${s.sales}\n\n=== DETTES ===\n${s.debts}\n\n=== DÉPENSES ===\n${s.expenses}\n\n=== PARAMÈTRES ===\n${s.settings}\n`;return{success:!0,message:"Exportation complète réussie (compatible Excel)",data:"\ufeff"+o}}catch(ba){return console.error("Erreur lors de l'exportation complète:",ba),{success:!1,message:"Erreur lors de l'exportation: "+ba.message}}}async exportData(e){try{let t=[],n=[],i="";switch(e){case"products":t=await ur.getProducts(),n=Oi,i="produits";break;case"users":t=await ur.getUsers(),n=Vi,i="utilisateurs";break;case"sales":t=await ur.getSales(),n=qi,i="ventes";break;case"debts":t=await ur.getDebts(),n=Bi,i="dettes";break;case"expenses":t=await ur.getExpenses(),n=_i,i="depenses"}const r=Li.arrayToCSV(t,n);return{success:!0,message:`Exportation ${i} réussie (${t.length} enregistrements)`,data:r}}catch(ba){return console.error(`Erreur lors de l'exportation ${e}:`,ba),{success:!1,message:"Erreur lors de l'exportation: "+ba.message}}}async importProducts(e,t=!1){try{const n=Li.csvToArray(e,Oi),i=Li.validateCSVData(n,Oi);if(!i.isValid)return{success:!1,message:"Données invalides détectées",errors:i.errors,importedCount:0};let r=n;if(!t){const e=await ur.getProducts(),t=new Set(e.map(e=>e.id)),i=n.filter(e=>!t.has(e.id));r=[...e,...i]}return await ur.setProducts(r),{success:!0,message:`${n.length} produits importés avec succès`,errors:[],importedCount:n.length}}catch(ba){return console.error("Erreur lors de l'importation des produits:",ba),{success:!1,message:"Erreur lors de l'importation: "+ba.message,errors:[ba.message],importedCount:0}}}async importUsers(e,t=!1){try{const n=Li.csvToArray(e,Vi),i=Li.validateCSVData(n,Vi);if(!i.isValid)return{success:!1,message:"Données invalides détectées",errors:i.errors,importedCount:0};let r=n;if(!t){const e=await ur.getUsers(),t=new Set(e.map(e=>e.id)),i=n.filter(e=>!t.has(e.id));r=[...e,...i]}return await ur.setUsers(r),{success:!0,message:`${n.length} utilisateurs importés avec succès`,errors:[],importedCount:n.length}}catch(ba){return console.error("Erreur lors de l'importation des utilisateurs:",ba),{success:!1,message:"Erreur lors de l'importation: "+ba.message,errors:[ba.message],importedCount:0}}}generateTemplate(e){switch(e){case"products":return Li.generateTemplate(Oi);case"users":return Li.generateTemplate(Vi);case"sales":return Li.generateTemplate(qi);case"debts":return Li.generateTemplate(Bi);case"expenses":return Li.generateTemplate(_i);default:return""}}async createAutomaticBackup(){try{const e=await this.exportAllData();if(e.success&&e.data){const t=(new Date).toISOString().replace(/[:.]/g,"-");return{success:!0,message:`Sauvegarde automatique créée: ${t}`,data:e.data}}return e}catch(ba){return console.error("Erreur lors de la sauvegarde automatique:",ba),{success:!1,message:"Erreur lors de la sauvegarde automatique: "+ba.message}}}getSampleCSVData(e){switch(e){case"products":return"ID,Nom du Produit,Description,Prix CDF,Prix USD,Code QR,Catégorie,Stock,Stock Minimum,Code Barres,Date de Création,Date de Modification\nSAMPLE1,Produit Exemple,Description du produit exemple,5600,2,SAMPLE123,Alimentation,50,10,1234567890,2024-01-01,2024-01-01";case"users":return"ID,Nom,Email,Rôle,Mot de Passe,Date de Création,Actif\nSAMPLE1,Utilisateur Exemple,<EMAIL>,employee,motdepasse123,2024-01-01,Oui";default:return this.generateTemplate(e)}}}const ua=new da,ma=Object.freeze(Object.defineProperty({__proto__:null,CSVImportExportService:da,csvImportExportService:ua},Symbol.toStringTag,{value:"Module"})),ha=({onSuccess:e,onError:t})=>{var n,a,s,l;const[c,d]=vt.useState(!1),[u,m]=vt.useState(!1),[p,x]=vt.useState(!1),[g,y]=vt.useState("products"),[j,D]=vt.useState(""),[C,v]=vt.useState(!1),[S,b]=vt.useState(""),f=[{key:"products",label:"Produits",icon:"📦"},{key:"users",label:"Utilisateurs",icon:"👥"},{key:"sales",label:"Ventes",icon:"💰"},{key:"debts",label:"Dettes",icon:"📋"},{key:"expenses",label:"Dépenses",icon:"💸"}],w=()=>{const e=ua.generateTemplate(g);D(e)};return i.jsx(ee,{children:i.jsxs(te,{children:[i.jsxs(o,{variant:"h6",gutterBottom:!0,children:[i.jsx(lt,{sx:{mr:1,verticalAlign:"middle"}}),"Gestion des Données CSV"]}),i.jsx(o,{variant:"body2",color:"text.secondary",paragraph:!0,children:"Exportez et importez vos données au format CSV pour une meilleure portabilité et accessibilité."}),i.jsxs(ce,{container:!0,spacing:2,children:[i.jsx(ce,{item:!0,xs:12,md:6,children:i.jsxs(r,{sx:{p:2,border:"1px solid #e0e0e0",borderRadius:1},children:[i.jsxs(o,{variant:"subtitle1",gutterBottom:!0,children:[i.jsx(Fe,{sx:{mr:1,verticalAlign:"middle"}}),"Exportation"]}),i.jsx(I,{variant:"contained",color:"primary",fullWidth:!0,onClick:async()=>{d(!0);try{const n=await ua.exportAllData();if(n.success&&n.data){const t=new Blob([n.data],{type:"text/plain;charset=utf-8"}),i=URL.createObjectURL(t),r=document.createElement("a");r.href=i,r.download=`SmartBoutique_Backup_${(new Date).toISOString().split("T")[0]}.txt`,document.body.appendChild(r),r.click(),document.body.removeChild(r),URL.revokeObjectURL(i),null==e||e("Sauvegarde complète exportée avec succès (compatible Excel)")}else null==t||t(n.message)}catch(ba){null==t||t("Erreur lors de l'exportation: "+ba.message)}finally{d(!1)}},disabled:c,startIcon:c?i.jsx(Ve,{size:20}):i.jsx(ct,{}),sx:{mb:2},children:"Exporter Toutes les Données"}),i.jsx(h,{sx:{my:2}}),i.jsx(o,{variant:"body2",gutterBottom:!0,children:"Exporter un type de données spécifique:"}),i.jsx(r,{sx:{mb:2},children:f.map(e=>i.jsx(M,{label:`${e.icon} ${e.label}`,onClick:()=>y(e.key),color:g===e.key?"primary":"default",sx:{m:.5}},e.key))}),i.jsxs(I,{variant:"outlined",fullWidth:!0,onClick:async()=>{d(!0);try{const n=await ua.exportData(g);n.success&&n.data?(b(n.data),m(!0),null==e||e(n.message)):null==t||t(n.message)}catch(ba){null==t||t("Erreur lors de l'exportation: "+ba.message)}finally{d(!1)}},disabled:c,startIcon:i.jsx(dt,{}),children:["Exporter ",null==(n=f.find(e=>e.key===g))?void 0:n.label]})]})}),i.jsx(ce,{item:!0,xs:12,md:6,children:i.jsxs(r,{sx:{p:2,border:"1px solid #e0e0e0",borderRadius:1},children:[i.jsxs(o,{variant:"subtitle1",gutterBottom:!0,children:[i.jsx(Ue,{sx:{mr:1,verticalAlign:"middle"}}),"Importation"]}),i.jsx(o,{variant:"body2",color:"text.secondary",gutterBottom:!0,children:"Sélectionnez le type de données à importer:"}),i.jsx(r,{sx:{mb:2},children:f.filter(e=>["products","users"].includes(e.key)).map(e=>i.jsx(M,{label:`${e.icon} ${e.label}`,onClick:()=>y(e.key),color:g===e.key?"primary":"default",sx:{m:.5}},e.key))}),i.jsxs(I,{variant:"outlined",fullWidth:!0,onClick:()=>x(!0),startIcon:i.jsx(Ue,{}),sx:{mb:1},children:["Importer ",null==(a=f.find(e=>e.key===g))?void 0:a.label]}),i.jsx(I,{variant:"text",size:"small",fullWidth:!0,onClick:w,children:"Obtenir un modèle CSV"})]})})]}),i.jsxs(Re,{open:u,onClose:()=>m(!1),maxWidth:"md",fullWidth:!0,children:[i.jsxs(Me,{children:["Données Exportées - ",null==(s=f.find(e=>e.key===g))?void 0:s.label]}),i.jsx(Ie,{children:i.jsx(ne,{multiline:!0,rows:10,fullWidth:!0,value:S,variant:"outlined",InputProps:{readOnly:!0},sx:{fontFamily:"monospace"}})}),i.jsxs(Ne,{children:[i.jsx(I,{onClick:()=>m(!1),children:"Fermer"}),i.jsx(I,{onClick:()=>{var e;const t=(null==(e=f.find(e=>e.key===g))?void 0:e.label)||g;let n=S;n.startsWith("\ufeff")||(n="\ufeff"+n);const i=new Blob([n],{type:"text/csv;charset=utf-8"}),r=URL.createObjectURL(i),a=document.createElement("a");a.href=r,a.download=`SmartBoutique_${t}_${(new Date).toISOString().split("T")[0]}.csv`,document.body.appendChild(a),a.click(),document.body.removeChild(a),URL.revokeObjectURL(r),m(!1)},variant:"contained",startIcon:i.jsx(Fe,{}),children:"Télécharger CSV"})]})]}),i.jsxs(Re,{open:p,onClose:()=>x(!1),maxWidth:"md",fullWidth:!0,children:[i.jsxs(Me,{children:["Importer ",null==(l=f.find(e=>e.key===g))?void 0:l.label]}),i.jsxs(Ie,{children:[i.jsx(O,{severity:"info",sx:{mb:2},children:'Collez le contenu CSV ci-dessous. Utilisez "Obtenir un modèle CSV" pour voir le format requis.'}),i.jsx(ne,{multiline:!0,rows:8,fullWidth:!0,value:j,onChange:e=>D(e.target.value),placeholder:"Collez votre contenu CSV ici...",variant:"outlined",sx:{mb:2,fontFamily:"monospace"}}),i.jsx(We,{control:i.jsx(Qe,{checked:C,onChange:e=>v(e.target.checked)}),label:"Remplacer les données existantes"})]}),i.jsxs(Ne,{children:[i.jsx(I,{onClick:()=>x(!1),children:"Annuler"}),i.jsx(I,{onClick:w,variant:"outlined",children:"Obtenir Modèle"}),i.jsx(I,{onClick:async()=>{if(j.trim()){d(!0);try{let n;switch(g){case"products":n=await ua.importProducts(j,C);break;case"users":n=await ua.importUsers(j,C);break;default:return void(null==t||t("Type de données non supporté pour l'importation"))}n.success?(null==e||e(n.message),x(!1),D("")):null==t||t(n.message+"\n"+n.errors.join("\n"))}catch(ba){null==t||t("Erreur lors de l'importation: "+ba.message)}finally{d(!1)}}else null==t||t("Veuillez saisir le contenu CSV à importer")},variant:"contained",disabled:c||!j.trim(),startIcon:c?i.jsx(Ve,{size:20}):i.jsx(Ue,{}),children:"Importer"})]})]})]})})},pa=({value:e="",onChange:t,disabled:n=!1,maxSizeKB:a=500,acceptedFormats:s=["image/jpeg","image/jpg","image/png","image/gif"]})=>{const[l,c]=vt.useState(""),[d,u]=vt.useState(!1),m=vt.useRef(null),h=()=>{m.current&&m.current.click()};return i.jsxs(r,{children:[i.jsx(o,{variant:"subtitle2",gutterBottom:!0,children:"Logo de l'entreprise"}),i.jsx("input",{ref:m,type:"file",accept:s.join(","),onChange:e=>{var n;const i=null==(n=e.target.files)?void 0:n[0];if(!i)return;if(c(""),u(!0),!s.includes(i.type))return c(`Format non supporté. Formats acceptés: ${s.map(e=>e.split("/")[1].toUpperCase()).join(", ")}`),void u(!1);if(i.size/1024>a)return c(`Fichier trop volumineux. Taille maximale: ${a}KB`),void u(!1);const r=new FileReader;r.onload=e=>{var n;const i=null==(n=e.target)?void 0:n.result;i&&t(i),u(!1)},r.onerror=()=>{c("Erreur lors de la lecture du fichier"),u(!1)},r.readAsDataURL(i)},style:{display:"none"},disabled:n}),i.jsx(ee,{variant:"outlined",sx:{mb:2},children:i.jsx(te,{sx:{textAlign:"center",py:3},children:e?i.jsxs(r,{children:[i.jsx(r,{component:"img",src:e,alt:"Logo de l'entreprise",sx:{maxWidth:"200px",maxHeight:"100px",objectFit:"contain",border:"1px solid #e0e0e0",borderRadius:1,mb:2}}),i.jsxs(r,{children:[i.jsx(I,{variant:"outlined",startIcon:i.jsx(ut,{}),onClick:h,disabled:n||d,sx:{mr:1},children:"Changer"}),i.jsx(U,{color:"error",onClick:()=>{t(""),c(""),m.current&&(m.current.value="")},disabled:n||d,title:"Supprimer le logo",children:i.jsx(q,{})})]})]}):i.jsxs(r,{children:[i.jsx(mt,{sx:{fontSize:48,color:"text.secondary",mb:2}}),i.jsx(o,{variant:"body2",color:"text.secondary",gutterBottom:!0,children:"Aucun logo configuré"}),i.jsx(I,{variant:"contained",startIcon:d?i.jsx(Ve,{size:20}):i.jsx(ut,{}),onClick:h,disabled:n||d,children:d?"Chargement...":"Télécharger un logo"})]})})}),l&&i.jsx(O,{severity:"error",sx:{mb:2},children:l}),i.jsxs(o,{variant:"caption",color:"text.secondary",children:["Formats acceptés: ",s.map(e=>e.split("/")[1].toUpperCase()).join(", ")," • Taille maximale: ",a,"KB • Recommandé: 200x100px pour un affichage optimal sur les reçus thermiques"]})]})};function xa(e){const{children:t,value:n,index:a,...s}=e;return i.jsx("div",{role:"tabpanel",hidden:n!==a,id:`settings-tabpanel-${a}`,"aria-labelledby":`settings-tab-${a}`,...s,children:n===a&&i.jsx(r,{sx:{p:3},children:t})})}const ga=()=>{const[e,t]=vt.useState(null),[n,a]=vt.useState(0),[s,l]=vt.useState(""),[d,u]=vt.useState(""),[m,g]=vt.useState({nom:"",adresse:"",telephone:"",email:"",rccm:"",idNat:"",logo:""}),[j,D]=vt.useState({tauxChangeUSDCDF:2800,seuilStockBas:10}),[C,v]=vt.useState({impressionAutomatique:!1,taillePapier:"thermal"}),[S,b]=vt.useState([]),[f,w]=vt.useState(!1),[E,P]=vt.useState(null),[F,T]=vt.useState({nom:"",description:"",couleur:"#2196F3"}),k=mr.getUserPermissions();vt.useEffect(()=>{A()},[]);const A=async()=>{var e,n,i,r,a,s,o,l,c,d,u,m,h,p,x,y;const j=await ur.getSettings();t(j),g({nom:(null==(e=null==j?void 0:j.company)?void 0:e.nom)||(null==(n=null==j?void 0:j.entreprise)?void 0:n.nom)||"SmartBoutique",adresse:(null==(i=null==j?void 0:j.company)?void 0:i.adresse)||(null==(r=null==j?void 0:j.entreprise)?void 0:r.adresse)||"Kinshasa, RDC",telephone:(null==(a=null==j?void 0:j.company)?void 0:a.telephone)||(null==(s=null==j?void 0:j.entreprise)?void 0:s.telephone)||"+*********** 000",email:(null==(o=null==j?void 0:j.company)?void 0:o.email)||(null==(l=null==j?void 0:j.entreprise)?void 0:l.email)||"<EMAIL>",rccm:(null==(c=null==j?void 0:j.company)?void 0:c.rccm)||(null==(d=null==j?void 0:j.entreprise)?void 0:d.rccm)||"",idNat:(null==(u=null==j?void 0:j.company)?void 0:u.idNat)||(null==(m=null==j?void 0:j.entreprise)?void 0:m.idNat)||"",logo:(null==(h=null==j?void 0:j.company)?void 0:h.logo)||(null==(p=null==j?void 0:j.entreprise)?void 0:p.logo)||""}),D({tauxChangeUSDCDF:(null==j?void 0:j.tauxChangeUSDCDF)||2800,seuilStockBas:(null==j?void 0:j.seuilStockBas)||10}),v({impressionAutomatique:(null==(x=null==j?void 0:j.impression)?void 0:x.impressionAutomatique)||!1,taillePapier:(null==(y=null==j?void 0:j.impression)?void 0:y.taillePapier)||"thermal"}),b((null==j?void 0:j.categories)||[{id:"1",nom:"Alimentation",couleur:"#4CAF50"},{id:"2",nom:"Vêtements",couleur:"#2196F3"},{id:"3",nom:"Électronique",couleur:"#FF9800"},{id:"4",nom:"Boissons",couleur:"#9C27B0"},{id:"5",nom:"Épicerie",couleur:"#795548"},{id:"6",nom:"Livres",couleur:"#607D8B"}])},R=e=>{e?(P(e),T({nom:e.nom,description:e.description,couleur:e.couleur})):(P(null),T({nom:"",description:"",couleur:"#2196F3"})),w(!0),u("")},N=()=>{w(!1),P(null),u("")},L=async()=>{try{console.log("🔄 Initializing essential user accounts only...");const e=[{id:"1",nom:"Super Admin",email:"<EMAIL>",role:"super_admin",motDePasse:"admin123",dateCreation:(new Date).toISOString(),actif:!0},{id:"2",nom:"Gestionnaire",email:"<EMAIL>",role:"admin",motDePasse:"manager123",dateCreation:(new Date).toISOString(),actif:!0},{id:"3",nom:"Employé",email:"<EMAIL>",role:"employee",motDePasse:"employee123",dateCreation:(new Date).toISOString(),actif:!0}];await ur.setUsers(e),await ur.setProducts([]),await ur.setSales([]),await ur.setDebts([]),await ur.setExpenses([]),await ur.setEmployeePayments([]);const t={tauxChangeUSDCDF:2800,seuilStockBas:10,categories:[{id:"1",nom:"Alimentation",couleur:"#4CAF50"},{id:"2",nom:"Vêtements",couleur:"#2196F3"},{id:"3",nom:"Électronique",couleur:"#FF9800"},{id:"4",nom:"Boissons",couleur:"#9C27B0"},{id:"5",nom:"Épicerie",couleur:"#795548"},{id:"6",nom:"Livres",couleur:"#607D8B"}],company:{nom:"SmartBoutique",adresse:"Kinshasa, RDC",telephone:"+*********** 000",email:"<EMAIL>",rccm:"",idNat:"",logo:""}};await ur.setSettings(t),console.log("✅ Essential user accounts initialized successfully (no demo data)")}catch(e){throw console.error("❌ Error initializing essential users:",e),e}},B=async()=>{try{(()=>{var e,t;return"capacitor:"===window.location.protocol||(null==(t=null==(e=window.Capacitor)?void 0:e.isNativePlatform)?void 0:t.call(e))||navigator.userAgent.includes("Capacitor")})()?await _():await z(),console.log("✅ Complete data reset performed successfully")}catch(e){throw console.error("❌ Error during data reset:",e),e}},_=async()=>{try{const{Preferences:t}=await Si(async()=>{const{Preferences:e}=await Promise.resolve().then(()=>Hi);return{Preferences:e}},void 0,import.meta.url),{keys:n}=await t.keys(),i=n.filter(e=>e.startsWith("smartboutique_")||e.startsWith("smartboutique_csv_"));for(const e of i)await t.remove({key:e});try{const{mobileSQLiteStorageService:e}=await Si(async()=>{const{mobileSQLiteStorageService:e}=await Promise.resolve().then(()=>cr);return{mobileSQLiteStorageService:e}},void 0,import.meta.url);console.log("Mobile SQLite data cleared (if available)")}catch(e){console.log("Mobile SQLite not available or already cleared")}console.log("✅ Mobile data cleared successfully")}catch(e){throw console.error("❌ Error clearing mobile data:",e),e}},z=async()=>{try{Object.keys(localStorage).forEach(e=>{e.startsWith("smartboutique_")&&localStorage.removeItem(e)});try{const{sqliteStorageService:e}=await Si(async()=>{const{sqliteStorageService:e}=await Promise.resolve().then(()=>ir);return{sqliteStorageService:e}},void 0,import.meta.url);console.log("Desktop SQLite data cleared (if available)")}catch(e){console.log("Desktop SQLite not available or already cleared")}console.log("✅ Desktop data cleared successfully")}catch(e){throw console.error("❌ Error clearing desktop data:",e),e}};return e?i.jsxs(r,{children:[i.jsx(o,{variant:"h4",gutterBottom:!0,children:"Paramètres"}),s&&i.jsx(O,{severity:"success",sx:{mb:2},onClose:()=>l(""),children:s}),d&&i.jsx(O,{severity:"error",sx:{mb:2},onClose:()=>u(""),children:d}),i.jsx(c,{sx:{mb:3},children:i.jsxs(it,{value:n,onChange:(e,t)=>{a(t)},"aria-label":"settings tabs",children:[i.jsx(rt,{label:"Entreprise",icon:i.jsx(ht,{})}),i.jsx(rt,{label:"Général",icon:i.jsx(de,{})}),i.jsx(rt,{label:"Impression",icon:i.jsx(qe,{})}),i.jsx(rt,{label:"Catégories",icon:i.jsx(tt,{})}),i.jsx(rt,{label:"Sauvegarde",icon:i.jsx(ct,{})}),i.jsx(rt,{label:"Données CSV",icon:i.jsx(Fe,{})})]})}),i.jsx(xa,{value:n,index:0,children:i.jsxs(ee,{children:[i.jsx(ue,{title:"Informations de l'Entreprise",avatar:i.jsx(ht,{})}),i.jsx(te,{children:i.jsxs(ce,{container:!0,spacing:3,children:[i.jsx(ce,{item:!0,xs:12,md:6,children:i.jsx(ne,{fullWidth:!0,label:"Nom de l'entreprise",value:m.nom,onChange:e=>g({...m,nom:e.target.value}),disabled:!k.canManageSettings})}),i.jsx(ce,{item:!0,xs:12,md:6,children:i.jsx(ne,{fullWidth:!0,label:"Email",type:"email",value:m.email,onChange:e=>g({...m,email:e.target.value}),disabled:!k.canManageSettings})}),i.jsx(ce,{item:!0,xs:12,md:6,children:i.jsx(ne,{fullWidth:!0,label:"Téléphone",value:m.telephone,onChange:e=>g({...m,telephone:e.target.value}),disabled:!k.canManageSettings})}),i.jsx(ce,{item:!0,xs:12,md:6,children:i.jsx(ne,{fullWidth:!0,label:"Adresse",value:m.adresse,onChange:e=>g({...m,adresse:e.target.value}),disabled:!k.canManageSettings})}),i.jsx(ce,{item:!0,xs:12,md:6,children:i.jsx(ne,{fullWidth:!0,label:"RCCM",value:m.rccm||"",onChange:e=>g({...m,rccm:e.target.value}),disabled:!k.canManageSettings,helperText:"Registre de Commerce et du Crédit Mobilier"})}),i.jsx(ce,{item:!0,xs:12,md:6,children:i.jsx(ne,{fullWidth:!0,label:"ID NAT",value:m.idNat||"",onChange:e=>g({...m,idNat:e.target.value}),disabled:!k.canManageSettings,helperText:"Identification Nationale"})}),i.jsx(ce,{item:!0,xs:12,children:i.jsx(pa,{value:m.logo||"",onChange:e=>g({...m,logo:e}),disabled:!k.canManageSettings})}),k.canManageSettings&&i.jsx(ce,{item:!0,xs:12,children:i.jsx(I,{variant:"contained",startIcon:i.jsx(pt,{}),onClick:async()=>{if(!e)return;const n={...e,entreprise:m};await ur.setSettings(n),t(n),l("Paramètres de l'entreprise sauvegardés avec succès"),setTimeout(()=>l(""),3e3)},children:"Sauvegarder"})})]})})]})}),i.jsx(xa,{value:n,index:1,children:i.jsxs(ee,{children:[i.jsx(ue,{title:"Paramètres Généraux",avatar:i.jsx(de,{})}),i.jsx(te,{children:i.jsxs(ce,{container:!0,spacing:3,children:[i.jsx(ce,{item:!0,xs:12,children:i.jsx(Gr,{label:"Taux de change (1 USD = ? CDF)",value:j.tauxChangeUSDCDF,onChange:e=>D({...j,tauxChangeUSDCDF:e}),min:1e3,max:1e4,step:10,exchangeRate:j.tauxChangeUSDCDF,disabled:!k.canManageSettings,allowUSDInput:!1,helperText:"Définit le taux de conversion entre USD et CDF"})}),i.jsx(ce,{item:!0,xs:12,md:6,children:i.jsx(ne,{fullWidth:!0,label:"Seuil de stock bas",type:"text",value:j.seuilStockBas,onChange:e=>{const t=e.target.value.replace(/[^0-9]/g,"");D({...j,seuilStockBas:parseInt(t)||0})},disabled:!k.canManageSettings,helperText:"Alerte quand le stock est ≤ à cette valeur",inputProps:{inputMode:"numeric",pattern:"[0-9]*"},sx:{"& input[type=number]":{MozAppearance:"textfield","&::-webkit-outer-spin-button":{WebkitAppearance:"none",margin:0},"&::-webkit-inner-spin-button":{WebkitAppearance:"none",margin:0}}}})}),k.canManageSettings&&i.jsx(ce,{item:!0,xs:12,children:i.jsx(I,{variant:"contained",startIcon:i.jsx(pt,{}),onClick:async()=>{if(!e)return;if(j.tauxChangeUSDCDF<=0)return void u("Le taux de change doit être supérieur à 0");if(j.seuilStockBas<0)return void u("Le seuil de stock bas ne peut pas être négatif");const n={...e,tauxChangeUSDCDF:j.tauxChangeUSDCDF,seuilStockBas:j.seuilStockBas};await ur.setSettings(n),t(n),l("Paramètres généraux sauvegardés avec succès"),setTimeout(()=>l(""),3e3)},children:"Sauvegarder"})})]})})]})}),i.jsx(xa,{value:n,index:2,children:i.jsxs(ee,{children:[i.jsx(ue,{title:"Paramètres d'Impression",avatar:i.jsx(qe,{})}),i.jsx(te,{children:i.jsxs(ce,{container:!0,spacing:3,children:[i.jsxs(ce,{item:!0,xs:12,children:[i.jsx(We,{control:i.jsx(Ye,{checked:C.impressionAutomatique,onChange:e=>v({...C,impressionAutomatique:e.target.checked}),disabled:!k.canManageSettings}),label:"Impression automatique des reçus"}),i.jsx(o,{variant:"body2",color:"text.secondary",sx:{mt:1},children:"Lorsque activé, les reçus seront automatiquement imprimés après chaque vente ou dépense"})]}),i.jsxs(ce,{item:!0,xs:12,md:6,children:[i.jsxs(je,{fullWidth:!0,children:[i.jsx(De,{children:"Taille du papier"}),i.jsxs(Ce,{value:C.taillePapier,label:"Taille du papier",onChange:e=>v({...C,taillePapier:e.target.value}),disabled:!k.canManageSettings,children:[i.jsx(J,{value:"thermal",children:"Reçu thermique (80mm)"}),i.jsx(J,{value:"a4",children:"A4"})]})]}),i.jsx(o,{variant:"body2",color:"text.secondary",sx:{mt:1},children:"Choisissez le format de papier pour l'impression des reçus"})]}),k.canManageSettings&&i.jsx(ce,{item:!0,xs:12,children:i.jsx(I,{variant:"contained",startIcon:i.jsx(pt,{}),onClick:async()=>{if(!e)return;const n={...e,impression:{impressionAutomatique:C.impressionAutomatique,taillePapier:C.taillePapier}};await ur.setSettings(n),t(n),l("Paramètres d'impression sauvegardés avec succès"),setTimeout(()=>l(""),3e3)},children:"Sauvegarder"})})]})})]})}),i.jsx(xa,{value:n,index:3,children:i.jsxs(ee,{children:[i.jsx(ue,{title:"Gestion des Catégories",avatar:i.jsx(tt,{}),action:k.canManageSettings&&i.jsx(I,{variant:"contained",startIcon:i.jsx(Ee,{}),onClick:()=>R(),children:"Nouvelle Catégorie"})}),i.jsx(te,{children:i.jsx(p,{children:S.map((n,a)=>i.jsxs(bt.Fragment,{children:[i.jsxs(x,{children:[i.jsx(r,{sx:{width:20,height:20,backgroundColor:n.couleur,borderRadius:1,mr:2}}),i.jsx(y,{primary:n.nom,secondary:n.description}),k.canManageSettings&&i.jsxs(V,{children:[i.jsx(U,{edge:"end",onClick:()=>R(n),sx:{mr:1},children:i.jsx(ke,{})}),i.jsx(U,{edge:"end",color:"error",onClick:()=>(async n=>{if(window.confirm(`Êtes-vous sûr de vouloir supprimer la catégorie "${n.nom}" ?`)){if((await ur.getProducts()).some(e=>e.categorie===n.nom))return u("Cette catégorie est utilisée par des produits et ne peut pas être supprimée"),void setTimeout(()=>u(""),5e3);const i=S.filter(e=>e.id!==n.id);if(b(i),e){const n={...e,categories:i};await ur.setSettings(n),t(n)}l("Catégorie supprimée avec succès"),setTimeout(()=>l(""),3e3)}})(n),children:i.jsx(q,{})})]})]}),a<S.length-1&&i.jsx(h,{})]},n.id))})})]})}),i.jsx(xa,{value:n,index:4,children:i.jsxs(ce,{container:!0,spacing:3,children:[i.jsx(ce,{item:!0,xs:12,md:6,children:i.jsxs(ee,{children:[i.jsx(ue,{title:"Sauvegarde des Données",avatar:i.jsx(Fe,{})}),i.jsxs(te,{children:[i.jsx(o,{variant:"body2",color:"text.secondary",gutterBottom:!0,children:"Exportez toutes vos données dans un fichier JSON pour créer une sauvegarde."}),i.jsx(I,{variant:"contained",startIcon:i.jsx(Fe,{}),onClick:async()=>{try{const{csvImportExportService:e}=await Si(async()=>{const{csvImportExportService:e}=await Promise.resolve().then(()=>ma);return{csvImportExportService:e}},void 0,import.meta.url),t=await e.exportAllData();if(t.success&&t.data){const e=new Blob([t.data],{type:"text/plain;charset=utf-8"}),n=URL.createObjectURL(e),i=document.createElement("a");i.href=n,i.download=`SmartBoutique_Backup_${(new Date).toISOString().split("T")[0]}.csv`,document.body.appendChild(i),i.click(),document.body.removeChild(i),URL.revokeObjectURL(n),l("Sauvegarde CSV exportée avec succès (compatible Excel)"),setTimeout(()=>l(""),3e3)}else u(t.message||"Erreur lors de l'exportation"),setTimeout(()=>u(""),3e3)}catch(e){u("Erreur lors de l'exportation des données"),setTimeout(()=>u(""),3e3)}},fullWidth:!0,sx:{mt:2},children:"Exporter les Données"})]})]})}),i.jsx(ce,{item:!0,xs:12,md:6,children:i.jsxs(ee,{children:[i.jsx(ue,{title:"Restauration des Données",avatar:i.jsx(Ue,{})}),i.jsxs(te,{children:[i.jsx(o,{variant:"body2",color:"text.secondary",gutterBottom:!0,children:"Importez un fichier de sauvegarde pour restaurer vos données."}),i.jsx("input",{accept:".json",style:{display:"none"},id:"import-file",type:"file",onChange:async e=>{var t;const n=null==(t=e.target.files)?void 0:t[0];if(!n)return;const i=new FileReader;i.onload=async e=>{var t;try{const n=JSON.parse(null==(t=e.target)?void 0:t.result);await ur.importData(n)?(A(),l("Données importées avec succès"),setTimeout(()=>l(""),3e3),setTimeout(()=>window.location.reload(),2e3)):(u("Erreur lors de l'importation des données"),setTimeout(()=>u(""),3e3))}catch(n){u("Fichier de sauvegarde invalide"),setTimeout(()=>u(""),3e3)}},i.readAsText(n),e.target.value=""}}),i.jsx("label",{htmlFor:"import-file",children:i.jsx(I,{variant:"outlined",component:"span",startIcon:i.jsx(Ue,{}),fullWidth:!0,sx:{mt:2},children:"Importer les Données"})})]})]})}),mr.hasRole(["super_admin"])&&i.jsx(ce,{item:!0,xs:12,children:i.jsxs(ee,{children:[i.jsx(ue,{title:"Réinitialisation",avatar:i.jsx(xt,{})}),i.jsxs(te,{children:[i.jsx(O,{severity:"warning",sx:{mb:2},children:i.jsxs(o,{variant:"body2",children:[i.jsx("strong",{children:"Attention:"})," Cette action supprimera toutes les données et restaurera les paramètres par défaut. Cette action est irréversible."]})}),i.jsx(I,{variant:"outlined",color:"error",startIcon:i.jsx(xt,{}),onClick:async()=>{if(!window.confirm("Êtes-vous sûr de vouloir réinitialiser toutes les données ? Cette action supprimera définitivement :"))return;if(window.confirm("ATTENTION: Cette action va supprimer TOUTES les données suivantes :\n\n• Tous les produits et inventaire\n• Toutes les ventes et transactions\n• Toutes les dettes et paiements\n• Tous les employés et leurs paiements\n• Toutes les dépenses\n• Tous les utilisateurs (sauf admin système)\n• Tous les fichiers CSV et données importées\n\nL'application sera nettoyée et prête pour vos propres données.\n\nCette action est IRRÉVERSIBLE. Confirmez-vous ?"))try{u(""),l("Réinitialisation en cours... Veuillez patienter.");const e=await ur.getCurrentUser(),t=(null==e?void 0:e.email)||"<EMAIL>";await B(),await L(),localStorage.setItem("smartboutique_data_reset","true");const n=await ur.getUsers(),i=n.find(e=>e.email===t)||n.find(e=>"super_admin"===e.role);i&&await ur.setCurrentUser(i),l("✅ Données réinitialisées avec succès ! Application prête pour vos données."),setTimeout(()=>{window.location.reload()},2e3)}catch(e){console.error("Erreur lors de la réinitialisation:",e),u("Erreur lors de la réinitialisation des données. Veuillez réessayer."),setTimeout(()=>u(""),5e3)}},children:"Réinitialiser toutes les Données"})]})]})})]})}),i.jsx(xa,{value:n,index:5,children:i.jsx(ha,{onSuccess:e=>{l(e),setTimeout(()=>l(""),3e3)},onError:e=>{u(e),setTimeout(()=>u(""),5e3)}})}),i.jsxs(Re,{open:f,onClose:N,maxWidth:"sm",fullWidth:!0,children:[i.jsx(Me,{children:E?"Modifier la Catégorie":"Nouvelle Catégorie"}),i.jsxs(Ie,{children:[d&&i.jsx(O,{severity:"error",sx:{mb:2},children:d}),i.jsxs(ce,{container:!0,spacing:2,sx:{mt:1},children:[i.jsx(ce,{item:!0,xs:12,children:i.jsx(ne,{fullWidth:!0,label:"Nom de la catégorie *",value:F.nom,onChange:e=>T({...F,nom:e.target.value})})}),i.jsx(ce,{item:!0,xs:12,children:i.jsx(ne,{fullWidth:!0,label:"Description",multiline:!0,rows:2,value:F.description,onChange:e=>T({...F,description:e.target.value})})}),i.jsx(ce,{item:!0,xs:12,children:i.jsx(ne,{fullWidth:!0,label:"Couleur",type:"color",value:F.couleur,onChange:e=>T({...F,couleur:e.target.value}),InputProps:{startAdornment:i.jsx(ie,{position:"start",children:i.jsx(gt,{})})}})}),i.jsx(ce,{item:!0,xs:12,children:i.jsxs(r,{display:"flex",alignItems:"center",gap:1,children:[i.jsx(o,{variant:"body2",children:"Aperçu:"}),i.jsx(M,{label:F.nom||"Nom de la catégorie",sx:{backgroundColor:F.couleur,color:"white"}})]})})]})]}),i.jsxs(Ne,{children:[i.jsx(I,{onClick:N,children:"Annuler"}),i.jsx(I,{onClick:async()=>{if(!F.nom.trim())return void u("Le nom de la catégorie est requis");if(S.some(e=>e.nom.toLowerCase()===F.nom.trim().toLowerCase()&&e.id!==(null==E?void 0:E.id)))return void u("Une catégorie avec ce nom existe déjà");let n;if(E)n=S.map(e=>e.id===E.id?{...e,nom:F.nom.trim(),description:F.description.trim(),couleur:F.couleur}:e);else{const e={id:Date.now().toString(),nom:F.nom.trim(),description:F.description.trim(),couleur:F.couleur};n=[...S,e]}if(b(n),e){const i={...e,categories:n};await ur.setSettings(i),t(i)}l(E?"Catégorie mise à jour":"Catégorie créée"),setTimeout(()=>l(""),3e3),N()},variant:"contained",children:E?"Mettre à jour":"Créer"})]})]})]}):i.jsx(o,{children:"Chargement..."})},ya=()=>{const[e,t]=vt.useState([]),[n,a]=vt.useState([]),[s,l]=vt.useState(""),[d,u]=vt.useState(""),[m,h]=vt.useState(""),[p,x]=vt.useState(0),[g,y]=vt.useState(10),[j,D]=vt.useState(!1),[C,v]=vt.useState(null),[S,b]=vt.useState({nomEmploye:"",montantCDF:0,datePaiement:Ft(new Date,"yyyy-MM-dd"),methodePaiement:"cash",notes:""}),[f,w]=vt.useState(""),[E,P]=vt.useState(""),[T,k]=vt.useState({tauxChangeUSDCDF:2800}),[A,R]=vt.useState(!1),[N,L]=vt.useState([]),[V,B]=vt.useState(!1),[_,z]=vt.useState(null),[$,W]=vt.useState({nomComplet:"",poste:"",salaireCDF:0,dateEmbauche:Ft(new Date,"yyyy-MM-dd"),telephone:"",adresse:"",statut:"actif",notes:""}),[X,Q]=vt.useState(0),H=mr.getUserPermissions(),Y=mr.getCurrentUser(),K=[{value:"cash",label:"Cash"},{value:"mobile_money",label:"Mobile Money"},{value:"bank",label:"Banque"}];vt.useEffect(()=>{Z(),re(),ae()},[]),vt.useEffect(()=>{se()},[e,s,d,m]);const Z=async()=>{try{R(!0);const e=await ur.getEmployeePayments();t(e)}catch(e){console.error("Error loading employee payments:",e),w("Erreur lors du chargement des paiements employés")}finally{R(!1)}},re=async()=>{try{const e=await ur.getEmployees();L(e)}catch(e){console.error("Error loading employees:",e),w("Erreur lors du chargement des employés")}},ae=async()=>{try{const e=await ur.getSettings();k(e)}catch(e){console.error("Error loading settings:",e)}},se=()=>{let t=[...e];if(s&&(t=t.filter(e=>{var t;return e.nomEmploye.toLowerCase().includes(s.toLowerCase())||(null==(t=e.notes)?void 0:t.toLowerCase().includes(s.toLowerCase()))})),d&&(t=t.filter(e=>e.methodePaiement===d)),m){const e=new Date;let n,i=e;switch(m){case"today":n=new Date(e.getFullYear(),e.getMonth(),e.getDate());break;case"week":n=new Date(e.getTime()-6048e5);break;case"month":n=Mt(e),i=It(e);break;case"year":n=new Date(e.getFullYear(),0,1);break;default:n=new Date(0)}t=t.filter(e=>{const t=new Date(e.datePaiement);return Lt(t,{start:n,end:i})})}a(t),x(0)},oe=e=>{e?(v(e),b({nomEmploye:e.nomEmploye,montantCDF:e.montantCDF,datePaiement:e.datePaiement,methodePaiement:e.methodePaiement,notes:e.notes||""})):(v(null),b({nomEmploye:"",montantCDF:0,datePaiement:Ft(new Date,"yyyy-MM-dd"),methodePaiement:"cash",notes:""})),D(!0),w(""),P("")},le=()=>{D(!1),v(null),w(""),P("")},de=e=>{const t=K.find(t=>t.value===e);return t?t.label:e},ue=e=>{e?(z(e),W({nomComplet:e.nomComplet,poste:e.poste,salaireCDF:e.salaireCDF,dateEmbauche:e.dateEmbauche,telephone:e.telephone||"",adresse:e.adresse||"",statut:e.statut,notes:e.notes||""})):(z(null),W({nomComplet:"",poste:"",salaireCDF:0,dateEmbauche:Ft(new Date,"yyyy-MM-dd"),telephone:"",adresse:"",statut:"actif",notes:""})),B(!0),w(""),P("")},ve=()=>{B(!1),z(null),w(""),P("")},Se=n.reduce((e,t)=>e+t.montantCDF,0);return n.reduce((e,t)=>e+(t.montantUSD||0),0),i.jsxs(r,{p:3,children:[i.jsxs(o,{variant:"h4",gutterBottom:!0,children:[i.jsx(G,{sx:{mr:1,verticalAlign:"middle"}}),"Gestion des Employés et Paiements"]}),i.jsx(c,{sx:{mb:3},children:i.jsxs(it,{value:X,onChange:(e,t)=>Q(t),children:[i.jsx(rt,{label:"Paiements Employés"}),i.jsx(rt,{label:"Gestion des Employés"})]})}),0===X&&i.jsxs(i.Fragment,{children:[i.jsxs(ce,{container:!0,spacing:3,sx:{mb:3},children:[i.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:i.jsx(ee,{children:i.jsxs(te,{children:[i.jsx(o,{color:"textSecondary",gutterBottom:!0,children:"Total Paiements"}),i.jsx(o,{variant:"h5",children:n.length})]})})}),i.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:i.jsx(ee,{children:i.jsxs(te,{children:[i.jsx(o,{color:"textSecondary",gutterBottom:!0,children:"Montant Total"}),i.jsxs(o,{variant:"h5",fontWeight:"medium",children:[Br(Se,T.tauxChangeUSDCDF).primaryAmount," ",Br(Se,T.tauxChangeUSDCDF).primaryCurrency]}),i.jsxs(o,{variant:"body2",color:"text.secondary",children:["≈ $",Br(Se,T.tauxChangeUSDCDF).secondaryAmount]})]})})}),i.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:i.jsx(ee,{children:i.jsxs(te,{children:[i.jsx(o,{color:"textSecondary",gutterBottom:!0,children:"Employés Payés"}),i.jsx(o,{variant:"h5",children:new Set(n.map(e=>e.nomEmploye)).size})]})})})]}),f&&i.jsx(O,{severity:"error",sx:{mb:2},onClose:()=>w(""),children:f}),E&&i.jsx(O,{severity:"success",sx:{mb:2},onClose:()=>P(""),children:E}),i.jsx(c,{sx:{p:2,mb:3},children:i.jsxs(ce,{container:!0,spacing:2,alignItems:"center",children:[i.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:i.jsx(ne,{fullWidth:!0,label:"Rechercher employé",value:s,onChange:e=>l(e.target.value),InputProps:{startAdornment:i.jsx(ie,{position:"start",children:i.jsx(Pe,{})})}})}),i.jsx(ce,{item:!0,xs:12,sm:6,md:2,children:i.jsxs(je,{fullWidth:!0,children:[i.jsx(De,{children:"Méthode de paiement"}),i.jsxs(Ce,{value:d,onChange:e=>u(e.target.value),label:"Méthode de paiement",children:[i.jsx(J,{value:"",children:"Toutes"}),K.map(e=>i.jsx(J,{value:e.value,children:e.label},e.value))]})]})}),i.jsx(ce,{item:!0,xs:12,sm:6,md:2,children:i.jsxs(je,{fullWidth:!0,children:[i.jsx(De,{children:"Période"}),i.jsx(Ce,{value:m,onChange:e=>h(e.target.value),label:"Période",children:[{value:"",label:"Toutes les dates"},{value:"today",label:"Aujourd'hui"},{value:"week",label:"Cette semaine"},{value:"month",label:"Ce mois"},{value:"year",label:"Cette année"}].map(e=>i.jsx(J,{value:e.value,children:e.label},e.value))})]})}),i.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:i.jsx(I,{variant:"contained",startIcon:i.jsx(Ee,{}),onClick:()=>oe(),disabled:!H.canManageEmployeePayments||A,fullWidth:!0,children:"Nouveau Paiement"})}),i.jsx(ce,{item:!0,xs:12,sm:6,md:2,children:i.jsx(I,{variant:"outlined",onClick:()=>{l(""),u(""),h("")},fullWidth:!0,children:"Réinitialiser"})})]})}),i.jsxs(c,{children:[i.jsx(me,{children:i.jsxs(he,{children:[i.jsx(pe,{children:i.jsxs(xe,{children:[i.jsx(ge,{children:"Employé"}),i.jsx(ge,{children:"Montant (CDF)"}),i.jsx(ge,{children:"Montant (USD)"}),i.jsx(ge,{children:"Date de Paiement"}),i.jsx(ge,{children:"Méthode"}),i.jsx(ge,{children:"Notes"}),i.jsx(ge,{children:"Créé par"}),H.canManageEmployeePayments&&i.jsx(ge,{children:"Actions"})]})}),i.jsx(ye,{children:A?i.jsx(xe,{children:i.jsx(ge,{colSpan:8,align:"center",children:i.jsx(Ve,{})})}):0===n.length?i.jsx(xe,{children:i.jsx(ge,{colSpan:8,align:"center",children:i.jsx(o,{variant:"body2",color:"textSecondary",children:"Aucun paiement employé trouvé"})})}):n.slice(p*g,p*g+g).map(e=>i.jsxs(xe,{children:[i.jsx(ge,{children:i.jsxs(r,{display:"flex",alignItems:"center",children:[i.jsx(ot,{sx:{mr:1,color:"primary.main"}}),e.nomEmploye]})}),i.jsx(ge,{children:i.jsx(o,{variant:"body2",fontWeight:"bold",color:"primary",children:qr(e.montantCDF,"CDF")})}),i.jsx(ge,{children:i.jsx(o,{variant:"body2",color:"textSecondary",children:qr(e.montantUSD||0,"USD")})}),i.jsx(ge,{children:i.jsxs(r,{display:"flex",alignItems:"center",children:[i.jsx(nt,{sx:{mr:1,fontSize:16,color:"text.secondary"}}),Ft(new Date(e.datePaiement),"dd/MM/yyyy",{locale:jr})]})}),i.jsx(ge,{children:i.jsx(M,{label:de(e.methodePaiement),size:"small",color:"cash"===e.methodePaiement?"success":"mobile_money"===e.methodePaiement?"info":"default"})}),i.jsx(ge,{children:i.jsx(o,{variant:"body2",noWrap:!0,sx:{maxWidth:150},children:e.notes||"-"})}),i.jsx(ge,{children:i.jsx(o,{variant:"body2",color:"textSecondary",children:e.creePar})}),H.canManageEmployeePayments&&i.jsxs(ge,{children:[i.jsx(F,{title:"Modifier",children:i.jsx(U,{size:"small",onClick:()=>oe(e),disabled:A,children:i.jsx(ke,{})})}),i.jsx(F,{title:"Supprimer",children:i.jsx(U,{size:"small",onClick:()=>(async e=>{if(window.confirm(`Êtes-vous sûr de vouloir supprimer le paiement de ${e.nomEmploye} ?`))try{R(!0),await ur.deleteEmployeePayment(e.id),P("Paiement employé supprimé avec succès"),await Z()}catch(t){console.error("Error deleting employee payment:",t),w("Erreur lors de la suppression du paiement employé")}finally{R(!1)}})(e),disabled:A,color:"error",children:i.jsx(q,{})})})]})]},e.id))})]})}),i.jsx(Ae,{rowsPerPageOptions:[5,10,25,50,100],component:"div",count:n.length,rowsPerPage:g,page:p,onPageChange:(e,t)=>{x(t)},onRowsPerPageChange:e=>{y(parseInt(e.target.value,10)),x(0)},labelRowsPerPage:"Lignes par page:",labelDisplayedRows:({from:e,to:t,count:n})=>`${e}-${t} sur ${-1!==n?n:`plus de ${t}`}`})]}),i.jsxs(Re,{open:j,onClose:le,maxWidth:"sm",fullWidth:!0,children:[i.jsx(Me,{children:C?"Modifier le Paiement Employé":"Nouveau Paiement Employé"}),i.jsx(Ie,{children:i.jsx(r,{sx:{pt:1},children:i.jsxs(ce,{container:!0,spacing:2,children:[i.jsx(ce,{item:!0,xs:12,children:i.jsxs(je,{fullWidth:!0,disabled:A,children:[i.jsx(De,{id:"employee-select-label",children:"Nom de l'employé *"}),i.jsxs(Ce,{labelId:"employee-select-label",value:S.nomEmploye,onChange:e=>b({...S,nomEmploye:e.target.value}),label:"Nom de l'employé *",startAdornment:i.jsx(ie,{position:"start",children:i.jsx(ot,{})}),children:[i.jsx(J,{value:"",disabled:!0,children:i.jsx("em",{children:"Sélectionner un employé"})}),N.filter(e=>"actif"===e.statut).sort((e,t)=>e.nomComplet.localeCompare(t.nomComplet,"fr",{sensitivity:"base"})).map(e=>i.jsx(J,{value:e.nomComplet,children:i.jsxs(r,{sx:{display:"flex",flexDirection:"column",alignItems:"flex-start"},children:[i.jsx(o,{variant:"body1",sx:{fontWeight:500},children:e.nomComplet}),i.jsx(o,{variant:"caption",color:"text.secondary",children:e.poste})]})},e.id)),0===N.filter(e=>"actif"===e.statut).length&&i.jsx(J,{value:"",disabled:!0,children:i.jsx("em",{children:"Aucun employé actif disponible"})})]})]})}),i.jsx(ce,{item:!0,xs:12,children:i.jsx(Gr,{label:"Montant du paiement *",value:S.montantCDF,onChange:e=>b({...S,montantCDF:e}),currency:"CDF",disabled:A,fullWidth:!0})}),i.jsx(ce,{item:!0,xs:12,sm:6,children:i.jsx(ne,{fullWidth:!0,type:"date",label:"Date de paiement *",value:S.datePaiement,onChange:e=>b({...S,datePaiement:e.target.value}),disabled:A,InputLabelProps:{shrink:!0},InputProps:{startAdornment:i.jsx(ie,{position:"start",children:i.jsx(nt,{})})}})}),i.jsx(ce,{item:!0,xs:12,sm:6,children:i.jsxs(je,{fullWidth:!0,children:[i.jsx(De,{children:"Méthode de paiement *"}),i.jsx(Ce,{value:S.methodePaiement,onChange:e=>b({...S,methodePaiement:e.target.value}),label:"Méthode de paiement *",disabled:A,children:K.map(e=>i.jsx(J,{value:e.value,children:e.label},e.value))})]})}),i.jsx(ce,{item:!0,xs:12,children:i.jsx(ne,{fullWidth:!0,multiline:!0,rows:3,label:"Notes (optionnel)",value:S.notes,onChange:e=>b({...S,notes:e.target.value}),disabled:A,placeholder:"Ajoutez des notes sur ce paiement..."})}),S.montantCDF>0&&i.jsx(ce,{item:!0,xs:12,children:i.jsxs(r,{sx:{p:2,bgcolor:"grey.50",borderRadius:1},children:[i.jsxs(o,{variant:"body2",color:"textSecondary",gutterBottom:!0,children:["Équivalent USD (taux: ",T.tauxChangeUSDCDF," CDF/USD)"]}),i.jsx(o,{variant:"h6",color:"primary",children:qr(S.montantCDF/T.tauxChangeUSDCDF,"USD")})]})})]})})}),i.jsxs(Ne,{children:[i.jsx(I,{onClick:le,disabled:A,children:"Annuler"}),i.jsx(I,{onClick:async()=>{var e;try{if(R(!0),w(""),!S.nomEmploye.trim())return void w("Veuillez sélectionner un employé");if(!N.find(e=>e.nomComplet===S.nomEmploye&&"actif"===e.statut))return void w("L'employé sélectionné n'est pas valide ou n'est plus actif");const t=Rr(S.montantCDF,"Le montant du paiement",{allowZero:!1,allowNegative:!1});if(!t.isValid)return void w(t.errors[0]||Ir);const n={id:(null==C?void 0:C.id)||`emp_pay_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,nomEmploye:S.nomEmploye.trim(),montantCDF:S.montantCDF,montantUSD:S.montantCDF/T.tauxChangeUSDCDF,datePaiement:S.datePaiement,methodePaiement:S.methodePaiement,notes:null==(e=S.notes)?void 0:e.trim(),creePar:(null==Y?void 0:Y.nom)||"Système",dateCreation:(null==C?void 0:C.dateCreation)||(new Date).toISOString(),dateModification:C?(new Date).toISOString():void 0};C?(await ur.updateEmployeePayment(n),P("Paiement employé modifié avec succès")):(await ur.addEmployeePayment(n),P("Paiement employé ajouté avec succès")),await Z(),le()}catch(t){console.error("Error saving employee payment:",t),w("Erreur lors de la sauvegarde du paiement employé")}finally{R(!1)}},variant:"contained",disabled:A||!S.nomEmploye.trim()||S.montantCDF<=0||!N.find(e=>e.nomComplet===S.nomEmploye&&"actif"===e.statut),startIcon:A?i.jsx(Ve,{size:20}):i.jsx(G,{}),children:C?"Modifier":"Ajouter"})]})]})]}),1===X&&i.jsxs(i.Fragment,{children:[i.jsxs(ce,{container:!0,spacing:3,sx:{mb:3},children:[i.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:i.jsx(ee,{children:i.jsxs(te,{children:[i.jsx(o,{color:"textSecondary",gutterBottom:!0,children:"Total Employés"}),i.jsx(o,{variant:"h5",children:N.length})]})})}),i.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:i.jsx(ee,{children:i.jsxs(te,{children:[i.jsx(o,{color:"textSecondary",gutterBottom:!0,children:"Employés Actifs"}),i.jsx(o,{variant:"h5",children:N.filter(e=>"actif"===e.statut).length})]})})}),i.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:i.jsx(ee,{children:i.jsxs(te,{children:[i.jsx(o,{color:"textSecondary",gutterBottom:!0,children:"Masse Salariale (CDF)"}),i.jsx(o,{variant:"h5",children:qr(N.filter(e=>"actif"===e.statut).reduce((e,t)=>e+t.salaireCDF,0),"CDF")})]})})})]}),i.jsx(c,{sx:{p:2,mb:3},children:i.jsxs(r,{display:"flex",justifyContent:"space-between",alignItems:"center",children:[i.jsx(o,{variant:"h6",children:"Liste des Employés"}),H.canManageEmployeePayments&&i.jsx(I,{variant:"contained",startIcon:i.jsx(Ee,{}),onClick:()=>ue(),children:"Ajouter Employé"})]})}),i.jsx(c,{children:i.jsx(me,{children:i.jsxs(he,{children:[i.jsx(pe,{children:i.jsxs(xe,{children:[i.jsx(ge,{children:"Nom"}),i.jsx(ge,{children:"Poste"}),i.jsx(ge,{children:"Salaire (CDF)"}),i.jsx(ge,{children:"Date d'Embauche"}),i.jsx(ge,{children:"Statut"}),i.jsx(ge,{children:"Téléphone"}),H.canManageEmployeePayments&&i.jsx(ge,{children:"Actions"})]})}),i.jsx(ye,{children:0===N.length?i.jsx(xe,{children:i.jsx(ge,{colSpan:7,align:"center",children:i.jsx(o,{variant:"body2",color:"textSecondary",children:"Aucun employé trouvé"})})}):N.map(e=>i.jsxs(xe,{children:[i.jsx(ge,{children:i.jsxs(r,{display:"flex",alignItems:"center",children:[i.jsx(ot,{sx:{mr:1,color:"primary.main"}}),e.nomComplet," "]})}),i.jsx(ge,{children:e.poste}),i.jsx(ge,{children:i.jsx(o,{variant:"body2",fontWeight:"bold",color:"primary",children:qr(e.salaireCDF,"CDF")})}),i.jsx(ge,{children:Ft(new Date(e.dateEmbauche),"dd/MM/yyyy",{locale:jr})}),i.jsx(ge,{children:i.jsx(M,{label:e.statut,color:"actif"===e.statut?"success":"default",size:"small"})}),i.jsx(ge,{children:e.telephone||"-"}),H.canManageEmployeePayments&&i.jsx(ge,{children:i.jsxs(r,{display:"flex",gap:1,children:[i.jsx(F,{title:"Modifier",children:i.jsx(U,{size:"small",onClick:()=>ue(e),children:i.jsx(ke,{fontSize:"small"})})}),i.jsx(F,{title:"Supprimer",children:i.jsx(U,{size:"small",color:"error",onClick:()=>(async e=>{if(window.confirm(`Êtes-vous sûr de vouloir supprimer l'employé ${e.nomComplet} ?`))try{R(!0),await ur.deleteEmployee(e.id),P("Employé supprimé avec succès"),await re()}catch(t){console.error("Error deleting employee:",t),w("Erreur lors de la suppression de l'employé")}finally{R(!1)}})(e),children:i.jsx(q,{fontSize:"small"})})})]})})]},e.id))})]})})}),i.jsxs(Re,{open:V,onClose:ve,maxWidth:"md",fullWidth:!0,children:[i.jsx(Me,{children:_?"Modifier Employé":"Ajouter Employé"}),i.jsxs(Ie,{children:[f&&i.jsx(O,{severity:"error",sx:{mb:2},children:f}),E&&i.jsx(O,{severity:"success",sx:{mb:2},children:E}),i.jsxs(ce,{container:!0,spacing:2,sx:{mt:1},children:[i.jsx(ce,{item:!0,xs:12,md:6,children:i.jsx(ne,{fullWidth:!0,label:"Nom Complet *",value:$.nomComplet,onChange:e=>W({...$,nomComplet:e.target.value}),placeholder:"Ex: Jean Dupont"})}),i.jsx(ce,{item:!0,xs:12,md:6,children:i.jsx(ne,{fullWidth:!0,label:"Poste *",value:$.poste,onChange:e=>W({...$,poste:e.target.value})})}),i.jsx(ce,{item:!0,xs:12,md:6,children:i.jsx(Gr,{label:"Salaire",value:$.salaireCDF,onChange:e=>W({...$,salaireCDF:e}),min:0,step:1e3,exchangeRate:T.tauxChangeUSDCDF,required:!0,allowUSDInput:!0})}),i.jsx(ce,{item:!0,xs:12,md:6,children:i.jsx(ne,{fullWidth:!0,label:"Date d'Embauche",type:"date",value:$.dateEmbauche,onChange:e=>W({...$,dateEmbauche:e.target.value}),InputLabelProps:{shrink:!0}})}),i.jsx(ce,{item:!0,xs:12,md:6,children:i.jsxs(je,{fullWidth:!0,children:[i.jsx(De,{children:"Statut"}),i.jsxs(Ce,{value:$.statut,label:"Statut",onChange:e=>W({...$,statut:e.target.value}),children:[i.jsx(J,{value:"actif",children:"Actif"}),i.jsx(J,{value:"inactif",children:"Inactif"}),i.jsx(J,{value:"suspendu",children:"Suspendu"})]})]})}),i.jsx(ce,{item:!0,xs:12,md:6,children:i.jsx(ne,{fullWidth:!0,label:"Téléphone",value:$.telephone,onChange:e=>W({...$,telephone:e.target.value})})}),i.jsx(ce,{item:!0,xs:12,md:6,children:i.jsx(ne,{fullWidth:!0,label:"Adresse",value:$.adresse,onChange:e=>W({...$,adresse:e.target.value})})}),i.jsx(ce,{item:!0,xs:12,children:i.jsx(ne,{fullWidth:!0,label:"Notes",multiline:!0,rows:3,value:$.notes,onChange:e=>W({...$,notes:e.target.value})})})]})]}),i.jsxs(Ne,{children:[i.jsx(I,{onClick:ve,children:"Annuler"}),i.jsx(I,{onClick:async()=>{var e,t,n;if($.nomComplet.trim()&&$.poste.trim())try{R(!0);const i=(new Date).toISOString(),r={id:(null==_?void 0:_.id)||`emp_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,nomComplet:$.nomComplet.trim(),poste:$.poste.trim(),salaireCDF:$.salaireCDF,salaireUSD:$.salaireCDF/T.tauxChangeUSDCDF,dateEmbauche:$.dateEmbauche,telephone:null==(e=$.telephone)?void 0:e.trim(),adresse:null==(t=$.adresse)?void 0:t.trim(),statut:$.statut,notes:null==(n=$.notes)?void 0:n.trim(),creePar:(null==Y?void 0:Y.nom)||"Système",dateCreation:(null==_?void 0:_.dateCreation)||i,dateModification:_?i:void 0};_?(await ur.updateEmployee(r),P("Employé modifié avec succès")):(await ur.addEmployee(r),P("Employé ajouté avec succès")),await re(),ve()}catch(i){console.error("Error saving employee:",i),w("Erreur lors de la sauvegarde de l'employé")}finally{R(!1)}else w("Le nom complet et le poste sont requis")},variant:"contained",disabled:A||!$.nomComplet.trim()||!$.poste.trim(),startIcon:A?i.jsx(Ve,{size:20}):i.jsx(ot,{}),children:_?"Modifier":"Ajouter"})]})]})]})]})},ja=()=>{var e,t,n,i,r,a,s,o,l;const c=(()=>{const e=Ii();return{components:{MuiButton:{styleOverrides:{root:{minHeight:e.isMobile?48:36,fontSize:e.isMobile?"1rem":"0.875rem"}}},MuiIconButton:{styleOverrides:{root:{padding:e.isMobile?12:8}}},MuiTableCell:{styleOverrides:{root:{padding:e.isMobile?"12px 8px":"16px"}}}}}})();return Dt({palette:{primary:{main:"#1976d2"},secondary:{main:"#dc004e"},background:{default:"#f5f5f5"}},typography:{fontFamily:'"Roboto", "Helvetica", "Arial", sans-serif',h4:{fontWeight:600},h5:{fontWeight:600},h6:{fontWeight:600}},components:{MuiButton:{styleOverrides:{root:{textTransform:"none",...null==(n=null==(t=null==(e=c.components)?void 0:e.MuiButton)?void 0:t.styleOverrides)?void 0:n.root}}},MuiCard:{styleOverrides:{root:{boxShadow:"0 2px 8px rgba(0,0,0,0.1)"}}},MuiIconButton:{styleOverrides:{root:{...null==(a=null==(r=null==(i=c.components)?void 0:i.MuiIconButton)?void 0:r.styleOverrides)?void 0:a.root}}},MuiTableCell:{styleOverrides:{root:{...null==(l=null==(o=null==(s=c.components)?void 0:s.MuiTableCell)?void 0:o.styleOverrides)?void 0:l.root}}},MuiCssBaseline:{styleOverrides:{"input[type=number]":{MozAppearance:"textfield","&::-webkit-outer-spin-button":{WebkitAppearance:"none",margin:0},"&::-webkit-inner-spin-button":{WebkitAppearance:"none",margin:0}},'input[type="number"]':{MozAppearance:"textfield","&::-webkit-outer-spin-button":{WebkitAppearance:"none",margin:0},"&::-webkit-inner-spin-button":{WebkitAppearance:"none",margin:0}}}}}},Ci)},Da=()=>{const[e,t]=vt.useState(null),[n,a]=vt.useState(!0),[s,o]=vt.useState(null),[l,c]=vt.useState(()=>ja());vt.useEffect(()=>{const e=()=>{document.querySelectorAll("input, textarea, [contenteditable]").forEach(e=>{const t=e;t.style.pointerEvents="auto",t.style.userSelect="text",(!t.hasAttribute("tabindex")||t.tabIndex<0)&&(t.tabIndex=0)})};e();const t=()=>{setTimeout(e,50)};return window.addEventListener("hashchange",t),()=>{window.removeEventListener("hashchange",t)}},[]),vt.useEffect(()=>{(async()=>{try{0;if("true"===localStorage.getItem("smartboutique_data_reset")){if(0===(await ur.getUsers()).length){const e=[{id:"1",nom:"Super Admin",email:"<EMAIL>",role:"super_admin",motDePasse:"admin123",dateCreation:(new Date).toISOString(),actif:!0},{id:"2",nom:"Gestionnaire",email:"<EMAIL>",role:"admin",motDePasse:"manager123",dateCreation:(new Date).toISOString(),actif:!0},{id:"3",nom:"Employé",email:"<EMAIL>",role:"employee",motDePasse:"employee123",dateCreation:(new Date).toISOString(),actif:!0}];await ur.setUsers(e)}0}else await ur.initializeDefaultData();Ii().isMobile&&await ur.migrateFromDesktop(),await mr.initialize();const e=mr.getCurrentUser();t(e),a(!1)}catch(e){console.error("SmartBoutique: Error during initialization:",e),o(e instanceof Error?e.message:"Unknown error"),a(!1)}})()},[]);return s?i.jsxs(yt,{theme:l,children:[i.jsx(jt,{}),i.jsxs(r,{display:"flex",flexDirection:"column",justifyContent:"center",alignItems:"center",minHeight:"100vh",p:3,children:[i.jsx("h2",{children:"Erreur de chargement"}),i.jsx("p",{children:"Une erreur s'est produite lors du chargement de l'application:"}),i.jsx("pre",{style:{background:"#f5f5f5",padding:"10px",borderRadius:"4px"},children:s}),i.jsx("button",{onClick:()=>window.location.reload(),children:"Recharger l'application"})]})]}):n?i.jsxs(yt,{theme:l,children:[i.jsx(jt,{}),i.jsx(r,{display:"flex",justifyContent:"center",alignItems:"center",minHeight:"100vh",children:"Chargement..."})]}):i.jsxs(yt,{theme:l,children:[i.jsx(jt,{}),i.jsx(xi,{children:i.jsxs(mi,{children:[i.jsx(di,{path:"/login",element:e?i.jsx(li,{to:"/dashboard",replace:!0}):i.jsx(Er,{onLogin:e=>{t(e)}})}),i.jsxs(di,{path:"/",element:i.jsx(fr,{children:i.jsx(br,{currentUser:e,onLogout:async()=>{await mr.logout(),t(null)}})}),children:[i.jsx(di,{index:!0,element:i.jsx(li,{to:"/dashboard",replace:!0})}),i.jsx(di,{path:"dashboard",element:i.jsx(fr,{requiredPermission:"canViewDashboard",children:i.jsx(Hr,{})})}),i.jsx(di,{path:"products",element:i.jsx(fr,{requiredPermission:"canViewProducts",children:i.jsx(Kr,{})})}),i.jsx(di,{path:"sales",element:i.jsx(fr,{requiredPermission:"canViewSales",children:i.jsx(ia,{})})}),i.jsx(di,{path:"debts",element:i.jsx(fr,{requiredPermission:"canViewDebts",children:i.jsx(ra,{})})}),i.jsx(di,{path:"expenses",element:i.jsx(fr,{requiredPermission:"canViewExpenses",children:i.jsx(sa,{})})}),i.jsx(di,{path:"employee-payments",element:i.jsx(fr,{requiredPermission:"canViewEmployeePayments",children:i.jsx(ya,{})})}),i.jsx(di,{path:"reports",element:i.jsx(fr,{requiredPermission:"canViewReports",children:i.jsx(la,{})})}),i.jsx(di,{path:"users",element:i.jsx(fr,{requiredPermission:"canViewUsers",children:i.jsx(ca,{})})}),i.jsx(di,{path:"settings",element:i.jsx(fr,{requiredPermission:"canViewSettings",children:i.jsx(ga,{})})})]}),i.jsx(di,{path:"*",element:i.jsx(li,{to:"/dashboard",replace:!0})})]})})]})};class Ca extends vt.Component{constructor(e){super(e),n(this,"handleReload",()=>{this.setState({hasError:!1,error:void 0,errorInfo:void 0}),window.location.reload()}),n(this,"handleReset",()=>{this.setState({hasError:!1,error:void 0,errorInfo:void 0})}),this.state={hasError:!1}}static getDerivedStateFromError(e){return{hasError:!0,error:e}}componentDidCatch(e,t){console.error("ErrorBoundary caught an error:",e,t),this.setState({error:e,errorInfo:t})}render(){return this.state.hasError?i.jsx(r,{display:"flex",flexDirection:"column",justifyContent:"center",alignItems:"center",minHeight:"100vh",p:3,bgcolor:"#f5f5f5",children:i.jsxs(c,{elevation:3,sx:{p:4,maxWidth:600,width:"100%"},children:[i.jsx(o,{variant:"h4",color:"error",gutterBottom:!0,children:"Oops! Une erreur s'est produite"}),i.jsx(o,{variant:"body1",paragraph:!0,children:"L'application a rencontré une erreur inattendue. Vous pouvez essayer de continuer ou recharger l'application."}),!1,i.jsxs(r,{display:"flex",gap:2,mt:3,children:[i.jsx(I,{variant:"contained",color:"primary",onClick:this.handleReset,children:"Continuer"}),i.jsx(I,{variant:"outlined",color:"secondary",onClick:this.handleReload,children:"Recharger l'application"})]})]})}):this.props.children}}Wt.register(Xt,Qt,Jt,Ht,Gt,Yt,Kt,Zt,en);const va=document.getElementById("root");va?rn.createRoot(va).render(i.jsx(bt.StrictMode,{children:i.jsx(Ca,{children:i.jsx(Da,{})})})):console.error("SmartBoutique: Root element not found!");export{Ui as W};
